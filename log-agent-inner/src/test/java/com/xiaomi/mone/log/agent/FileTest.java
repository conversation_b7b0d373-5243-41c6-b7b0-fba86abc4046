package com.xiaomi.mone.log.agent;

import cn.hutool.core.io.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 *
 * @description
 * @version 1.0
 * <AUTHOR>
 * @date 2024/3/26 14:08
 *
 */
@Slf4j
public class FileTest {

    @Test
    public void writeFileTest() {
        // 获取当前时间
        LocalDateTime currentTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");

        String filePrefix = "/home/<USER>/log/";

        // 创建文件并写入数据
        String fileName = filePrefix + "file" + ".log";
        writeToFile(fileName, "Data written at: " + currentTime);

//        try {
//            // 等待10分钟
//            Thread.sleep(2 * 60 * 1000);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//
//        // 获取新的当前时间
//        LocalDateTime newTime = LocalDateTime.now();
//
//        // 新文件名
//        String newFileName = filePrefix + "file" + ".log." + newTime.format(formatter);
//
//        // 重命名文件
//        renameFile(fileName, newFileName);
//
//        // 继续向重命名后的文件写入数据
//        writeToFile(newFileName, "Data written at: " + newTime);
//
//        try {
//            // 等待10分钟
//            Thread.sleep(2 * 60 * 1000);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//        writeToFile(newFileName, "Data written at: " + newTime);
    }

    // 写入文件方法
    private static void writeToFile(String fileName, String data) {
        try (FileWriter writer = new FileWriter(fileName)) {
//            writer.write(data);
            System.out.println("Data written to " + fileName);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    // 重命名文件方法
    private static void renameFile(String oldFileName, String newFileName) {
        File oldFile = new File(oldFileName);
        File newFile = new File(newFileName);

        if (oldFile.exists()) {
            if (oldFile.renameTo(newFile)) {
                System.out.println("File renamed successfully.");
            } else {
                System.out.println("Failed to rename file.");
            }
        } else {
            System.out.println("File does not exist.");
        }
    }


    @Test
    public void queryFileTest(){
//        List<File> files = FileUtil.glob(pattern);
    }
}
