<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xiaomi.mone</groupId>
        <artifactId>hera-log-inner</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>log-agent-inner</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.4.11</version>
        </dependency>

        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
            <version>3.0.6</version>
        </dependency>

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>docean</artifactId>
            <version>1.4-java20-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>docean-plugin-config</artifactId>
            <version>1.4-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>hera-tspandata</artifactId>
            <version>2.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.apache.ozhera</groupId>
            <artifactId>log-common</artifactId>
            <version>2.2.6-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>file</artifactId>
            <version>1.6.1-jdk21-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.apache.ozhera</groupId>
            <artifactId>log-agent</artifactId>
            <version>2.2.6-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.rocketmq</groupId>
                    <artifactId>rocketmq-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>run.mone</groupId>
                    <artifactId>dubbo</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>lombok</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.rocketmq</groupId>
                    <artifactId>rocketmq-acl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.ozhera</groupId>
                    <artifactId>log-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>run.mone</groupId>
                    <artifactId>file</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>log-common-inner</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>lombok</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.21</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.infra</groupId>
            <artifactId>rocketmq-client-java</artifactId>
            <version>1.0.1-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.infra.galaxy</groupId>
            <artifactId>galaxy-talos-sdk</artifactId>
            <version>2.7.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- log appender -->
        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>log</artifactId>
            <version>1.6-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.xiaomi.infra.galaxy</groupId>
                    <artifactId>galaxy-talos-sdk</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.code.gson</groupId>
                    <artifactId>gson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
            <version>2.7.12-mone-v14-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>netty</artifactId>
                    <groupId>org.jboss.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.code.gson</groupId>
                    <artifactId>gson</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>dubbo-trace</artifactId>
            <version>2.7.12-mone-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.dubbo</groupId>
                    <artifactId>dubbo</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>lombok</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>


    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>config/*.properties</exclude>
                </excludes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <compilerVersion>21</compilerVersion>
                    <source>21</source>
                    <target>21</target>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.5.1</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <finalName>${project.artifactId}-${project.version}</finalName>
                            <shadedArtifactAttached>false</shadedArtifactAttached>
                            <filters>
                                <filter>
                                    <artifact>*:run.mone:log-agent</artifact>
                                    <includes>
                                        <include>**/*.class</include>
                                        <include>**/*.properties</include>
                                        <include>**/*.xml</include>
                                    </includes>
                                    <excludes>
                                        <exclude>**/config.properties</exclude>
                                    </excludes>
                                </filter>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                            <transformers>
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <mainClass>com.xiaomi.mone.log.agent.bootstrap.InnerMiLogAgentBootstrap</mainClass>
                                </transformer>
                            </transformers>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>

    </build>

    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <profiles.active>local</profiles.active>
                <talos.access.secret>ROzisDiJIX9uqWUqJe3Q2XpJr9eZRr/14OIao36V</talos.access.secret>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <filters>
                    <filter>src/main/resources/config/local.properties</filter>
                </filters>
            </build>
        </profile>

        <profile>
            <id>staging</id>
            <properties>
                <profiles.active>staging</profiles.active>
                <talos.access.secret>ROzisDiJIX9uqWUqJe3Q2XpJr9eZRr/14OIao36V</talos.access.secret>
            </properties>
            <build>
                <filters>
                    <filter>src/main/resources/config/staging.properties</filter>
                </filters>
            </build>
        </profile>

        <profile>
            <id>intranet</id>
            <properties>
                <profiles.active>intranet</profiles.active>
                <talos.access.secret>ELB+d5waJyXyZmnc1v9Vpon2S1FmdelyMAOQOVTt</talos.access.secret>
            </properties>
            <build>
                <filters>
                    <filter>src/main/resources/config/intranet.properties</filter>
                </filters>
            </build>
        </profile>

        <profile>
            <id>physics</id>
            <properties>
                <profiles.active>physics</profiles.active>
                <talos.access.secret>ELB+d5waJyXyZmnc1v9Vpon2S1FmdelyMAOQOVTt</talos.access.secret>
            </properties>
            <build>
                <filters>
                    <filter>src/main/resources/config/physics.properties</filter>
                </filters>
            </build>
        </profile>

        <profile>
            <id>physics_deploysystem</id>
            <properties>
                <profiles.active>physics_deploysystem</profiles.active>
                <talos.access.secret>ELB+d5waJyXyZmnc1v9Vpon2S1FmdelyMAOQOVTt</talos.access.secret>
            </properties>
            <build>
                <filters>
                    <filter>src/main/resources/config/physics_deploysystem.properties</filter>
                </filters>
            </build>
        </profile>

    </profiles>

    <distributionManagement>
        <repository>
            <id>central</id>
            <name>maven-release-virtual</name>
            <url>https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>maven-snapshot-virtual</name>
            <url>https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual</url>
        </snapshotRepository>
    </distributionManagement>

</project>