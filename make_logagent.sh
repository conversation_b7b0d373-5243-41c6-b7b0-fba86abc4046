#!/bin/bash

set -x -e
# cleanup old package
rm -rf release

MVN=$MAVEN_HOME/bin/mvn
JDEPS=$JAVA_HOME/bin/jdeps
JLINK=$JAVA_HOME/bin/jlink

$MVN -U clean package -P physics_deploysystem -pl log-agent-inner -am -DskipTests

mkdir -p release/lib/jdk21
mkdir -p release/conf
cp -r log-agent-inner/target/* release/
cp release/classes/*.xml release/conf
cp release/classes/*.properties release/conf
cp release/classes/*.json release/conf
cp -r release/classes/bin release
chmod +x release/bin/*

# get dependency modules
MODULES=$($JDEPS --multi-release 21 --module-path release/lib/jdk21 --ignore-missing-deps --list-deps release/log-agent-inner-*.jar | grep -v "Warning:" | grep -v "JDK" | awk -F/ '{print $1}' | awk '{print $1}' | sort | uniq | paste -sd ",")

$JLINK --module-path release/lib/jdk21 --no-header-files --no-man-pages --add-modules $MODULES --output release/java