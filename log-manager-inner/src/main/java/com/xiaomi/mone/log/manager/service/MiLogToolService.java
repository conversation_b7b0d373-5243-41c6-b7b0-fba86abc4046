package com.xiaomi.mone.log.manager.service;

import com.xiaomi.mone.log.manager.model.MigrateDtResourceConfig;
import com.xiaomi.mone.log.manager.model.bo.ResourceIds;
import org.apache.ozhera.log.common.Result;
import org.apache.ozhera.log.manager.model.dto.MapDTO;
import org.apache.ozhera.log.manager.model.vo.LogQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/1/5 10:55
 */
public interface MiLogToolService {
    Result<List<MapDTO<String, Long>>> getSpacesByUser(String user, Boolean isAdmin);

    Result<String> sendLokiMsg(Long tailId);

    void fixAlertAppId();

    void fixMilogAlertTailId();

    void fixFlinkAlertNotify(Long alertId);

    String fixResourceLabel();

    String fixLogStoreMqResourceId(Long storeId);

    String fixNacosEsInfo(Long spaceId);

    String updateEsClusterAddress(Long spaceId, Long esClusterId, String newEsAddr, String catalog);

    String updateEsClusterInfo(ResourceIds esInfoUpdate);

    void fixLogTailLogAppId(String appName);

    String fixEsIndexNotExistByStore(Long storeId);

    void fixAlertLogAppId(Integer tailId);

    void fixAppMiddleRel(Long tailId);

    void fixTailAppId();

    void fixLogAlertJarPath(Long alertId) throws InterruptedException;

    void fixAlarmJobRestart(Long alertId);

    void fixAlarmLogPath(Long alertId);

    List<Long> queryAlarmByCondition(String key, String value);

    void fixAlertFlinkCluster(Long id);

    // 将报警作业从一个机房迁移到拆分后的机房
    String rebuildAlert(Long alertId, String oldflinkCluster, String oldAlphaToken, String newAlphaToken, Boolean forceUpdate);

    // 将工场资源迁移到另一个机房
    String migrateCollectionResourceToAnotherDtSpace(MigrateDtResourceConfig config);

    void sendAllStorePeriodMq(Long storeId);

    Result<String> queryEsSearchDSL(LogQuery query);

    String fixNacosMqToken(List<Long> mqIdList);

    String pushConfigToNacos(String machineRoom);
}
