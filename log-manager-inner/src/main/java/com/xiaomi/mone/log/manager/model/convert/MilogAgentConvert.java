package com.xiaomi.mone.log.manager.model.convert;

import com.xiaomi.youpin.gwdash.bo.openApi.ProjectDeployInfoQuery;
import org.apache.ozhera.log.manager.model.dto.MilogAgentDTO;
import org.apache.ozhera.log.manager.model.pojo.MilogAgentDO;
import org.apache.ozhera.log.manager.model.pojo.MilogComputerRoomDO;
import org.apache.ozhera.log.manager.model.pojo.MilogLogProcessDO;
import org.apache.ozhera.log.manager.model.vo.AgentListQuery;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MilogAgentConvert {
    MilogAgentConvert INSTANCE = Mappers.getMapper(MilogAgentConvert.class);

    @Mappings({
            @Mapping(source = "milogComputerRoomDO.roomName", target = "computerRoomName"),
            @Mapping(source = "milogAgentDO.id", target = "id"),
            @Mapping(source = "logProcessDOList", target = "processList"),
    })
    MilogAgentDTO fromDO(MilogAgentDO milogAgentDO, MilogComputerRoomDO milogComputerRoomDO, List<MilogLogProcessDO> logProcessDOList);

    ProjectDeployInfoQuery toQueryDeploy(AgentListQuery agentListQuery);
}
