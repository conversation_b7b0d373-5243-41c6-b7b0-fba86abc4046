package com.xiaomi.mone.log.manager.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description k8s需要传递给agent的数据
 * @date 2022/9/6 15:11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class K8sToAgentVo {
    private List<String> podNamePrefix;
    private String podType;

    public static K8sToAgentVo init() {
        return K8sToAgentVo.builder().podNamePrefix(Collections.EMPTY_LIST).build();
    }
}
