package com.xiaomi.mone.log.manager.service.consumer;

import com.xiaomi.mone.log.manager.model.dto.K8sMachineChangeDTO;
import com.xiaomi.mone.log.manager.service.impl.CloudPlatformK8sAppServiceImpl;
import com.xiaomi.mone.log.manager.service.impl.InnerMqConsumerServiceImpl;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.manager.service.consumer.RocketMqConsumer;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.message.MessageExt;

import javax.annotation.Resource;
import java.util.function.Consumer;

import static com.xiaomi.mone.log.manager.user.MoneUserDetailService.GSON;


@Slf4j
@Service
public class CloudPlatformK8sRMQConsumer  extends RocketMqConsumer {
    @Value("$miks_rocketmq_consumer_topic")
    private String consumeTopic;

    @Value("$miks_rocketmq_consumer_tag")
    private String consumeTag;

    @Value("$miks_rocketmq_consumer_group")
    private String consumerGroup;

    @Value("$rocketmq_ak")
    private String ak;

    @Value("$rocketmq_sk")
    private String sk;

    @Value("$rocketmq_namesrv_addr")
    private String addresses;

    @Resource
    private InnerMqConsumerServiceImpl milogLogtailSevice;
    @Resource
    private CloudPlatformK8sAppServiceImpl cloudPlatformK8sAppService;

    public void init() {
        log.info("【miks machine change】consumer mq service init");
        for (String address : addresses.split(",")) {
            String projectTag = getSplitTagString(consumeTag);
            DefaultMQPushConsumer consumer = initDefaultMQPushConsumer(ak, sk, consumerGroup, address);
            try {
                consumer.subscribe(consumeTopic, projectTag);
            } catch (MQClientException e) {
                log.error("【miks machine change】订阅RocketMq消费异常", e);
            }
            consumer.registerMessageListener((MessageListenerOrderly) (list, consumeOrderlyContext) -> {
                list.stream().forEach(ele -> {
                    // k8s机器变更时时发送消息
                    k8sMachineChange(ele, k8sMachineChangeDTO -> cloudPlatformK8sAppService.handleK8sTopicTail(k8sMachineChangeDTO));
                });
                return ConsumeOrderlyStatus.SUCCESS;
            });
            try {
                consumer.start();
            } catch (MQClientException e) {
                log.error("【k8s machine change】RocketMq客户端启动异常", e);
            }
        }
    }

    private void k8sMachineChange(MessageExt message, Consumer<K8sMachineChangeDTO> changeDTOConsumer) {
        try {
            byte[] body = message.getBody();
            K8sMachineChangeDTO machineChangeDTO = GSON.fromJson(new String(body), K8sMachineChangeDTO.class);
            log.info("【miks machine change】RocketMq消费的消息数据转化为对象: {}", machineChangeDTO.toString());
            changeDTOConsumer.accept(machineChangeDTO);
            log.info("【miks machine change】RocketMq消费的消息消费结束");
        } catch (Exception ex) {
            log.error(String.format("【miks machine change】RocketMq消费的消息消费异常:%s", message), ex);
        }
    }

}
