package com.xiaomi.mone.log.manager.service.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.xiaomi.mone.enums.InnerDeployWayEnum;
import com.xiaomi.mone.enums.InnerProjectTypeEnum;
import com.xiaomi.mone.log.manager.dao.InnerLogTailDao;
import com.xiaomi.mone.log.manager.model.po.AutoAccessLogParam;
import com.xiaomi.mone.log.manager.model.po.LogEnabledParam;
import com.xiaomi.mone.log.manager.model.vo.LogEnabledTreeVo;
import com.xiaomi.mone.log.manager.model.vo.LogEnabledVo;
import com.xiaomi.mone.log.manager.service.AutoAccessLogService;
import com.xiaomi.mone.log.manager.service.InnerTailExtensionService;
import com.xiaomi.mone.tpc.common.vo.NodeVo;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.app.api.response.AppBaseInfo;
import org.apache.ozhera.log.api.enums.OperateEnum;
import org.apache.ozhera.log.manager.common.exception.MilogManageException;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.dao.MilogLogstoreDao;
import org.apache.ozhera.log.manager.model.bo.LogTailParam;
import org.apache.ozhera.log.manager.model.dto.MilogAppEnvDTO;
import org.apache.ozhera.log.manager.model.pojo.MilogLogStoreDO;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;
import org.apache.ozhera.log.manager.service.HeraAppService;
import org.apache.ozhera.log.manager.service.impl.LogTailServiceImpl;
import org.apache.ozhera.log.manager.service.impl.TpcSpaceAuthService;
import org.apache.ozhera.log.parse.LogParserFactory;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Clock;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.*;
import static org.apache.ozhera.log.common.Constant.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/7/8 16:13
 */
@Slf4j
@Service
public class AutoAccessLogServiceImpl implements AutoAccessLogService {

    @Resource
    private MilogLogTailDao logTailDao;

    @Resource
    private InnerLogTailDao innerLogTailDao;

    @Resource
    private MilogLogstoreDao logStoreDao;

    @Resource
    private TpcSpaceAuthService spaceAuthService;

    @Resource
    private HeraAppService heraAppService;

    @Resource
    private LogTailServiceImpl logTailService;

    @Resource(name = INNER_TAIL_SERVICE)
    private InnerTailExtensionService tailExtensionService;

    @Value("$hera.url")
    private String heraUrl;

    private static final String DEFAULT_LOG_PATH_PREFIX = "/home/<USER>/log";
    private static final String DUPLICATE_OUTPUT_MESSAGE = "名称已经存在了";

    private static final Cache<String, List<LogEnabledTreeVo>> BIND_TREE_CACHE = CacheBuilder.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build();

    @Override
    public LogEnabledVo queryLogEnabled(LogEnabledParam logEnabledParam) {
        List<MilogLogTailDo> logTailDos = logTailDao.queryByAppAndEnv(logEnabledParam.getAppId(), logEnabledParam.getEnvId());
        return buildLogEnabledVo(logEnabledParam, logTailDos);
    }

    @Override
    public List<LogEnabledTreeVo> queryLogBindTree(String spaceName, String userName) {
        String key = spaceName + userName;
        if (BIND_TREE_CACHE.getIfPresent(key) != null) {
            return BIND_TREE_CACHE.getIfPresent(key);
        }
        List<LogEnabledTreeVo> logEnabledTreeVos = Lists.newArrayList();
        Result<List<NodeVo>> result = spaceAuthService.queryUserListNode(spaceName, userName);

        Optional.ofNullable(result)
                .map(Result::getData)
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(dataList -> dataList.parallelStream()
                        .filter(data -> !StringUtils.endsWith(data.getNodeName(), MIFE_LOG_PREFIX)
                                && !StringUtils.endsWith(data.getNodeName(), SYNC_LOG_PREFIX))
                        .forEach(nodeVo -> logEnabledTreeVos.add(buildLogEnabledTreeVo(nodeVo))));
        BIND_TREE_CACHE.put(key, logEnabledTreeVos);
        return logEnabledTreeVos;
    }

    @Override
    public String autoAccessToLog(AutoAccessLogParam logParam) {
        if (isTailIdAndNamePresent(logParam)) {
            return updateTailIfNecessary(logParam);
        }

        AppBaseInfo appBaseInfo = getAppBaseInfo(logParam);
        MilogLogStoreDO logStoreDO = getLogStore(logParam);
        MilogAppEnvDTO milogAppEnvDTO = getMilogAppEnvDTO(appBaseInfo, logParam, logStoreDO);
//        MilogAppEnvDTO milogAppEnvDTO = new MilogAppEnvDTO();
//        milogAppEnvDTO.setIps(Lists.newArrayList("************","*************"));

        String tailName = generateTailName(logParam.getEnvId(), appBaseInfo.getAppName(), logParam.getEnvName(), logParam.getTailName());
        if (isTailNameDuplicated(logParam, tailName)) {
            return DUPLICATE_OUTPUT_MESSAGE;
        }
        LogTailParam logTailParam = buildLogTailParam(logParam.getSpaceId(), logStoreDO, logParam, appBaseInfo, tailName, milogAppEnvDTO.getIps());

        MilogLogTailDo logTailDo = logTailService.buildLogTailDo(logTailParam, logStoreDO, appBaseInfo, logParam.getUserName());
        logTailDao.add(logTailDo);

        tailExtensionService.defaultBindingAppTailConfigRel(logTailDo.getId(), logTailDo.getMilogAppId(), logStoreDO.getMqResourceId(), "", null);
        logTailService.sengMessageToStream(logTailDo, OperateEnum.ADD_OPERATE.getCode());

        if (CollectionUtils.isNotEmpty(milogAppEnvDTO.getIps())) {
            CompletableFuture.runAsync(() -> logTailService.sengMessageToAgent(Long.valueOf(appBaseInfo.getId()), logTailDo));
        }
        return SUCCESS_MESSAGE;
    }

    private AppBaseInfo getAppBaseInfo(AutoAccessLogParam logParam) {
        return heraAppService.queryByAppId(logParam.getAppId(), InnerProjectTypeEnum.MIONE_TYPE.getCode());
    }

    private MilogLogStoreDO getLogStore(AutoAccessLogParam logParam) {
        MilogLogStoreDO logStoreDO = logStoreDao.queryById(logParam.getStoreId());
        validLogStore(logStoreDO);
        return logStoreDO;
    }

    private boolean isTailNameDuplicated(AutoAccessLogParam logParam, String tailName) {
        List<MilogLogTailDo> logTailDos = innerLogTailDao.queryByBindName(logParam.getSpaceId(), logParam.getStoreId(), tailName);
        return CollectionUtils.isNotEmpty(logTailDos);
    }

    private void validLogStore(MilogLogStoreDO logStoreDO) {
        if (logStoreDO.isPlatformResourceStore()) {
            throw new MilogManageException("Platform resource store cannot be accessed,Only applicable to the Matrix platform");
        }
    }

    private LogTailParam buildLogTailParam(Long spaceId, MilogLogStoreDO logStoreDO, AutoAccessLogParam logParam,
                                           AppBaseInfo appBaseInfo, String tailName, List<String> ips) {
        return LogTailParam.builder()
                .spaceId(spaceId)
                .storeId(logStoreDO.getId())
                .appId(Long.valueOf(appBaseInfo.getBindId()))
                .milogAppId(Long.valueOf(appBaseInfo.getId()))
                .envId(logParam.getEnvId())
                .envName(logParam.getEnvName())
                .tail(tailName)
                .parseType(LogParserFactory.LogParserEnum.SEPARATOR_PARSE.getCode())
                .parseScript(DEFAULT_TAIL_SEPARATOR)
                .logPath(buildLogPath(logParam.getLogPath(), logParam.getAppName()))
                .ips(ips)
                .valueList(DEFAULT_VALUE_LIST)
                .appType(InnerProjectTypeEnum.MIONE_TYPE.getCode())
                .deployWay(InnerDeployWayEnum.MILINE.getCode())
                .build();
    }

    private String buildLogPath(String logPath, String appName) {
        String logPathSuffix = "*.log";
        if (StringUtils.isBlank(logPath)) {
            return String.format("%s/%s/%s", DEFAULT_LOG_PATH_PREFIX, appName, logPathSuffix);
        }
        if (logPath.startsWith(DEFAULT_LOG_PATH_PREFIX)) {
            if (logPath.endsWith("/")) {
                return String.format("%s%s", logPath, logPathSuffix);
            }
            return String.format("%s/%s", logPath, logPathSuffix);
        } else {
            return String.format("%s/%s/%s", DEFAULT_LOG_PATH_PREFIX, logPath, logPathSuffix);
        }
    }

    private boolean isTailIdAndNamePresent(AutoAccessLogParam logParam) {
        return logParam.getTailId() != null && StringUtils.isNotBlank(logParam.getTailName());
    }

    private String updateTailIfNecessary(AutoAccessLogParam logParam) {
        MilogLogTailDo milogLogTailDo = logTailDao.queryById(logParam.getTailId());
        if (!StringUtils.equalsIgnoreCase(milogLogTailDo.getTail(), logParam.getTailName())) {
            milogLogTailDo.setTail(logParam.getTailName());
            logTailDao.update(milogLogTailDo);
        }
        return SUCCESS_MESSAGE;
    }

    private MilogAppEnvDTO getMilogAppEnvDTO(AppBaseInfo appBaseInfo, AutoAccessLogParam logParam, MilogLogStoreDO logStoreDO) {

        List<MilogAppEnvDTO> envDTOList = tailExtensionService.getEnInfosByAppId(appBaseInfo,
                Long.valueOf(appBaseInfo.getId()), InnerDeployWayEnum.MILINE.getCode(), logStoreDO.getMachineRoom());
//        String str = "[\n" +
//                "  {\n" +
//                "    \"id\": 931138,\n" +
//                "    \"name\": \"开源版本\",\n" +
//                "    \"ips\": [\n" +
//                "      \"************\"\n" +
//                "    ]\n" +
//                "  },\n" +
//                "  {\n" +
//                "    \"id\": 932053,\n" +
//                "    \"name\": \"open-source-inner\"\n" +
//                "  },\n" +
//                "  {\n" +
//                "    \"id\": 991214,\n" +
//                "    \"name\": \"测试环境->staging\",\n" +
//                "    \"ips\": [\n" +
//                "      \"************\",\n" +
//                "      \"*************\"\n" +
//                "    ]\n" +
//                "  }\n" +
//                "]";
//        List<SimplePipleEnvBo> simplePipeEnvBos = JSON.parseArray(str, SimplePipleEnvBo.class);
//        List<MilogAppEnvDTO> enInfosByAppId = simplePipeEnvBos.stream().map(envBo -> MilogAppEnvDTO.builder().label(envBo.getName()).value(envBo.getId()).ips(envBo.getIps()).build()).toList();

        return envDTOList.stream()
                .filter(milogAppEnvDTO -> milogAppEnvDTO.getValue().equals(logParam.getEnvId()))
                .findFirst()
                .orElseThrow(() -> new MilogManageException(String.format("query env not exist, envName: %s", logParam.getEnvName())));
    }

    private String generateTailName(Long envId, String appName, String envName, String tailName) {
        return StringUtils.isBlank(tailName) ? String.format("%d-%s-%s", envId, appName, envName) : tailName;
    }

    private LogEnabledTreeVo buildLogEnabledTreeVo(NodeVo nodeVo) {
        LogEnabledTreeVo logEnabledTreeVo = new LogEnabledTreeVo();
        logEnabledTreeVo.setSpaceId(nodeVo.getOutId());
        logEnabledTreeVo.setSpaceName(nodeVo.getNodeName());
        logEnabledTreeVo.setStoreList(buildLogStoreTreeVo(nodeVo.getOutId()));
        return logEnabledTreeVo;
    }

    private List<LogEnabledTreeVo.LogStoreTreeVo> buildLogStoreTreeVo(Long spaceId) {
        return Optional.ofNullable(spaceId)
                .map(logStoreDao::getMilogLogstoreBySpaceId)
                .filter(CollectionUtils::isNotEmpty)
                .map(logStoreDoS -> logStoreDoS.stream()
                        .filter(logStoreDO -> !logStoreDO.getUsePlatformResource())
                        .map(this::convertToLogStoreTreeVo)
                        .collect(Collectors.toList()))
                .orElseGet(ArrayList::new);
    }

    private LogEnabledTreeVo.LogStoreTreeVo convertToLogStoreTreeVo(MilogLogStoreDO logStoreDO) {
        LogEnabledTreeVo.LogStoreTreeVo logStoreTreeVo = new LogEnabledTreeVo.LogStoreTreeVo();
        logStoreTreeVo.setStoreId(logStoreDO.getId());
        logStoreTreeVo.setStoreName(logStoreDO.getLogstoreName());
        return logStoreTreeVo;
    }

    private LogEnabledVo buildLogEnabledVo(LogEnabledParam logEnabledParam, List<MilogLogTailDo> logTailDos) {
        LogEnabledVo logEnabledVo = new LogEnabledVo();

        if (CollectionUtils.isNotEmpty(logTailDos)) {
            MilogLogTailDo milogLogTailDo = logTailDos.getFirst();
            logEnabledVo.setHasLoggingEnabled(true);
            logEnabledVo.setSpaceId(milogLogTailDo.getSpaceId());
            logEnabledVo.setStoreId(milogLogTailDo.getStoreId());
            logEnabledVo.setTailId(milogLogTailDo.getId());
            logEnabledVo.setTailName(milogLogTailDo.getTail());
            String logUrl = buildLogUrl(logEnabledParam, milogLogTailDo);
            logEnabledVo.setLogUrl(logUrl);
        } else {
            logEnabledVo.setHasLoggingEnabled(false);
        }

        return logEnabledVo;
    }

    private String buildLogUrl(LogEnabledParam logEnabledParam, MilogLogTailDo milogLogTailDo) {
        long curTimestamp = Clock.systemUTC().instant().toEpochMilli();
        long startTime = curTimestamp - TimeUnit.MINUTES.toMillis(5);
        long endTime = curTimestamp + TimeUnit.MINUTES.toMillis(5);

        return String.format("%s/project-milog/user/space-tree?spaceId=%d&startTime=%d&inputV=%s" +
                        "&storeId=%d&endTime=%d&type=search&tailName=%s",
                heraUrl,
                milogLogTailDo.getSpaceId(),
                startTime,
                buildLogIpParam(logEnabledParam.getIp()),
                milogLogTailDo.getStoreId(),
                endTime,
                URLEncoder.encode(milogLogTailDo.getTail(), StandardCharsets.UTF_8));
    }

    private String buildLogIpParam(String logIp) {
        if (StringUtils.isBlank(logIp)) {
            return StringUtils.EMPTY;
        }
        return URLEncoder.encode(String.format("%s:%s", "logip", logIp), StandardCharsets.UTF_8);
    }
}
