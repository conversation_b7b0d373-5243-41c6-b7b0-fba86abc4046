package com.xiaomi.mone.log.manager.service.alert;

import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import com.xiaomi.mone.enums.AlertTypeEnum;
import com.xiaomi.mone.enums.InnerMachineRegionEnum;
import com.xiaomi.mone.enums.InnerMiddlewareEnum;
import com.xiaomi.mone.enums.MatchOperatorEnum;
import com.xiaomi.mone.log.manager.common.context.MoneUserContext;
import com.xiaomi.mone.log.manager.common.utils.DtUtils;
import com.xiaomi.mone.log.manager.dao.alert.AlertConditionDao;
import com.xiaomi.mone.log.manager.dao.alert.AlertDao;
import com.xiaomi.mone.log.manager.dao.alert.AlertRuleDao;
import com.xiaomi.mone.log.manager.model.alert.*;
import com.xiaomi.mone.log.manager.model.bo.alert.*;
import com.xiaomi.mone.log.manager.service.impl.InnerMilogAppMiddlewareRelServiceImpl;
import com.xiaomi.mone.model.res.AlertMatchRuleRes;
import com.xiaomi.mone.model.res.MatchContentRule;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.app.api.response.AppBaseInfo;
import org.apache.ozhera.log.manager.common.Result;
import org.apache.ozhera.log.manager.common.exception.MilogManageException;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.dao.MilogLogstoreDao;
import org.apache.ozhera.log.manager.model.pojo.MilogLogStoreDO;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;
import org.apache.ozhera.log.manager.service.impl.HeraAppServiceImpl;
import org.apache.ozhera.log.parse.LogParser;
import org.apache.ozhera.log.parse.LogParserData;
import org.apache.ozhera.log.parse.LogParserFactory;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.xiaomi.mone.enums.AlertTypeEnum.CONTENT_ORIGIN_MATCH;
import static org.apache.ozhera.log.common.Constant.*;
import static org.apache.ozhera.log.manager.common.Utils.getKeyValueList;
import static org.apache.ozhera.log.parse.LogParser.ES_KEY_MAP_MESSAGE;


/**
 * <AUTHOR>
 */

@Slf4j
@Service
public class AlertService {

    @Resource
    private AlertDao alertDao;

    @Resource
    private FlinkService flinkService;

    @Resource
    private FlinkAlphaService flinkAlphaService;

    @Resource
    private LogProducer logProducer;

    @Resource
    private AlertRuleDao alertRuleDao;

    @Resource
    private AlertConditionDao alertConditionDao;

    @Resource
    private SendAlertRulesService sendAlertRulesService;

    @Resource
    private HeraAppServiceImpl heraAppService;

    @Resource
    private InnerMilogAppMiddlewareRelServiceImpl middlewareRelService;

    @Resource
    private MilogLogTailDao milogLogtailDao;

    @Resource
    private MilogLogstoreDao milogLogstoreDao;

    @Value(value = "$server.type")
    private String serverType;

    @Value("$alpha_token")
    private String alphaToken;

    private static final ExecutorService executor = Executors.newFixedThreadPool(10);

    private boolean isValidParam(AlertParam alertParam) {
        return alertParam != null && StringUtils.isNotEmpty(alertParam.getType()) &&
                null != alertParam.getMilogAppId() && StringUtils.isNotBlank(alertParam.getName())
                && CollectionUtils.isNotEmpty(alertParam.getTailIds());
    }

    public Alert createAlert(AlertParam alertParam, String user) {
        if (!isValidParam(alertParam)) {
            log.warn("invalid alertParam");
            return null;
        }
        AppBaseInfo appBaseInfo = heraAppService.queryById(alertParam.getMilogAppId());
        String logPaths = buildLogPaths(alertParam.getTailIds());
        Alert alert = new Alert();
        alert.setName(alertParam.getName());
        alert.setType(alertParam.getType());
        alert.setApp(appBaseInfo.getBindId());
        alert.setMilogAppId(alertParam.getMilogAppId());
        alert.setLogPath(logPaths);
        alert.setContacts(alertParam.getContacts());
        alert.setFeishuGroups(alertParam.getFeishuGroups());
        alert.setAlertGroups(alertParam.getAlertGroups());
        alert.setAtMembers(alertParam.getAtMembers());
        alert.setAppName(appBaseInfo.getAppName());
        alert.setCallbackUrl(alertParam.getCallbackUrl());
        alert.setWindowSize(alertParam.getWindowSize());
        updateArgumentsMap(alert, alertParam);
        alert.setStatus(AlertStatus.OFF.getStatus());
        alert.setCreator(user);
        createAlert(alert);
        createRules(alertParam.getRules(), alert.getId(), user, alertParam.getType());

        return alert.getId() > 0 ? alert : null;
    }

    public String buildLogPaths(List<Long> tailIds) {
        List<MilogLogTailDo> logTailDos = milogLogtailDao.queryByIds(tailIds);
        String logPaths = logTailDos.stream().map(MilogLogTailDo::getLogPath).collect(Collectors.joining(SYMBOL_COMMA));
        return logPaths;
    }

    private void createAlert(Alert alert) {
        alertDao.insert(alert);
    }

    private void createRules(List<AlertRuleParam> rules, long alertId, String creator, String alertType) {
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }

        rules.forEach(alertRuleParam -> {
            String ruleRegex = alertRuleParam.getRegex();

            if (StringUtils.equals(AlertTypeEnum.CONTENT_PARSE_MATCH.getType(), alertType)) {
                List<AlertMatchRuleRes> alertMatchRuleRes = alertRuleParam.getTailIds().stream().map(tailId -> {
                    MilogLogTailDo logTailDo = milogLogtailDao.queryById(tailId);
                    MilogLogStoreDO logStoreDO = milogLogstoreDao.queryById(logTailDo.getStoreId());
                    LogParser logParser = LogParserFactory.getLogParser(
                            logTailDo.getParseType(), logStoreDO.getKeyList(), logTailDo.getValueList(),
                            logTailDo.getParseScript(), "", logTailDo.getTail(),
                            "", logStoreDO.getLogstoreName(), logStoreDO.getKeyList());
                    AlertMatchRuleRes res = new AlertMatchRuleRes();
                    res.setTailId(tailId);
                    //由于日志告警版本的问题，导致这里只能改，不能用原始的
                    res.setParserClassName(STR."com.xiaomi.mione.log.parse.\{StringUtils.substringAfterLast(logParser.getClass().getName(), ".")}");
                    res.setMatchContentRule(alertRuleParam.getMatchContentRuleList());

                    LogParserData logParserData = createLogParserData(logStoreDO, logTailDo);
                    res.setMatchContentRule(alertRuleParam.getMatchContentRuleList());
                    res.setLogParserData(logParserData);
                    return res;
                }).collect(Collectors.toList());
                ruleRegex = GSON.toJson(alertMatchRuleRes);
            }
            if (StringUtils.equals(CONTENT_ORIGIN_MATCH.getType(), alertType)) {
                List<AlertMatchRuleRes> alertMatchRuleRes = alertRuleParam.getTailIds().stream().map(tailId -> {
                    AlertMatchRuleRes res = new AlertMatchRuleRes();
                    res.setTailId(tailId);
                    res.setMatchContentRule(alertRuleParam.getMatchContentRuleList());
                    return res;
                }).collect(Collectors.toList());
                ruleRegex = GSON.toJson(alertMatchRuleRes);
            }
            String name = StringUtils.equals(AlertTypeEnum.REGEX_COUNT.getType(), alertType) ? alertRuleParam.getName() : StringUtils.join(alertRuleParam.getTailIds(), SYMBOL_COMMA);
            alertRuleDao.createRule(alertRuleParam.getAlertConditionList(), alertId, ruleRegex, creator, name);
        });
    }

    private static LogParserData createLogParserData(MilogLogStoreDO logStoreDO, MilogLogTailDo logTailDo) {
        LogParserData logParserData = new LogParserData();
        logParserData.setKeyList(logStoreDO.getKeyList());
        logParserData.setValueList(logTailDo.getValueList());
        logParserData.setParseScript(logTailDo.getParseScript());
        logParserData.setTopicName("");
        logParserData.setTailName(logTailDo.getTail());
        return logParserData;
    }

    private void updateArgumentsMap(Alert alert, AlertParam alertParam) {
        // alert.addArgument(Constants.COUNT_LIMIT,
        // String.valueOf(alertParam.getCountLimit()));
        // alert.addArgument(Constants.FILTER_REGEX, alertParam.getFilterRegex());
        // alert.addArgument(Constants.WINDOW_SIZE,
        // String.valueOf(alertParam.getWindowSize()));
        alert.addArgument(Constants.WINDOW_SIZE, String.valueOf(alertParam.getWindowSize()));
        // alert.addArgument(Constants.WINDOW_OFFSET,
        // String.valueOf(alertParam.getWindowOffset()));
        // alert.addArgument(Constants.OPERATION,
        // String.valueOf(alertParam.getOperation()));
        alert.addArgument(Constants.DEPARTMENT, alertParam.getDepartment());
        alert.addArgument(Constants.APP_NAME, alert.getAppName());
        alert.addArgument(Constants.MQ_TYPE, alertParam.getMqType());
        alert.addArgument(TAILID_KEY,
                alertParam.getTailIds().stream().map(String::valueOf).collect(Collectors.joining(SYMBOL_COMMA)));

    }

    public void updateAlertArguments(Alert alert, AlertParam alertParam) {
        if (StringUtils.isEmpty(alertParam.getMqType())) {
            alertParam.setMqType(InnerMiddlewareEnum.TALOS.getName());
        }
        alert.setApp(alertParam.getAppId());
        alert.setLogPath(alertParam.getLogPath());
        updateArgumentsMap(alert, alertParam);
        alertDao.update(alert);
    }

    public Alert getAlert(long id) {
        return alertDao.getAlert(id);
    }

    public AlertQueryResult getAlert(String alertName) {
        return alertDao.getAlert(alertName);
    }

    public List<Alert> getAllAlerts() {
        return alertDao.getAllAlerts();
    }

    public AlertQueryResult getAllAlerts(AlertQuery alertQuery) {
        AlertQueryResult result = alertDao.getAllAlerts(alertQuery);
        List<AlertBo> alerts = result.getAlerts();

        List<CompletableFuture<AlertBo>> completableFutures = alerts.stream()
                .map(alertBo -> CompletableFuture.supplyAsync(() -> {
                    configureAlertRules(alertBo);
                    processAlert(alertBo);
                    return alertBo;
                }, executor)).toList();

        // 等待所有结束
        CompletableFuture<Void> allOf = CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0]));
        try {
            allOf.get(30, TimeUnit.SECONDS);
        } catch (InterruptedException | TimeoutException | ExecutionException e) {
            // 处理等待过程中的异常
            log.error("Error while waiting for tasks to complete", e);
        }

        // 获取结果
        List<AlertBo> alertBoList = completableFutures.stream().map(CompletableFuture::join)
                .collect(Collectors.toList());
        result.setAlerts(alertBoList);

        return result;
    }

    private void processAlert(AlertBo alertBo) {
        configureAlertRules(alertBo);
        try {
            String url = String.format("/openapi/develop/jobs/%s", alertBo.getJobId());
            AlertJobDetail alertJobDetail = DtUtils.get(url, alphaToken, AlertJobDetail.class);
            alertBo.setJobStatus(alertJobDetail.getJobStatus());
        } catch (Exception e) {
            alertBo.setJobStatus("Unknown");
            log.error("Error while processing alert,alertBo:{}", alertBo, e);
        }
    }

    private void configureAlertRules(AlertBo alertBo) {
        List<AlertRuleBo> alertRuleBos = alertRuleDao.getAllRulesBo(alertBo.getAlertId());

        if (AlertTypeEnum.CONTENT_PARSE_MATCH.getType().equals(alertBo.getType()) || AlertTypeEnum.CONTENT_ORIGIN_MATCH.getType().equals(alertBo.getType())) {
            List<AlertRuleParam> contentRules = createContentRules(alertRuleBos);
            alertBo.setContentRules(contentRules);
        }
        configureAlertConditions(alertRuleBos, alertBo.getContentRules(), alertBo.getType());
        alertBo.setAlertRules(alertRuleBos);
    }

    private List<AlertRuleParam> createContentRules(List<AlertRuleBo> alertRuleBos) {
        return alertRuleBos.stream()
                .map(this::createAlertRuleParam)
                .collect(Collectors.toList());
    }

    private AlertRuleParam createAlertRuleParam(AlertRuleBo alertRuleBo) {
        AlertRuleParam alertRuleParam = new AlertRuleParam();
        alertRuleParam.setAlertRuleId(alertRuleBo.getAlertRuleId());
        alertRuleParam.setName(alertRuleBo.getName());
        alertRuleParam.setTailIds(parseTailIds(alertRuleBo.getName()));
        alertRuleParam.setMatchContentRuleList(extractMatchContentRules(alertRuleBo.getRegex()));
        return alertRuleParam;
    }

    private List<Long> parseTailIds(String name) {
        return Arrays.stream(name.split(SYMBOL_COMMA))
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }

    private List<MatchContentRule> extractMatchContentRules(String regex) {
        List<AlertMatchRuleRes> matchContentRules = GSON.fromJson(regex, new TypeToken<List<AlertMatchRuleRes>>() {
        }.getType());
        return matchContentRules.stream()
                .flatMap(rule -> rule.getMatchContentRule().stream())
                .distinct()
                .collect(Collectors.toList());
    }

    private void configureAlertConditions(List<AlertRuleBo> alertRuleBos, List<AlertRuleParam> contentRules, String type) {
        if (AlertTypeEnum.CONTENT_PARSE_MATCH.getType().equals(type) || AlertTypeEnum.CONTENT_ORIGIN_MATCH.getType().equals(type)) {
            configureConditionsForContentRules(contentRules);
        } else {
            configureConditionsForAlertRules(alertRuleBos);
        }
    }

    private void configureConditionsForContentRules(List<AlertRuleParam> contentRules) {
        contentRules.forEach(contentRule -> {
            List<AlertConditionBo> alertConditionBos = alertConditionDao.getAllConditionsBo(contentRule.getAlertRuleId());
            contentRule.setAlertConditions(alertConditionBos);
        });
    }

    private void configureConditionsForAlertRules(List<AlertRuleBo> alertRuleBos) {
        alertRuleBos.forEach(alertRuleBo -> {
            List<AlertConditionBo> alertConditionBos = alertConditionDao.getAllConditionsBo(alertRuleBo.getAlertRuleId());
            alertRuleBo.setAlertConditions(alertConditionBos);
        });
    }

    public boolean updateAlert(Alert alert) {
        return alertDao.update(alert);
    }

    public void deleteAlert(long id) {
        alertDao.delete(id);
        alertRuleDao.deleteAllRules(id);
    }

    public boolean startAlert(Alert alert) {
        // return flinkService.updateFlinkAlertRules(alert, getAllRules(alert.getId()));
        // return flinkService.startFlinkJob(alert);
        return flinkAlphaService.startJob(getRegionEnByAlert(alert), alert.getJobId(), alert.getId());
    }

    public String getRegionEnByAlert(Alert alert) {
        String regionEn = InnerMachineRegionEnum.CN_MACHINE.getEn();
        try {
            String tailIds = alert.getArguments().get(TAILID_KEY);
            if (StringUtils.isNotEmpty(tailIds)) {
                String[] tailSplit = tailIds.split(SYMBOL_COMMA);
                MilogLogTailDo milogLogTailDo = milogLogtailDao.queryById(Long.valueOf(tailSplit[0]));
                if (null != milogLogTailDo) {
                    return milogLogstoreDao.queryById(milogLogTailDo.getStoreId()).getMachineRoom();
                }
            }
        } catch (Exception e) {
            log.error("getRegionEnByAlert error,param:{}", GSON.toJson(alert), e);
        }
        return regionEn;
    }

    public void restartAllAlerts(List<Alert> alerts) {
        for (Alert alert : alerts) {
            if (alert.getStatus() == AlertStatus.ON.getStatus()) {
                // flinkService.restartFlinkJob(alert);
                flinkAlphaService.startJob(getRegionEnByAlert(alert), alert.getJobId(), alert.getId());
            }
        }
    }

    public boolean stopAlert(Alert alert) {
        // return flinkService.stopFlinkJob(alert);
        return flinkAlphaService.stopJob(getRegionEnByAlert(alert), alert.getJobId(), alert.getId());
    }

    public boolean removeFlinkJob(Alert alert) {
        // return flinkService.removeFlinkJob(alert);
        return flinkAlphaService.deleteJob(getRegionEnByAlert(alert), alert.getJobId(), alert.getId());
    }

    /**
     * Update yarn queue for all flink jobs
     */
    public void updateYarnQueue() {
        List<Alert> alerts = getAllAlerts();
        for (Alert alert : alerts) {
            String department = alert.getArgument(Constants.DEPARTMENT);
            if (StringUtils.isNotEmpty(department)) {
                flinkService.updateFlinkJobYarnQueue(alert, department);
            }
        }
    }

    public boolean updateFlinkJobArguments(Alert alert, AlertParam param) {
        // return flinkService.updateFlinkJobArguments(alert, param,
        // getAllRules(alert.getId()));
        MqInfoBo mqInfoBo = middlewareRelService.getMqInfoBo(param.getTailIds());
        return flinkAlphaService.updateJob(alert, param, mqInfoBo);
    }

    public Long submitFlinkJob(Alert alert, AlertParam params) {
        if (StringUtils.isEmpty(params.getMqType())) {
            params.setMqType(InnerMiddlewareEnum.TALOS.getName());
        }
        MqInfoBo mqInfoBo = middlewareRelService.getMqInfoBo(params.getTailIds());
        Long jobId = flinkAlphaService.submitFlinkJob(alert, params, mqInfoBo);
        return jobId;
    }

    public boolean produceLogs(String accessKey, String secretKey, String topicName, String mqServer, String logBody,
                               int numOfLogs) {
        return logProducer.produceLogs(accessKey, secretKey, topicName, mqServer, logBody, numOfLogs);
    }

    public List<AlertRule> getAllRules(long alertId) {
        return alertRuleDao.getAllRules(alertId);
    }

    public List<AlertRuleBo> getAllRulesBo(long alertId) {
        List<AlertRuleBo> alertRuleBos = alertRuleDao.getAllRulesBo(alertId);
        for (AlertRuleBo alertRuleBo : alertRuleBos) {
            List<AlertConditionBo> alertConditionBos = alertConditionDao
                    .getAllConditionsBo(alertRuleBo.getAlertRuleId());
            alertRuleBo.setAlertConditions(alertConditionBos);
        }
        return alertRuleBos;
    }

    public boolean updateRules(long alertId, List<AlertRuleParam> rules, String creator, String type) {
        if (rules == null) {
            return true;
        }
        if ((AlertTypeEnum.CONTENT_PARSE_MATCH.getType().equals(type) || CONTENT_ORIGIN_MATCH.getType().equals(type))) {
            handleContentMatchRules(alertId, rules, creator, type);
        } else if (AlertTypeEnum.REGEX_COUNT.getType().equals(type)) {
            handleRegexCountRules(alertId, rules, creator);
        }
        return true;
    }

    private void handleRegexCountRules(long alertId, List<AlertRuleParam> rules, String creator) {
        for (AlertRuleParam rule : rules) {
            if (rule.getType().equals(AlertRuleUpdate.CREATE.getType())) {
                createRule(rule.getAlertConditionList(), alertId, rule.getRegex(), creator, rule.getName());
            } else if (rule.getType().equals(AlertRuleUpdate.DELETE.getType())) {
                deleteRule(rule.getAlertRuleId());
            } else {
                // update
                AlertRule alertRule = getRule(rule.getAlertRuleId());
                if (alertRule == null) {
                    continue;
                }
                alertRule.setRegex(rule.getRegex());
                alertRule.setName(rule.getName());
                updateRule(alertRule);

                deleteAllAlertConditions(rule.getAlertRuleId());
                if (rule.getAlertConditionList() != null) {
                    for (AlertConditionParam alertConditionParam : rule.getAlertConditionList()) {
                        createAlertCondition(rule.getAlertRuleId(), alertConditionParam, creator);
                    }
                }
            }
        }
    }

    private void handleContentMatchRules(long alertId, List<AlertRuleParam> rules, String creator, String type) {
        deleteAllRule(alertId);
        createRules(rules, alertId, creator, type);
    }

    public void createRule(List<AlertConditionParam> alertConditionList, long alertId, String regex, String creator,
                           String name) {
        alertRuleDao.createRule(alertConditionList, alertId, regex, creator, name);
    }

    public void createAlertCondition(long alertRuleId, AlertConditionParam conditionParam, String creator) {
        alertConditionDao.createAlertCondition(alertRuleId, conditionParam, creator);
    }

    public void updateFlinkRules(long alertId) {
        List<AlertRule> rules = getAllRules(alertId);
        sendAlertRulesService.updateAlertRules(alertId, rules);
        CompletableFuture.runAsync(() -> updateAlertJobDetails(alertId));
    }

    private void updateAlertJobDetails(long alertId) {
        try {
            Alert alert = alertDao.getAlert(alertId);
            String authorization = String.format("workspace-token/1.0 %s", alphaToken);

            // Extract constants or use a configuration for URLs
            String jobDetailUrl = String.format("/openapi/develop/jobs/%s", alert.getJobId());

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", authorization);

            AlertJobDetail alertJobDetail = DtUtils.get(jobDetailUrl, alphaToken, AlertJobDetail.class);

            alert.setFlinkCluster(alertJobDetail.getFlinkCluster());
            alert.setFlinkJobName(alertJobDetail.getJobName());
            alertDao.update(alert);
        } catch (Exception e) {
            log.error("updateAlertJobDetails error,jobId:{}", alertId, e);
        }
    }

    public void updateRule(AlertRule alertRule) {
        alertRuleDao.updateRule(alertRule);
    }

    public void deleteRule(long alertRuleId) {
        alertRuleDao.deleteRule(alertRuleId);
    }

    public AlertRule getRule(long alertRuleId) {
        return alertRuleDao.getRule(alertRuleId);
    }

    public void deleteAllAlertConditions(long alertRuleId) {
        alertConditionDao.deleteAllAlertConditions(alertRuleId);
    }

    public void deleteAllRule(long alertId) {
        alertRuleDao.deleteAllRules(alertId);
        alertConditionDao.deleteAllAlertConditions(alertId);
    }

    public void deleteAlertCondition(long alertConditionId) {
        alertConditionDao.deleteAlertCondition(alertConditionId);
    }

    public List<AlertCondition> getAllConditions(long alertRuleId) {
        return alertConditionDao.getAllConditions(alertRuleId);
    }

    public void updateSendAlertTime(AlertCondition alertCondition) {
        alertConditionDao.updateSendAlertTime(alertCondition);
    }

    public String fixFlinkJobId(String jobName) {
        List<Alert> alerts;
        if (StringUtils.isBlank(jobName)) {
            alerts = alertDao.getAllAlerts();
        } else {
            alerts = alertDao.queryByJobName(jobName);
        }
        int count = 0;
        for (Alert alert : alerts) {
            count++;
            log.info("fixFlinkJobId total:{},start {},remaining:{}", alerts.size(), count, alerts.size() - count);
            Long jobId = flinkAlphaService.getFlinkJobIdByName(alert.getFlinkJobName());
            alert.setJobId(jobId);
            alertDao.update(alert);
        }
        return SUCCESS_MESSAGE;
    }

    public Result<AlertBo> getAlertDetail(Long alertId) {
        Alert alert = alertDao.getAlert(alertId);
        if (null == alert) {
            throw new MilogManageException("alert info not exist");
        }

        AlertBo alertBo = alertDao.convert(alert);
        configureAlertRules(alertBo);
        return Result.success(alertBo);
    }

    public List<AlarmTailRelationship> alarmWithTailIds(List<Long> tailIds, String type) {
        if (CONTENT_ORIGIN_MATCH.getType().equals(type)) {
            List<AlarmTailRelationship> result = new ArrayList<>();
            AlarmTailRelationship alarmTailRelationship = new AlarmTailRelationship();
            alarmTailRelationship.setTailIds(tailIds);
            alarmTailRelationship.setTailNames(tailIds.stream().map(milogLogtailDao::queryById).map(MilogLogTailDo::getTail).toList());
            alarmTailRelationship.setTailNames(Lists.newArrayList(ES_KEY_MAP_MESSAGE));
            result.add(alarmTailRelationship);
            return result;
        }
        // Step 1: Initialize the result list and validate input
        if (CollectionUtils.isEmpty(tailIds)) {
            return Collections.emptyList();
        }

        // Step 2: Retrieve log tail objects and group them by valueList
        List<MilogLogTailDo> logTailDos = tailIds.stream()
                .map(milogLogtailDao::queryById)
                .filter(Objects::nonNull)
                .toList();

        if (CollectionUtils.isEmpty(logTailDos)) {
            return Collections.emptyList();
        }

        Map<String, List<MilogLogTailDo>> logTailMap = logTailDos.stream().collect(Collectors.groupingBy(tail -> {
            MilogLogStoreDO logStore = milogLogstoreDao.queryById(tail.getStoreId());
            if (logStore == null) {
                return ""; // Handle null cases gracefully
            }
            return STR."\{tail.getParseType()}#\{getKeyValueList(logStore.getKeyList(), tail.getValueList())}";
        }));

        // Step 3: Convert grouped data into AlarmTailRelationship objects
        return logTailMap.entrySet().stream()
                .map(entry -> {
                    AlarmTailRelationship alarmTailRelationship = new AlarmTailRelationship();
                    alarmTailRelationship.setTailIds(entry.getValue().stream().map(MilogLogTailDo::getId).collect(Collectors.toList()));
                    alarmTailRelationship.setTailNames(entry.getValue().stream()
                            .map(MilogLogTailDo::getTail)
                            .toList());
                    alarmTailRelationship.setValueList(Arrays.asList(StringUtils.substringAfter(entry.getKey(), "#").split(",")));
                    return alarmTailRelationship;
                })
                .toList();
    }

    public List<AlertMatchOperator> getAlertMatchOperators(String type) {
        if (StringUtils.equals(CONTENT_ORIGIN_MATCH.getType(), type)) {
            return Stream.of(MatchOperatorEnum.CONTAINS, MatchOperatorEnum.NOT_CONTAINS)
                    .map(this::createAlertMatchOperator)
                    .collect(Collectors.toList());
        }
        return Arrays.stream(MatchOperatorEnum.values())
                .map(this::createAlertMatchOperator)
                .collect(Collectors.toList());
    }

    private AlertMatchOperator createAlertMatchOperator(MatchOperatorEnum operator) {
        AlertMatchOperator alertMatchOperator = new AlertMatchOperator();
        alertMatchOperator.setCode(operator.getCode());
        alertMatchOperator.setName(operator.getName());
        alertMatchOperator.setDesc(operator.getDesc());
        return alertMatchOperator;
    }

    public void updateAlertProperties(Alert alert, AlertUpdateParam param) {
        Integer oldWindowSize = alert.getWindowSize();
        boolean shouldRestartAlert = shouldRestartAlert(alert, param);
        alert.setContacts(param.getContacts());
        alert.setFeishuGroups(param.getFeishuGroups());
        alert.setAlertGroups(param.getAlertGroups());
        alert.setAtMembers(param.getAtMembers());
        alert.setCallbackUrl(param.getCallbackUrl());
        alert.setLogPath(buildLogPaths(param.getTailIds()));
        alert.addArgument(TAILID_KEY, param.getTailIds().stream().map(String::valueOf).collect(Collectors.joining(SYMBOL_COMMA)));

        alert.setName(param.getName());
        alert.setWindowSize(param.getWindowSize());

        updateAlert(alert);
        updateRules(alert.getId(), param.getRules(), MoneUserContext.getCurrentUser().getUser(), param.getType());

        if (shouldUpdateFlinkRules(param)) {
            List<Long> tailIdList = param.getTailIds();
            MqInfoBo mqInfoBo = middlewareRelService.getMqInfoBo(tailIdList);

            if (AlertStatus.ON.getStatus() == alert.getStatus() && (shouldRestartAlert || !Objects.equals(oldWindowSize, param.getWindowSize()))) {
                log.info("restart job,jobId:{}", alert.getId());
                flinkAlphaService.updateJob(alert, flinkAlphaService.buildAlertParam(param), mqInfoBo);
            }
            updateFlinkRules(alert.getId());
        }
    }

    private boolean shouldUpdateFlinkRules(AlertUpdateParam param) {
        return CollectionUtils.isNotEmpty(param.getRules());
    }

    /**
     * 如果规则和选择的tail没有变化就不需要重启
     *
     * @return
     */
    private boolean shouldRestartAlert(Alert alert, AlertUpdateParam param) {
        if (AlertTypeEnum.REGEX_COUNT.getType().equals(param.getType())) {
            return shouldRegexCountRestart(alert, param);
        }
        if (AlertTypeEnum.CONTENT_PARSE_MATCH.getType().equals(param.getType()) || CONTENT_ORIGIN_MATCH.getType().equals(param.getType())) {
            return shouldContentMatchRestart(alert, param);
        }
        return false;
    }

    private boolean shouldContentMatchRestart(Alert alert, AlertUpdateParam param) {
        // 获取现有的规则
        List<AlertRuleBo> alertRuleBos = alertRuleDao.getAllRulesBo(alert.getId());
        if (CollectionUtils.isEmpty(alertRuleBos)) {
            return true;
        }
        List<AlertRuleParam> contentRules = createContentRules(alertRuleBos);

        // 检查规则数量是否一致
        if (isRuleSizeDifferent(contentRules, param.getRules())) {
            return true;
        }

        // 检查规则名称是否一致
        if (isRuleNameDifferent(contentRules, param.getRules())) {
            return true;
        }

        // 检查规则内容是否一致
        return isRuleContentDifferent(contentRules, param.getRules());
    }

    /**
     * 检查规则数量是否一致
     */
    private boolean isRuleSizeDifferent(List<AlertRuleParam> contentRules, List<AlertRuleParam> paramRules) {
        return contentRules.size() != paramRules.size();
    }

    /**
     * 检查规则名称是否一致
     */
    private boolean isRuleNameDifferent(List<AlertRuleParam> contentRules, List<AlertRuleParam> paramRules) {
        Set<String> contentRuleNames = contentRules.stream()
                .map(AlertRuleParam::getName)
                .collect(Collectors.toSet());

        return paramRules.stream()
                .anyMatch(rule -> !contentRuleNames.contains(StringUtils.join(rule.getTailIds(), SYMBOL_COMMA)));
    }

    /**
     * 检查规则内容是否一致
     */
    private boolean isRuleContentDifferent(List<AlertRuleParam> contentRules, List<AlertRuleParam> paramRules) {
        Map<String, AlertRuleParam> contentRuleMap = contentRules.stream()
                .collect(Collectors.toMap(AlertRuleParam::getName, rule -> rule));

        for (AlertRuleParam paramRule : paramRules) {
            AlertRuleParam contentRule = contentRuleMap.get(StringUtils.join(paramRule.getTailIds(), SYMBOL_COMMA));

            // 检查规则是否存在
            if (contentRule == null) {
                return true;
            }

            // 检查匹配规则数量是否一致
            if (paramRule.getMatchContentRuleList().size() != contentRule.getMatchContentRuleList().size()) {
                return true;
            }

            // 检查每条匹配规则是否一致
            for (int i = 0; i < paramRule.getMatchContentRuleList().size(); i++) {
                MatchContentRule paramMatchRule = paramRule.getMatchContentRuleList().get(i);
                MatchContentRule contentMatchRule = contentRule.getMatchContentRuleList().get(i);

                if (!paramMatchRule.equals(contentMatchRule)) {
                    return true;
                }
            }
        }

        return false;
    }

    private boolean shouldRegexCountRestart(Alert alert, AlertUpdateParam param) {
        String targetTailIds = param.getTailIds().stream().map(String::valueOf).collect(Collectors.joining(SYMBOL_COMMA));
        String originTailIds = alert.getArgument(TAILID_KEY);
        List<String> originRegexps = getAllRulesBo(alert.getId()).stream()
                .map(AlertRuleBo::getRegex)
                .collect(Collectors.toList());
        List<String> targetRegexps = param.getRules().stream()
                .filter(rule -> !rule.getType().equals(AlertRuleUpdate.DELETE.getType()))
                .map(AlertRuleParam::getRegex)
                .collect(Collectors.toList());

        return !StringUtils.equals(originTailIds, targetTailIds) || !CollectionUtils.isEqualCollection(originRegexps, targetRegexps);
    }
}
