package com.xiaomi.mone.log.manager.model.bo.alert;

import com.xiaomi.mone.log.manager.model.alert.AlertGroup;
import lombok.Data;

import java.util.List;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.DEFAULT_ALERT_WINDOW_SIZE;

@Data
public class AlertParam {
    private long alertId;
    private String name;
    private String type;
    private Long milogAppId;
    private List<Long> tailIds;
    private String appId;
    private String appName;
    private String regionEn;
    /**
     * 多个以逗号分隔
     */
    private String logPath;
    private String contacts;
    private String feishuGroups;
    private List<AlertGroup> alertGroups;
    private String atMembers;
    private String flinkJobName;
    private String department;
    private String mqType;

    private int countLimit;
    private String filterRegex;
    private int windowSize = DEFAULT_ALERT_WINDOW_SIZE;
    private int windowOffset;
    private String operation;

    private String content;

    private List<AlertRuleParam> rules;

    private String callbackUrl;
}
