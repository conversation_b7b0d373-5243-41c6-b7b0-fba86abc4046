package com.xiaomi.mone.log.manager.model.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

import com.xiaomi.mione.tesla.k8s.bo.LogAgentListBo;

/**
 * <AUTHOR>
 * @Description 从 trace 得到的 matrix 应用信息
 * @date 2022-06-17
 */
@Data
@Builder
public class TraceAppInfoDTO {
    private Long appId;
    private String appName;
    private String accountId;
    private String deployPlatform;
    //优先从data取，data获取不到从service取
    private Long iamTreeId;
    private String matrixDeploySpace;
    // 保持向前兼容，后续使用 matrixNamespace
    private String matrixNamespace;
    private String appNamespace;
    // idc or available zone
    private String serviceCluster;
    private Long serviceClusterId;
    // tracing service
    private String tracingService;
    private String tracingCluster;
    // cloudml instance list
    private List<LogAgentListBo> cloudmlPodList;
}
