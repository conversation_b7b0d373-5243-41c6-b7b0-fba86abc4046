package com.xiaomi.mone.log.manager.service.alert;

import com.xiaomi.mone.log.manager.model.alert.Alert;
import com.xiaomi.mone.log.manager.model.bo.alert.AlertParam;
import com.xiaomi.mone.log.manager.model.bo.alert.MqInfoBo;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/6/13 14:14
 */
public interface AlertJobService {
    /**
     * 提交任务
     *
     * @param alert
     * @param params
     * @param mqInfoBo
     * @return
     */
    Long submitFlinkJob(Alert alert, AlertParam params, MqInfoBo mqInfoBo);

    /**
     * 启动任务
     *
     * @param jobId
     * @param alertId
     * @return
     */
    boolean startJob(final String regionEn, final Long jobId, final Long alertId);

    /**
     * 删除任务
     *
     * @param jobId
     * @param alertId
     * @return
     */
    boolean deleteJob(final String regionEn, final Long jobId, final Long alertId);

    /**
     * 停止任务
     *
     * @param jobId
     * @param alertId
     * @return
     */
    boolean stopJob(final String regionEn, final Long jobId, final Long alertId);

    /**
     * 更新任务
     *
     * @param alert
     * @param params
     * @param mqInfoBo
     * @return
     */
    boolean updateJob(final Alert alert, final AlertParam params, MqInfoBo mqInfoBo);
}
