package com.xiaomi.mone.log.manager.service;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.xiaomi.mione.tesla.k8s.bo.LogAgentListBo;
import com.xiaomi.mone.log.manager.model.dto.MetaAppInfoDTO;
import com.xiaomi.mone.log.manager.model.dto.MatrixDataDTO;
import com.xiaomi.mone.log.manager.service.impl.CloudPlatformK8sAppServiceImpl;
import com.xiaomi.mone.log.manager.service.impl.MatrixLogServiceImpl;
import com.xiaomi.youpin.docean.anno.Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ozhera.log.api.model.meta.LogCollectMeta;
import org.apache.ozhera.log.api.model.meta.LogPattern;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.xiaomi.mone.enums.InnerProjectTypeEnum.*;
import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.INNER_LOG_AGENT_SERVICE;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MatrixAgentConfigProcessor implements AgentConfigProcessor {

    @Resource(name = INNER_LOG_AGENT_SERVICE)
    private InnerLogAgentService logAgentService;
    @Resource
    private InnerLogTailService milogLogtailService;

    @Resource
    private MatrixLogServiceImpl matrixLogServiceImpl;

    @Resource
    private CloudPlatformK8sAppServiceImpl cloudPlatformK8sAppService;

    @Resource
    private HeraK8sAgentConfigProcessor heraK8sAgentConfigProcessor;

    private static final Cache<String, List<LogAgentListBo>> CACHE_LOCAL_PODS = CacheBuilder.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build();
    private static final Cache<String, LogCollectMeta> CACHE_LOCAL_METAS = CacheBuilder.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build();

    @Override
    public LogCollectMeta queryLogCollectMeta(String ip) {
        LogCollectMeta meta = CACHE_LOCAL_METAS.getIfPresent(ip);
        try {
            if (null == meta || CollectionUtils.isEmpty(meta.getAppLogMetaList())) {
                List<LogAgentListBo> alivePods = CACHE_LOCAL_PODS.getIfPresent(ip);
                if (CollectionUtils.isEmpty(alivePods)) {
                    // 读链路拿 miks pod 列表
                    List<LogAgentListBo> tracingPods = cloudPlatformK8sAppService.queryPodListByIp(ip);
                    // 如果链路拿到的 pod 是空的，就兜底走 matrix 的接口，尽可能但不完全下掉 Matrix 依赖
                    if (CollectionUtils.isEmpty(tracingPods)) {
                        List<MatrixDataDTO.PodInfo> matrixPods = matrixLogServiceImpl.queryMatrixPodInfoByIp(ip);
                        List<LogAgentListBo> aliveMatrixPods = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(matrixPods)) {
                            aliveMatrixPods = matrixPods.stream().filter(MatrixDataDTO.PodInfo::isAlivePod).map(MatrixDataDTO.PodInfo::toLogAgentListBo).collect(Collectors.toList());
                        }
                        alivePods = aliveMatrixPods;
                    } else {
                        alivePods = tracingPods.stream()
                                .distinct()
                                .toList();
                    }
                    if (CollectionUtils.isNotEmpty(alivePods)) {
                        CACHE_LOCAL_PODS.put(ip, alivePods);
                    }
                }
                //过滤活着的pod
                meta = queryLogAgentConfigForK8s(ip, alivePods);
                CACHE_LOCAL_METAS.put(ip, meta);
            }
        } catch (Exception e) {
            log.error("queryLogCollectMeta failed for cloud platform k8s ip:{}, e", ip, e);
        }
        return meta;
    }

    /**
     * queryLogAgentConfigForK8s
     * 获取这个 agentIp(daemon set) 下的全量采集信息
     * 1. 根据每一个pod ip查出一个taillist
     * 2. 根据每个taillist 拼出一个logcollectmeta
     */
    public LogCollectMeta queryLogAgentConfigForK8s(String agentIp, List<LogAgentListBo> podIps) {
        LogCollectMeta logCollectMeta = logAgentService.initializeLogCollectMeta(agentIp);
        // tail 2 pods 关系，pods 都在该宿主机上的
        Map<MilogLogTailDo, List<LogAgentListBo>> tailDoListMap = heraK8sAgentConfigProcessor.mapLogTailsToAgents(podIps);
        List<MilogLogTailDo> logTailDos = tailDoListMap.keySet().stream().distinct().collect(Collectors.toList());
        // appId 2 tails 关系
        Map<Long, List<MilogLogTailDo>> idTailListMap = heraK8sAgentConfigProcessor.warpAppAndTailRel(logTailDos);
        List<Long> appBaseInfoIds = Lists.newArrayList(idTailListMap.keySet());
        logCollectMeta.setAppLogMetaList(appBaseInfoIds.stream()
                // 对每一个 app_id 下的 tail list
                .map(appBaseInfoId -> {
                    List<LogPattern> logPatternList = Lists.newArrayList();
                    // 遍历 tail
                    for (MilogLogTailDo milogLogtailDo : idTailListMap.get(appBaseInfoId)) {
                        // 对每一个 tail 对应的多个 pod
                        String appNamespace = "";
                        //todo 下线旧接口
                        if (MIKS_TYPE.getCode().equals(milogLogtailDo.getAppType())) {
                            MetaAppInfoDTO appInfoDTO = cloudPlatformK8sAppService.getMetaAppByAppId(milogLogtailDo.getAppId(), false);
                            appNamespace = appInfoDTO.getExtra().getLogNamespace();
                        } else {
                            appNamespace = matrixLogServiceImpl.getTraceBaseInfoByAppId(milogLogtailDo.getAppId()).getAppNamespace();
                        }
                        logPatternList.addAll(logAgentService.assembleLogPatternForCloudPlatformK8s(
                                appNamespace,
                                milogLogtailDo,
                                tailDoListMap.get(milogLogtailDo)));
                    }
                    return logAgentService.assembleSingleConfig(appBaseInfoId, logPatternList);
                })
                .filter(appLogMeta -> CollectionUtils.isNotEmpty(appLogMeta.getLogPatternList()))
                .collect(Collectors.toList()));
        return logCollectMeta;
    }


}
