package com.xiaomi.mone.log.manager.controller;

import com.xiaomi.mone.log.manager.service.InnerAppService;
import com.xiaomi.youpin.docean.anno.Controller;
import com.xiaomi.youpin.docean.anno.RequestMapping;
import com.xiaomi.youpin.docean.anno.RequestParam;
import org.apache.ozhera.log.common.Result;
import org.apache.ozhera.log.manager.service.impl.MilogDictionaryServiceImpl;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/4/11 19:52
 */
@Controller
public class InnerMilogDictionaryController {

    @Resource
    private MilogDictionaryServiceImpl milogDictionaryService;

    @Resource
    private InnerAppService innerAppService;


    @RequestMapping(path = "/milog/sync/mis/app", method = "get")
    public Result<String> synchronousMisApp(@RequestParam("serviceName") String serviceName) {
        return innerAppService.synchronousMisApp(serviceName);
    }

    @RequestMapping(path = "/milog/sync/radar/app", method = "get")
    public Result<String> synchronousRadarApp(@RequestParam("serviceName") String serviceName) {
        return innerAppService.synchronousRadarApp(serviceName);
    }

    @RequestMapping(path = "/milog/down/file", method = "get")
    public Result<String> downLoadFile() {
        return milogDictionaryService.downLoadFile();
    }


    @RequestMapping(path = "/milog/fix/tail/milog/appId", method = "get")
    public Result<String> fixLogTailMilogAppId(@RequestParam("appName") String appName) {
        return milogDictionaryService.fixLogTailMilogAppId(appName);
    }
}
