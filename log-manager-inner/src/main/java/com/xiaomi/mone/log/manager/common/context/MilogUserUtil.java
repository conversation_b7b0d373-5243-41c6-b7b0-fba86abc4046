package com.xiaomi.mone.log.manager.common.context;

import com.xiaomi.mone.enums.InnerProjectSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2021/9/14 10:58
 */
@Slf4j
public class MilogUserUtil {

    /**
     * 用户是否是中国区
     *
     * @return
     */
    public static boolean isChina() {
        try {
            return StringUtils.equalsIgnoreCase(MoneUserContext.getCurrentUser().getZone(), InnerProjectSourceEnum.CHINA_SOURCE.getSource());
        } catch (Exception e) {
            log.error("query user zone error,is china", e);
        }
        return false;
    }

    /**
     * 用户是否是有品
     *
     * @return
     */
    public static boolean isYoupin() {
        try {
            return StringUtils.equalsIgnoreCase(MoneUserContext.getCurrentUser().getZone(), InnerProjectSourceEnum.YOUPIN_SOURCE.getSource());
        } catch (Exception e) {
            log.error("query user zone error, is youpin", e);
        }
        return false;
    }
}
