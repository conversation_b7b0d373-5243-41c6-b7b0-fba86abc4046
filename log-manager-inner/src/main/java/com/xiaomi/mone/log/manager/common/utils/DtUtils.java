package com.xiaomi.mone.log.manager.common.utils;

import com.google.common.collect.Maps;
import com.google.common.io.ByteStreams;
import com.google.gson.Gson;
import com.xiaomi.data.push.client.HttpClientV6;
import com.xiaomi.youpin.docean.Ioc;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.ozhera.log.common.Config;
import org.apache.ozhera.log.manager.common.exception.MilogManageException;

@Slf4j
public class DtUtils {
    public static String URL_TABLE_DELETE = "/openapi/metadata/table/table/delete";
    public static String URL_TABLE_ES_CREATE = "/openapi/metadata/table/es/create";
    public static String URL_TABLE_TALOS_CREATE = "/openapi/metadata/table/talos/create";
    public static String URL_TABLE_LIST_QUERY = "/openapi/metadata/table/authorized/list";
    public static String URL_TABLE_DETAIL_QUERY = "/openapi/metadata/table/get";
    public static String URL_JOB_LIST_QUERY = "/openapi/develop/jobs/op/list";
    public static String URL_JOB_DETAIL_QUERY = "/openapi/develop/jobs/%s";
    public static String URL_JOB_DELETE = "/openapi/develop/jobs/%s/delete/force";
    public static String URL_JOB_START = "/openapi/develop/jobs/%s/start";
    public static String URL_JOB_STOP = "/openapi/develop/jobs/%s/stop";
    public static String URL_TOKEN_DETAIL = "/openapi/workspace/token/detail";
    //
    public static Long DEFAULT_TIMEOUT_PERIOD = TimeUnit.MINUTES.toMillis(1);

    private static Gson gson = new Gson();

    public static <T> T get(String url, String token, Class<T> resultType) {
        url = Config.ins().get("dt_domain", "https://api-gateway.dp.pt.xiaomi.com") + url;
        Map<String, String> headerMap = Maps.newHashMap();
        headerMap.put("Content-Type", "application/x-www-form-urlencoded");
        headerMap.put("Authorization", String.format("workspace-token/1.0 %s", token));
        String returnGet = "";
        T result = null;
        try {
            returnGet = HttpClientV6.get(url, headerMap, DEFAULT_TIMEOUT_PERIOD.intValue());
            result = gson.fromJson(returnGet, resultType);
        } catch (Exception exception) {
            log.error(String.format("[Error] get request to datum failed, url: %s, response: %s", url, returnGet), exception);
        }
        return result;
    }

    public static <T> T query(String url, String token, Class<T> resultType) {
        url = Config.ins().get("dt_domain", "https://api-gateway.dp.pt.xiaomi.com") + url;
        OkHttpClient client = Ioc.ins().getBean(OkHttpClient.class);

        Request request = createRequest(url, String.format("workspace-token/1.0 %s", token));

        String returnGet = "";
        T result = null;
        try {
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                returnGet = response.body().string();
                result = gson.fromJson(returnGet, resultType);
            } else {
                log.info("Request was not successful. Response code: {}", response.code());
            }
        } catch (Exception exception) {
            log.error(String.format("[Error] get request to datum failed, url: %s, response: %s", url, returnGet), exception);
        }
        return result;
    }

    private static Request createRequest(String url, String authorizationHeader) {
        return new Request.Builder()
                .url(url)
                .header("Authorization", authorizationHeader)
                .build();
    }


    public static <T> T post(String url, String body, String token, Class<T> resultType) {
        url = Config.ins().get("dt_domain", "https://api-gateway.dp.pt.xiaomi.com") + url;
        Map<String, String> headerMap = Maps.newHashMap();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Authorization", String.format("workspace-token/1.0 %s", token));
        String returnPost = "";
        T result = null;
        try {
            returnPost = post(url, body.getBytes(), headerMap, DEFAULT_TIMEOUT_PERIOD.intValue());
            result = gson.fromJson(returnPost, resultType);
        } catch (Exception exception) {
            log.error(String.format("[Error] post request to datum failed, url: %s, response: %s, body: %s", url, returnPost, body), exception);
            throw new MilogManageException(exception);
        }
        return result;
    }

    /**
     * topic 命名："hera_topic_" + storeId + "_" + tailId
     *
     * @param storeId
     * @param tailId
     * @return
     */
    public static String buildTalosTopic(Long storeId, Long tailId) {
        return Config.ins().get("hera_topic_prefix", "hera_topic_") + storeId + "_" + tailId;
    }

    /**
     * // 创建 es 索引，命名为：hera_index_ + store 的 id
     *
     * @param storeId
     * @return
     */
    public static String buildEsIndex(Long storeId) {
        return Config.ins().get("hera_es_prefix", "hera_index_") + storeId;
    }

    public static String post(String url, byte[] body, Map<String, String> headers, int timeout) {
        HttpURLConnection conn = null;
        String urlPrefix = Config.ins().get("dt_domain", "https://api-gateway.dp.pt.xiaomi.com");
        if (!url.startsWith(urlPrefix)) {
            url = urlPrefix + url;
        }
        String var14;
        try {
            conn = (HttpURLConnection) (new URL(url)).openConnection();
            conn.setRequestMethod("POST");
            conn.setConnectTimeout(timeout);
            conn.setReadTimeout(timeout);
            conn.setDoOutput(true);
            conn.setDoInput(true);
            if (null != headers) {
                Iterator var7 = headers.entrySet().iterator();
                while (var7.hasNext()) {
                    Map.Entry<String, String> entry = (Map.Entry) var7.next();
                    conn.addRequestProperty((String) entry.getKey(), (String) entry.getValue());
                }
            }
            conn.addRequestProperty("Connection", "close");
            conn.getOutputStream().write(body);
            int code = conn.getResponseCode();
            if (code == 200) {
                var14 = new String(ByteStreams.toByteArray(conn.getInputStream()));
            } else {
                var14 = new String(ByteStreams.toByteArray(conn.getErrorStream()));
                throw new RuntimeException(var14);
            }
        } catch (Exception var12) {
            log.warn("http client v2 error:{}", var12.getMessage());
            throw new RuntimeException(var12.getMessage());
        } finally {
            if (null != conn) {
                conn.disconnect();
            }
        }

        return var14;
    }

    public static String put(String url, String jsonBody, Map<String, String> headers) {
        String urlPrefix = Config.ins().get("dt_domain", "https://api-gateway.dp.pt.xiaomi.com");
        if (!url.startsWith(urlPrefix)) {
            url = urlPrefix + url;
        }
        OkHttpClient okHttpClient = Ioc.ins().getBean(OkHttpClient.class);
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");

        RequestBody requestBody = RequestBody.create(mediaType, jsonBody);

        try {
            Request request = new Request.Builder()
                    .url(url)
                    .put(requestBody)
                    .headers(Headers.of(headers))
                    .build();
            Response response = okHttpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                return "success";
            }
        } catch (Exception e) {
            log.info("put request error", e);
        }
        return null;
    }

    public static String delete(String url, Map<String, String> headers) {
        String urlPrefix = Config.ins().get("dt_domain", "https://api-gateway.dp.pt.xiaomi.com");
        if (!url.startsWith(urlPrefix)) {
            url = urlPrefix + url;
        }
        OkHttpClient okHttpClient = Ioc.ins().getBean(OkHttpClient.class);

        try {
            Request request = new Request.Builder()
                    .url(url)
                    .headers(Headers.of(headers))
                    .delete()
                    .build();
            Response response = okHttpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                return "success";
            }
        } catch (Exception e) {
            log.info("put request error", e);
        }
        return null;
    }

    public static String postNoParam(String url, Map<String, String> headers) {
        String urlPrefix = Config.ins().get("dt_domain", "https://api-gateway.dp.pt.xiaomi.com");
        if (!url.startsWith(urlPrefix)) {
            url = urlPrefix + url;
        }
        OkHttpClient okHttpClient = Ioc.ins().getBean(OkHttpClient.class);

        try {
            Request request = new Request.Builder()
                    .url(url)
                    .headers(Headers.of(headers))
                    .post(RequestBody.create(null, new byte[0]))
                    .build();
            Response response = okHttpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                return "success";
            }
        } catch (Exception e) {
            log.info("put request error", e);
        }
        return null;
    }

}
