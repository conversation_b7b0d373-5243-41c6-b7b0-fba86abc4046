package com.xiaomi.mone.log.manager.common.utils;

import com.google.gson.JsonParser;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/12/1 16:49
 */
public class CommonUtil {

    public static String encodeParam(String param) throws UnsupportedEncodingException {
        return URLEncoder.encode(param, "UTF-8");
    }

    /**
     * 判断是否为json
     *
     * @param str
     * @return
     */
    public static boolean isJson(String str) {
        try {
            JsonParser.parseString(str);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
