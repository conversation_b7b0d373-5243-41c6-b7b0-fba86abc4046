package com.xiaomi.mone.log.manager.model;

import lombok.Builder;
import lombok.Data;
import org.apache.ozhera.log.manager.model.BaseCommon;
import org.nutz.dao.entity.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Table("milog_log_storage")
@Comment("日志存储ES用量统计")
@Data
@Builder
public class LogStorageDO extends BaseCommon {
    @Id
    @Comment("主键Id")
    @ColDefine(customType = "bigint")
    private Long id;

    /**
     * The log data is generated on the day yyyy.MM.dd
     */
    @Column(value = "day")
    @ColDefine(type = ColType.VARCHAR, width = 50)
    @Comment("日期")
    private String day;

    @Column(value = "es_index")
    @ColDefine(type = ColType.VARCHAR, width = 256)
    @Comment("es 索引名")
    private String esIndex;

    @Column(value = "store_ids")
    @ColDefine(type = ColType.VARCHAR, width = 1024)
    @Comment("关联的 log_store id")
    private List<Long> storeIds;

    @Column(value = "machine_room")
    @ColDefine(type = ColType.VARCHAR, width = 50)
    @Comment("机房信息")
    private String machineRoom;

    @Column(value = "storage_bytes_total")
    @ColDefine(type = ColType.INT)
    @Comment("索引截止至当天（包含当天）的累计存储量")
    private Long storageBytesTotal;

    @Column(value = "storage_bytes")
    @ColDefine(type = ColType.INT)
    @Comment("索引当天分片存储量")
    private Long storageBytes;

    @Column(value = "line_avg_size_total")
    @ColDefine(type = ColType.FLOAT)
    @Comment("索引截止至当天（包含当天）的平均日志长度（单位字节）")
    private Double lineAvgSizeTotal;

    @Column(value = "line_avg_size")
    @ColDefine(type = ColType.FLOAT)
    @Comment("索引当天分片的平均日志长度（单位字节）")
    private Double lineAvgSize;


    public LogStorageDO() {
    }

    public LogStorageDO(Long id, String day, String esIndex, List<Long> storeIds,
                        String machineRoom, Long storageBytesTotal, Long storageBytes,
                        Double lineAvgSizeTotal, Double lineAvgSize
    ) {
        this.id = id;
        this.day = day;
        this.esIndex = esIndex;
        this.storeIds = storeIds;
        this.machineRoom = machineRoom;
        this.storageBytesTotal = storageBytesTotal;
        this.storageBytes = storageBytes;
        this.lineAvgSizeTotal = lineAvgSizeTotal;
        this.lineAvgSize = lineAvgSize;

    }


}
