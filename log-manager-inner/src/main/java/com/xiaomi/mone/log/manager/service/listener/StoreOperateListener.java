package com.xiaomi.mone.log.manager.service.listener;

import com.google.gson.Gson;
import com.xiaomi.youpin.docean.Ioc;
import com.xiaomi.youpin.docean.anno.Component;
import com.xiaomi.youpin.docean.listener.Listener;
import com.xiaomi.youpin.docean.listener.event.Event;
import com.xiaomi.youpin.docean.listener.event.EventType;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.manager.model.pojo.MilogLogStoreDO;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.MACHINE_LOKI_CLUSTERMQ_MAP;
import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.STORE_PERIOD_MQ_PREFIX;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/2/29 10:14
 */
@Component
@Slf4j
public class StoreOperateListener implements Listener {

    public static final String OPERATE_FLAG = "operate-store";

    @Resource
    private Gson gson;

    @Resource
    private DefaultMQProducer defaultMQProducer;

    @Value("$store_operate_event_topic")
    private String storeOperateEventTopic;

    @Value("$server.type")
    private String serverType;

    @Override
    public void onEvent(Event event) {
        if (event.getEventType().equals(EventType.custom) && StringUtils.equals(OPERATE_FLAG, event.getAttachments().get(OPERATE_FLAG).toString())) {
            StoreOperateEvent storeOperateEvent = (StoreOperateEvent) event;
            log.info("received store operate event: {}", gson.toJson(storeOperateEvent));
            sendStorePeriodData(storeOperateEvent);
        }
    }

    private void sendStorePeriodData(StoreOperateEvent storeOperateEvent) {
        StorePeriodData storePeriodData = buildStorePeriodData(storeOperateEvent);
        String mqMessage = gson.toJson(storePeriodData);
        Message message = new Message(storeOperateEventTopic, mqMessage.getBytes(StandardCharsets.UTF_8));
        try {
            SendResult sendResult = defaultMQProducer.send(message);
            log.info("Sent message to topic '{}' successfully,MessageId:{}", storeOperateEventTopic, sendResult.getMsgId());
        } catch (Exception e) {
            log.error("Failed to send message to topic '{}'", storeOperateEventTopic, e);
        }
    }

    private StorePeriodData buildStorePeriodData(StoreOperateEvent storeOperateEvent) {
        MilogLogStoreDO storeDO = storeOperateEvent.getStoreDO();
        StorePeriodData storePeriodData = new StorePeriodData();
        storePeriodData.setSpaceId(storeDO.getSpaceId());
        storePeriodData.setStoreId(storeDO.getId());
        storePeriodData.setUniqueId(String.format("%s_%s_%s", STORE_PERIOD_MQ_PREFIX,
                storeDO.getSpaceId(), storeDO.getId()));
        storePeriodData.setStoreName(storeDO.getLogstoreName());
        storePeriodData.setStorePeriod(storeDO.getStorePeriod());
        storePeriodData.setOperateCode(storeOperateEvent.getOperateEnum().getCode());
        storePeriodData.setOperateDesc(storeOperateEvent.getOperateEnum().getDescribe());

        storePeriodData.setMachineRoom(storeDO.getMachineRoom());
        if ("staging".equals(serverType)) {
            storePeriodData.setCluster(MACHINE_LOKI_CLUSTERMQ_MAP.get("staging"));
        } else {
            storePeriodData.setCluster(MACHINE_LOKI_CLUSTERMQ_MAP.get(storeDO.getMachineRoom()));
        }

        storePeriodData.setCtime(storeDO.getCtime());
        storePeriodData.setUtime(storeDO.getUtime());
        storePeriodData.setCreator(storeDO.getCreator());
        storePeriodData.setUpdater(storeDO.getUpdater());
        return storePeriodData;
    }


    public static void regStoreOperateListener() {
        StoreOperateListener storeOperateListener = Ioc.ins().getBean(StoreOperateListener.class);
        Ioc.ins().regListener(storeOperateListener);
    }
}
