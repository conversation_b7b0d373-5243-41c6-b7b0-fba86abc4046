package com.xiaomi.mone.log.manager.model.dto;

import com.xiaomi.mone.enums.InnerProjectTypeEnum;
import lombok.Builder;
import lombok.Data;

import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class ResourceBillDetailDTO {

    private Long chargeMilogAppId;
    private String chargeAppName;

    private HashSet<TailDetail> tailDetails;


    @Data
    @Builder
    public static class TailDetail {
        private Long storeId;
        private Long tailId;
        private String tailName;
        //应用中心id，全局唯一
        private Long milogAppId;
        //元数据id，来源方唯一
        private Long appId;
        //应用名
        private String appName;
        //应用类型
        private InnerProjectTypeEnum appType;

        //前端访问链接
        private String storeDetailUrl;

        //前端访问链接
        private String tailDetailUrl;

        public TailDetail() {
        }

        public TailDetail(Long storeId, Long tailId, String tailName, Long milogAppId,
                          Long appId, String appName, InnerProjectTypeEnum appType,
                          String storeDetailUrl, String tailDetailUrl) {
            this.storeId = storeId;
            this.tailId = tailId;
            this.tailName = tailName;
            this.milogAppId = milogAppId;
            this.appId = appId;
            this.appName = appName;
            this.appType = appType;
            this.storeDetailUrl = storeDetailUrl;
            this.tailDetailUrl = tailDetailUrl;
        }
    }

    public ResourceBillDetailDTO() {

    }

    public ResourceBillDetailDTO(Long chargeMilogAppId, String chargeAppName, HashSet<TailDetail> tailDetails) {
        this.chargeMilogAppId = chargeMilogAppId;
        this.chargeAppName = chargeAppName;
        this.tailDetails = tailDetails;
    }
}
