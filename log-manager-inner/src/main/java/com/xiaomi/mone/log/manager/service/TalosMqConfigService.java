package com.xiaomi.mone.log.manager.service;

import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.xiaomi.bigdata.workshop.model.ColumnDTO2;
import com.xiaomi.bigdata.workshop.model.FieldType2;
import com.xiaomi.bigdata.workshop.model.TalosCreateDTO;
import com.xiaomi.bigdata.workshop.model.WsTableDTO;
import com.xiaomi.infra.galaxy.rpc.thrift.Credential;
import com.xiaomi.infra.galaxy.rpc.thrift.Grantee;
import com.xiaomi.infra.galaxy.rpc.thrift.UserType;
import com.xiaomi.infra.galaxy.talos.admin.TalosAdmin;
import com.xiaomi.infra.galaxy.talos.client.TalosClientConfig;
import com.xiaomi.infra.galaxy.talos.thrift.*;
import com.xiaomi.mone.enums.InnerProjectTypeEnum;
import com.xiaomi.mone.log.manager.common.utils.DtUtils;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import libthrift091.TException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.manager.common.Utils;
import org.apache.ozhera.log.manager.common.exception.MilogManageException;
import org.apache.ozhera.log.manager.dao.MilogAppMiddlewareRelDao;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.model.dto.DictionaryDTO;
import org.apache.ozhera.log.manager.model.pojo.MilogAppMiddlewareRel;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;
import org.apache.ozhera.log.manager.model.pojo.MilogMiddlewareConfig;
import org.apache.ozhera.log.manager.service.impl.HeraAppServiceImpl;
import org.apache.ozhera.log.utils.PinYin4jUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.apache.ozhera.log.common.Constant.*;
import static org.apache.rocketmq.common.MixAll.DEFAULT_CONSUMER_GROUP;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2021/9/23 11:32
 */
@Service
@Slf4j
public class TalosMqConfigService implements InnerMqConfigService {

    private static final int PARTITION_NUMBER = 1;
    private Map<String, TalosAdmin> talosAdminMap = new HashMap<>();

    @Resource
    private MilogLogTailDao milogLogtailDao;
    @Resource
    private MilogAppMiddlewareRelDao middlewareRelDao;
    @Resource
    private HeraAppServiceImpl heraAppService;

    @Value("$server.type")
    private String serverType;

    private Gson gson = new Gson();

    @Override
    public MilogAppMiddlewareRel.Config generateConfig(String ak, String sk, String nameServer, String serviceUrl,
                                                       String authorization, String orgId, String teamId, Long appId,
                                                       String appName, String source, Long tailId) {

        List<DictionaryDTO> dictionaryDTOS = queryExistsTopic(ak, sk, nameServer, serviceUrl, authorization, orgId, teamId);
        List<String> topics = dictionaryDTOS.stream().map(dictionaryDTO -> (String) dictionaryDTO.getValue()).collect(Collectors.toList());
        String commonTopic = queryCommonTopic(topics, orgId);

        if (StringUtils.isNotBlank(commonTopic) && queryIsChinaAppInfo(tailId) && serverType.equals("staging")) {
            return instantiateConfig(commonTopic);
        }
        String existTopic = isExistTopic(topics, appId, appName, source, tailId);
        if (StringUtils.isNotBlank(existTopic)) {
            return instantiateConfig(existTopic);
        }
        String notRealTopicName = generateTopicName(orgId, appId, appName, source, tailId);
        return instantiateConfig(createRealityTopicName(serviceUrl, ak, sk, teamId, notRealTopicName,
                null, PARTITION_NUMBER));
    }

    private boolean queryIsChinaAppInfo(Long tailId) {
        MilogLogTailDo milogLogTailDo = milogLogtailDao.queryById(tailId);
        if (Objects.equals(InnerProjectTypeEnum.MIONE_TYPE.getCode(), milogLogTailDo.getAppType())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private String createRealityTopicName(String serviceUrl, String ak, String sk, String teamId,
                                          String topicName, Map<String, String> attributes,
                                          Integer partitionNumber) {
        CreateTopicRequest topicRequest = createTopicRequest(topicName.trim(), teamId, partitionNumber);
        if (null != attributes && !attributes.isEmpty()) {
            TopicAttribute topicAttribute = new TopicAttribute()
                    .setPartitionNumber(partitionNumber)
                    .setAttributes(attributes);
            topicRequest.setTopicAttribute(topicAttribute);
        }
        TalosAdmin talosAdmin = generateTalosAdmin(serviceUrl, ak, sk);
        String reality = "";
        try {
            CreateTopicResponse talosAdminTopic = talosAdmin.createTopic(topicRequest);
            log.info("talos create topic finished,ak:{},sk:{},topic:{},talosAdminTopic:{}", ak, sk, topicName, talosAdminTopic);
            reality = talosAdminTopic.getTopicInfo().topicName;
        } catch (GalaxyTalosException e) {
            log.error(String.format("talos create topic error,ak:%s,sk:%s,topic:%s,message:%s", ak, sk, topicName, e.details), e);
            throw new MilogManageException(e.getMessage());
        } catch (TException e) {
            log.error(String.format("talos create topic error,ak:%s,sk:%s,topic:%s", ak, sk, topicName), e);
            throw new MilogManageException(e.getMessage());
        }
        return reality;
    }

    private String updateRealityTopicName(String serviceUrl, String ak, String sk,
                                          String topicName, Map<String, String> attributes) {
        TopicAttribute topicAttribute = new TopicAttribute();
        ChangeTopicAttributeRequest request = new ChangeTopicAttributeRequest();
        if (null != attributes && !attributes.isEmpty()) {
            topicAttribute.setAttributes(attributes);
            request.setTopicAttribute(topicAttribute);
        }
        TalosAdmin talosAdmin = generateTalosAdmin(serviceUrl, ak, sk);
        try {
            request.setTopicTalosResourceName(talosAdmin.getTopicAttribute(new GetTopicAttributeRequest(
                    topicName)).getTopicTalosResourceName());
            talosAdmin.changeTopicAttribute(request);
            log.info("talos update topic finished,ak:{},sk:{},topic:{}", ak, sk, topicName);
        } catch (GalaxyTalosException e) {
            log.error(String.format("talos update topic error,ak:%s,sk:%s,topic:%s,message:%s", ak, sk, topicName, e.details), e);
            throw new MilogManageException(e.getMessage());
        } catch (TException e) {
            log.error(String.format("talos update topic error,ak:%s,sk:%s,topic:%s", ak, sk, topicName), e);
            throw new MilogManageException(e.getMessage());
        }
        return topicName;
    }

    private TalosAdmin generateTalosAdmin(String serviceUrl, String ak, String sk) {
        TalosAdmin talosAdmin = talosAdminMap.get(generateTalosAdminKey(serviceUrl, ak, sk));
        if (null == talosAdmin) {
            talosAdmin = talosAdminGenerate(serviceUrl, ak, sk);
        }
        return talosAdmin;
    }

    public CreateTopicRequest createTopicRequest(String topicName, String teamId, Integer partitionNumber) {
        TopicAttribute topicAttribute = new TopicAttribute()
                .setPartitionNumber(partitionNumber);
        Grantee grant = new Grantee().setIdentifier(teamId);
        Map<Grantee, Permission> aclMap = new HashMap<>(1);
        aclMap.put(grant, Permission.TOPIC_READ_AND_MESSAGE_FULL_CONTROL);
        CreateTopicRequest request = new CreateTopicRequest()
                .setTopicName(topicName)
                .setTopicAttribute(topicAttribute)
                .setAclMap(aclMap);
        return request;
    }

    private MilogAppMiddlewareRel.Config instantiateConfig(String topicName) {
        MilogAppMiddlewareRel.Config config = new MilogAppMiddlewareRel.Config();
        config.setTopic(topicName);
        config.setPartitionCnt(PARTITION_NUMBER);
        return config;
    }

    private String isExistTopic(List<String> topics, Long appId, String appName,
                                String source, Long tailId) {
        String simpleTopicName = generateSimpleTopicName(appId, appName, source, tailId);
        if (topics.contains(simpleTopicName)) {
            return simpleTopicName;
        }
        return StringUtils.EMPTY;
    }

    /**
     * 这个应该取使用次数少的，不然会影响单个topic消费数据的能力
     *
     * @param existTopics
     * @param orgId
     * @return
     */
    private String queryCommonTopic(List<String> existTopics, String orgId) {
        if (CollectionUtils.isNotEmpty(existTopics)) {
            List<String> commonTopics = existTopics.stream()
                    .filter(s -> s.startsWith(String.format("%s_%s", COMMON_MQ_PREFIX, orgId)))
                    .sorted((a, b) -> middlewareRelDao.queryCountByTopicName(a)
                            .compareTo(middlewareRelDao.queryCountByTopicName(b)))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(commonTopics)) {
                return commonTopics.get(0);
            }
        }
        return StringUtils.EMPTY;
    }

    @Override
    public List<DictionaryDTO> queryExistsTopic(String ak, String sk, String nameServer, String serviceUrl, String authorization, String orgId, String teamId) {
        TalosAdmin talosAdmin = talosAdminMap.get(generateTalosAdminKey(serviceUrl, ak, sk));
        if (null == talosAdmin) {
            talosAdmin = talosAdminGenerate(serviceUrl, ak, sk);
        }
        List<DictionaryDTO> dictionaryDTOS = Lists.newArrayList();
        try {
            List<Topic> topicInfos = talosAdmin.getTopicList();
            dictionaryDTOS = topicInfos.stream().map(topicInfo -> {
                DictionaryDTO<String> dictionaryDTO = new DictionaryDTO<>();
                dictionaryDTO.setValue(topicInfo.getTopicInfo().topicName);
                dictionaryDTO.setLabel(topicInfo.getTopicInfo().topicName);
                return dictionaryDTO;
            }).collect(Collectors.toList());
        } catch (TException e) {
            log.error(String.format("talos query exist topic error,serviceUrl:%s,ak:%s,sk:%s", serviceUrl, ak, sk), e);
        }
        return dictionaryDTOS;
    }

    /**
     * 1.生成3个公共topic的名称
     * 2.查询是否存在，不存在创建，存在开启tag过滤
     */
    @Override
    public List<String> createCommonTagTopic(String ak, String sk, String nameServer, String serviceUrl, String authorization, String orgId, String teamId) {
        List<String> commonTagTopicNames = generateCommonTagTopicName(orgId);
        List<DictionaryDTO> dictionaryDTOS = queryExistsTopic(ak, sk, nameServer, serviceUrl, authorization, orgId, teamId);

        for (String commonTagTopicName : commonTagTopicNames) {
            Optional<DictionaryDTO> commonTagTopicNameOptional = dictionaryDTOS.stream().filter(dictionaryDTO ->
                    Objects.equals(commonTagTopicName, dictionaryDTO.getValue())
            ).findFirst();
            Map<String, String> attributes = Maps.newHashMap();
//            attributes.put("tag", "true");
            String topicNameWrap = String.format("%s/%s", orgId, commonTagTopicName);

            if (!commonTagTopicNameOptional.isPresent()) {
                createRealityTopicName(serviceUrl, ak, sk, teamId, topicNameWrap, attributes, COMMON_MQ_PARTITION_NUM);
            }
            if (commonTagTopicNameOptional.isPresent()) {
//                updateRealityTopicName(serviceUrl, ak, sk, commonTagTopicName, attributes);
            }
        }
        return commonTagTopicNames;
    }

    @Override
    public boolean CreateGroup(String s, String s1, String s2) {
        return false;
    }

    @Override
    public List<String> generateCommonTagTopicName(String orgId) {
        return IntStream.range(0, COMMON_MQ_SUFFIX.size()).mapToObj(value ->
                        String.format("%s_%s_%s", COMMON_MQ_PREFIX, orgId, COMMON_MQ_SUFFIX.get(value)))
                .collect(Collectors.toList());
    }

    private String generateTopicName(String orgId, Long appId, String appName, String source, Long tailId) {
        return STR."\{orgId}/\{generateSimpleTopicName(appId, appName, source, tailId)}";
    }

    @Override
    public String generateSimpleTopicName(Long appId, String appName, String source, Long tailId) {
        if (StringUtils.isNotEmpty(appName)) {
            // 汉字转拼音
            appName = PinYin4jUtils.getAllPinyin(appName);
        }
        // 处理特殊字符
        List<String> collect = ReUtil.RE_KEYS.stream()
                .map(character -> character.toString()).collect(Collectors.toList());
        String topicName = String.format("%s_%s_%s_%s", appId, appName, tailId, source);
        topicName = StrUtil.removeAny(topicName, collect.toArray(new String[0]));
        if (topicName.length() > 80) {
            topicName = topicName.substring(0, 80);
        }
        if (topicName.endsWith("-")) {
            topicName = topicName.substring(0, topicName.length() - 1);
        }
        return topicName;
    }

    public TalosAdmin talosAdminGenerate(String serviceUrl, String ak, String sk) {
        Properties properties = new Properties();
        properties.setProperty("galaxy.talos.service.endpoint", serviceUrl);
        properties.setProperty("galaxy.talos.client.falcon.monitor.switch", "false");
        TalosClientConfig clientConfig = new TalosClientConfig(properties);
        // credential
        Credential credential = new Credential();
        credential.setSecretKeyId(ak)
                .setSecretKey(sk)
                .setType(UserType.DEV_XIAOMI);
        // init admin
        TalosAdmin talosAdmin = new TalosAdmin(clientConfig, credential);
        talosAdminMap.put(generateTalosAdminKey(serviceUrl, ak, sk), talosAdmin);
        return talosAdmin;
    }

    private String generateTalosAdminKey(String serviceUrl, String ak, String sk) {
        return String.format("%s_%s_%s", serviceUrl, ak, sk);
    }


    public boolean deleteTopic(String serviceUrl, String ak, String sk, String topicName) {
        TalosAdmin talosAdmin = talosAdminMap.get(generateTalosAdminKey(serviceUrl, ak, sk));
        if (null == talosAdmin) {
            talosAdmin = talosAdminGenerate(serviceUrl, ak, sk);
        }
        try {
            Topic topic = talosAdmin.describeTopic(new DescribeTopicRequest(topicName));
            TopicTalosResourceName resourceName = topic.getTopicInfo().getTopicTalosResourceName();
            DeleteTopicRequest deleteTopicRequest = new DeleteTopicRequest();
            deleteTopicRequest.setTopicTalosResourceName(resourceName);
            talosAdmin.deleteTopic(deleteTopicRequest);
            return true;
        } catch (TException e) {
            log.info(String.format("删除topic异常，topicName:%s", topicName), e);
        }
        return false;
    }

    public boolean deleteConsumerGroup(Long tailId, String serviceUrl, String ak, String sk, String topicName) {
        TalosAdmin talosAdmin = talosAdminMap.get(generateTalosAdminKey(serviceUrl, ak, sk));
        if (null == talosAdmin) {
            talosAdmin = talosAdminGenerate(serviceUrl, ak, sk);
        }
        TopicTalosResourceName topicTalosResourceName;
        try {
            topicTalosResourceName = getTopicTalosResourceName(talosAdmin, topicName);

            QueryConsumerGroupResponse listTopicGroupResponse = talosAdmin.queryConsumerGroup(new QueryConsumerGroupRequest(topicTalosResourceName));
            Set<String> consumerGroupList = listTopicGroupResponse.consumerGroupList;

            List<String> delGroups = consumerGroupList.stream()
                    .filter(data -> data.endsWith(String.valueOf(tailId)) || data.endsWith(tailId + "_backup"))
                    .toList();
            for (String delGroup : delGroups) {
                talosAdmin.deleteConsumerGroup(new DeleteConsumerGroupRequest(delGroup, topicTalosResourceName));
                log.info("deleteConsumerGroup success,topic:{},consumerGroup:{}", topicName, delGroup);
            }
        } catch (TException e) {
            log.error("deleteConsumerGroup error,tailId:{}", tailId, e);
        }
        return false;
    }

    private TopicTalosResourceName getTopicTalosResourceName(TalosAdmin talosAdmin, String topicName) throws TException {
        GetDescribeInfoResponse response = talosAdmin.getDescribeInfo(
                new GetDescribeInfoRequest(topicName));
        TopicTalosResourceName resourceName = response.getTopicTalosResourceName();
        return resourceName;
    }


    @Override
    public MilogAppMiddlewareRel.Config generatePlatformConfig(Long spaceId, Long storeId, Long tailId, MilogMiddlewareConfig milogMiddlewareConfig) {
        // 创建 talos topic
        String topicName = createPlatformTopic(storeId, tailId, milogMiddlewareConfig);

        // 生成 config
        String consumerGroup = DEFAULT_CONSUMER_GROUP + Utils.createTag(spaceId, storeId, tailId);
        String tag = DEFAULT_TAGS + spaceId + UNDERLINE_SYMBOL + storeId + UNDERLINE_SYMBOL + tailId;
        return instantiatePlatformConfig(topicName, consumerGroup, tag);
    }

    public String createPlatformTopic(Long storeId, Long tailId, MilogMiddlewareConfig milogMiddlewareConfig) {
        String topicName = DtUtils.buildTalosTopic(storeId, tailId);
        String url = DtUtils.URL_TABLE_TALOS_CREATE;
        List<ColumnDTO2> columnDTOs = generateExampleColumnDTOs();
        TalosCreateDTO talosCreateDTO = new TalosCreateDTO();
        talosCreateDTO.name(topicName)
                .partitionNum(PARTITION_NUMBER)
                .catalog(milogMiddlewareConfig.getDtCatalog())
                .dbName(milogMiddlewareConfig.getDtDatabase())
                .preserveTime(24)    // Hera 平台创建的 topic 按 24 小时
                .serializationLib("json")
                .description("talos topic for hera logtail: " + tailId)
                .columnDtos(columnDTOs);
        String body = gson.toJson(talosCreateDTO);
        WsTableDTO wsTableDTO = DtUtils.post(url, body, milogMiddlewareConfig.getToken(), WsTableDTO.class);
        if (null == wsTableDTO) {
            log.error("创建使用平台资源的 logstore 下 logtail 对应的 talos topic 失败， tail: " + tailId + ", topicName: " + topicName);
            return "";
        }
        return topicName;
    }

    /**
     * 伪造一个 talos topic 字段，创建 topic 表时字段必填，但与工场同学沟通得知实际只用于工场内部统计
     *
     * @return
     */
    private List<ColumnDTO2> generateExampleColumnDTOs() {
        List<ColumnDTO2> columnDTO2List = new ArrayList<>();
        ColumnDTO2 columnDTO2 = new ColumnDTO2();
        columnDTO2.fieldName("message").type(new FieldType2().type("string")).isKey(false);
        columnDTO2List.add(columnDTO2);
        return columnDTO2List;
    }

    private MilogAppMiddlewareRel.Config instantiatePlatformConfig(String topicName, String consumerGroup, String tag) {
        MilogAppMiddlewareRel.Config config = new MilogAppMiddlewareRel.Config();
        config.setTopic(topicName);
        config.setPartitionCnt(PARTITION_NUMBER);
        config.setConsumerGroup(consumerGroup);
        config.setTag(tag);
        return config;
    }

    @Override
    public void deletePlatformTopic(String topicName, MilogMiddlewareConfig milogMiddlewareConfig) {
        String url = DtUtils.URL_TABLE_DELETE;
        List<WsTableDTO> wsTableDTOS = new ArrayList<>();
        WsTableDTO wsTableDTO = new WsTableDTO().catalog(milogMiddlewareConfig.getDtCatalog()).dbName(milogMiddlewareConfig.getDtDatabase()).tableNameEn(topicName);
        wsTableDTOS.add(wsTableDTO);
        String body = gson.toJson(wsTableDTOS);
        Boolean result = DtUtils.post(url, body, milogMiddlewareConfig.getToken(), Boolean.class);
        if (null == result || !result) {
            log.error("删除 matrix 应用类型 logstore 下的 logtail 对应的 topic 失败，topic 信息：" + wsTableDTO.toString());
        }
    }

    @Override
    public MilogAppMiddlewareRel.Config generatePlatformConfigWithoutCreate(Long spaceId, Long storeId, Long tailId, MilogMiddlewareConfig milogMiddlewareConfig) {
        String topicName = DtUtils.buildTalosTopic(storeId, tailId);
        String consumerGroup = DEFAULT_CONSUMER_GROUP + Utils.createTag(spaceId, storeId, tailId);
        String tag = DEFAULT_TAGS + spaceId + UNDERLINE_SYMBOL + storeId + UNDERLINE_SYMBOL + tailId;
        return instantiatePlatformConfig(topicName, consumerGroup, tag);
    }
}
