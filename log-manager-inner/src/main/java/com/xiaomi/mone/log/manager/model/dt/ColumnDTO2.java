package com.xiaomi.mone.log.manager.model.dt;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.xiaomi.bigdata.workshop.model.FieldType2;
import io.swagger.annotations.ApiModelProperty;
import lombok.Setter;

import java.util.Map;
import java.util.Objects;

@Setter
public class ColumnDTO2 {
    @JsonProperty("id")
    private Integer id = null;
    @JsonProperty("fieldName")
    private String fieldName = null;
    @JsonProperty("type")
    private FieldType2 type = null;
    @JsonProperty("comment")
    private String comment = null;
    @JsonProperty("fieldType")
    private String fieldType = null;
    @JsonProperty("isKey")
    private Boolean isKey = null;
    @JsonProperty("extra")
    private Map<String, Object> extra;

    public ColumnDTO2() {
    }

    public ColumnDTO2 extra(Map<String, Object> extra) {
        this.extra = extra;
        return this;
    }

    public Map<String, Object> getExtra() {
        return this.extra;
    }

    public ColumnDTO2 id(Integer id) {
        this.id = id;
        return this;
    }

    @ApiModelProperty("")
    public Integer getId() {
        return this.id;
    }

    public ColumnDTO2 fieldName(String fieldName) {
        this.fieldName = fieldName;
        return this;
    }

    @ApiModelProperty("字段名称，必填，必须为英文字母数字或下划线")
    public String getFieldName() {
        return this.fieldName;
    }

    public ColumnDTO2 type(FieldType2 type) {
        this.type = type;
        return this;
    }

    @ApiModelProperty(
            required = true,
            value = "字段类型"
    )
    public FieldType2 getType() {
        return this.type;
    }

    public ColumnDTO2 comment(String comment) {
        this.comment = comment;
        return this;
    }

    @ApiModelProperty(
            required = true,
            value = "字段描述"
    )
    public String getComment() {
        return this.comment;
    }

    public ColumnDTO2 fieldType(String fieldType) {
        this.fieldType = fieldType;
        return this;
    }

    @ApiModelProperty("已弃用，字段类型")
    public String getFieldType() {
        return this.fieldType;
    }

    public ColumnDTO2 isKey(Boolean isKey) {
        this.isKey = isKey;
        return this;
    }

    @ApiModelProperty("是否为主键")
    public Boolean isIsKey() {
        return this.isKey;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } else if (o != null && this.getClass() == o.getClass()) {
            ColumnDTO2 columnDTO2 = (ColumnDTO2) o;
            return Objects.equals(this.id, columnDTO2.id) && Objects.equals(this.fieldName, columnDTO2.fieldName) && Objects.equals(this.type, columnDTO2.type) && Objects.equals(this.comment, columnDTO2.comment) && Objects.equals(this.fieldType, columnDTO2.fieldType) && Objects.equals(this.isKey, columnDTO2.isKey);
        } else {
            return false;
        }
    }

    public int hashCode() {
        return Objects.hash(new Object[]{this.id, this.fieldName, this.type, this.comment, this.fieldType, this.isKey});
    }

    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ColumnDTO2 {\n");
        sb.append("    id: ").append(this.toIndentedString(this.id)).append("\n");
        sb.append("    fieldName: ").append(this.toIndentedString(this.fieldName)).append("\n");
        sb.append("    type: ").append(this.toIndentedString(this.type)).append("\n");
        sb.append("    comment: ").append(this.toIndentedString(this.comment)).append("\n");
        sb.append("    fieldType: ").append(this.toIndentedString(this.fieldType)).append("\n");
        sb.append("    isKey: ").append(this.toIndentedString(this.isKey)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    private String toIndentedString(Object o) {
        return o == null ? "null" : o.toString().replace("\n", "\n    ");
    }
}
