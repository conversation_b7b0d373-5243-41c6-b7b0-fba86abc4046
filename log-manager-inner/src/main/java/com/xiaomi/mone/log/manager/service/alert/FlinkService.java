package com.xiaomi.mone.log.manager.service.alert;

import com.google.gson.Gson;
import com.xiaomi.infra.galaxy.rpc.thrift.Credential;
import com.xiaomi.infra.galaxy.rpc.thrift.UserType;
import com.xiaomi.infra.galaxy.streaming.jobserver.admin.Admin;
import com.xiaomi.infra.galaxy.streaming.jobserver.client.ClientConfig;
import com.xiaomi.infra.galaxy.streaming.jobserver.thrift.exceptions.GalaxyStreamingJobException;
import com.xiaomi.infra.galaxy.streaming.jobserver.thrift.job.*;
import com.xiaomi.infra.galaxy.streaming.jobserver.thrift.jobserver.*;
import com.xiaomi.mone.log.manager.model.alert.Alert;
import com.xiaomi.mone.log.manager.model.alert.AlertRule;
import com.xiaomi.mone.log.manager.model.bo.alert.AlertParam;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.common.StringUtils;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import com.xiaomi.youpin.mione.FlinkAlertRule;
import com.xiaomi.youpin.mione.FlinkRuleParams;
import libthrift091.TException;
import libthrift091.protocol.TProtocolException;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.ozhera.log.common.Config;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * <AUTHOR>
 */


@Slf4j
@Service
public class FlinkService {
    @Value(value = "$server.type")
    private String serverType;

    @Value(value = "$derora_endpoint")
    public String endpoint;


    @Value(value = "$team.access_key")
    private String secretKeyId;

    @Value(value = "$team.secret_key")
    private String secretKey;

    @Value(value = "$team.id")
    private String teamId;

    @Value(value = "$kerberos")
    private String kerberos;

    @Value(value = "$yarn_queue")
    private String yarnQueue;

    @Value(value = "$flink_cluster")
    private String flinkCluster;

    @Value(value = "$hdfs.file")
    private String hdfsFilePath;

    @Value(value = "$producer.topic")
    private String producerTopic;

    @Value(value = "$producer.server")
    private String producerServer;

    private Admin deroraAdmin;

    private String baseArguments;

    private Gson gson = new Gson();

    @PostConstruct
    public void init() {
        log.info("FlinkService init");
        Properties prop = new Properties();
        prop.setProperty("galaxy.streaming.jobserver.endpoint", endpoint);
        prop.setProperty("galaxy.streaming.jobserver.api.version", "/v3/api");
        ClientConfig clientConfig = new ClientConfig(prop);
        Credential credential = new Credential()
                .setSecretKeyId(secretKeyId)
                .setSecretKey(secretKey)
                .setType(UserType.DEV_XIAOMI);
        deroraAdmin = new Admin(clientConfig, credential);
        baseArguments = "";
        serializeBaseArguments();
    }

    private boolean isOnline() {
        return serverType.equals("online");
    }

    public String getYarnQueue(String department) {
        /*
        if (!isOnline()) {
            return yarnQueue;
        }
        return department != null && department.equals("china") ? chinaYarnQueue : youpinYarnQueue;

         */
        return yarnQueue;
    }

    private void serializeBaseArguments() {
        HashMap<String, String> map = new HashMap<>();
        map.put("producerServer", producerServer);
        map.put("producerTopic", producerTopic);
        map.put("producerAccessKey", secretKeyId);
        map.put("producerSecretKey", secretKey);
        baseArguments = serialize(map);
    }

    private String serialize(Map<String, String> map) {
        StringBuilder builder = new StringBuilder();
        for (Map.Entry<String, String> current : map.entrySet()) {
            builder.append("--");
            builder.append(current.getKey());
            builder.append(" ");
            builder.append(current.getValue());
            builder.append(" ");
        }
        return builder.toString();
    }

    private String serializeFlinkArguments(Map<String, String> map, long alertId, List<AlertRule> alertRules) {
        HashMap<String, String> input = new HashMap<>();
        input.putAll(map);

        input.put("alertRulesInput", processAlertRules(alertId, alertRules));
        return serialize(input);
    }

    private String serializeFlinkArguments(Alert alert, AlertParam params, List<AlertRule> alertRules) {
        HashMap<String, String> input = new HashMap<>();
        input.putAll(alert.getArguments());
//        input.put("accessKey", params.getConsumerAccessKey());
//        input.put("secretKey", params.getConsumerSecretKey());
//        input.put("alertId", String.valueOf(alert.getId()));
//        input.put("consumerServer", params.getConsumerServer());
//        input.put("consumerTopic", params.getConsumerTopic());
//        input.put("mqType", params.getMqType());
//
//        if (params.getMqType().equals("talos") == false) {
//            input.put("consumerGroup", params.getConsumerGroup());
//            input.put("consumerTag", params.getConsumerTag());
//        }

        //    input.put("alertRulesInput", processAlertRules(alert.getId(), alertRules));
        return serialize(input);
    }

    private String processAlertRules(long alertId, List<AlertRule> alertRules) {
        FlinkRuleParams flinkRuleParams = new FlinkRuleParams();
        flinkRuleParams.setAlertId(alertId);
        ArrayList<FlinkAlertRule> flinkAlertRules = new ArrayList<>();
        for (AlertRule current : alertRules) {
            FlinkAlertRule flinkAlertRule = new FlinkAlertRule();
            flinkAlertRule.setRuleId(current.getId());
            flinkAlertRule.setFilterRegex(current.getRegex());
            flinkAlertRules.add(flinkAlertRule);
        }
        flinkRuleParams.setAlertRules(flinkAlertRules);
        flinkRuleParams.setActionTime(System.currentTimeMillis());
        return gson.toJson(flinkRuleParams, FlinkRuleParams.class);
    }

    public DescribeJobResponse queryJob(String jobName) {
        String cluster = flinkCluster;

        DescribeJobRequest describeJobRequest = new DescribeJobRequest()
                .setJobId(new JobId(cluster, jobName))
                .setWithJobInfo(true)
                .setWithJobOptions(true)
                .setWithJobRunInfo(false);
        DescribeJobResponse describeJobResponse = null;
        try {
            describeJobResponse = deroraAdmin.describeJob(describeJobRequest);
            System.out.println(describeJobResponse);
        } catch (TException e) {
            log.error(String.format("query flink job error"), e);
            e.printStackTrace();
        }
        return describeJobResponse;
    }


    public boolean submitJob(Alert alert, AlertParam params, String flinkJobName, List<AlertRule> alertRules) {
        log.info("flink submitJob");

        try {
            String arguments = baseArguments + serializeFlinkArguments(alert, params, alertRules);
            log.info("arugments: " + arguments);
            SubmitJobRequest submitJobRequest = initStreamJobSubmitRequest(flinkJobName, arguments, params.getDepartment());

            log.info("here");
            //调用admin中的submitJob方法,若无异常表示提交成功
            deroraAdmin.submitJob(submitJobRequest);
            log.info("submit flink job success");
            return true;
        } catch (GalaxyStreamingJobException e) {
            //可能原因:参数非法,作业不存在. 详情在"e.getDetails()"中
            log.error("提交flink任务", e);
        } catch (TProtocolException e) {
            //可能原因:有必填字段未赋值. 详情在"e.getMessage()"中
            log.error("提交flink任务", e);
        } catch (TException e) {
            log.error("提交flink任务", e);
        } catch (Exception e) {
            log.error("提交flink任务", e);
        }
        return false;
    }

    private SubmitJobRequest initStreamJobSubmitRequest(String flinkJobName, String arguments,
                                                        String department) {
        //权限信息

        //作业信息
        String jobName = flinkJobName;//作业名: 支持英文与常见符号
        String jobOwner = Config.ins().get("flink_job_director", "wangtao29");
        ;//负责人
        String jobDetails = "日志分析报警任务";//[选填]作业描述
        String jobTags = "";//[选填]标签

        //作业配置
        String driverMemory = "2G";//JobManager Memory | Driver Memory: 对应为Flink作业的JobManager或Spark作业的Driver属性
        int executorNumber = 2;//Parallelism | Num Executors
        String executorMemory = "2G";//TaskManager Memory | Executor Memory
        List<String> appConfigs = new ArrayList<>();//[选填]框架参数: 对应前端页面框架参数的多行key=value参数配置

        String userJarPath = hdfsFilePath;//作业Jar包: 需提前上传到HDFS上
        String userMainClass = "com.xiaomi.mione.log.MilogAlarmBootstrap";//Main Class
        String mainArguments = arguments;
        int maxRetry = 100;//失败重试次数: 系统每2分钟检测一次,作业状态会转为ERROR状态,不再提交

        SubmitType submitType = SubmitType.SUBMIT;

        //仅需配置以上参数

        JobInfo jobInfo = new JobInfo()
                .setJobDescription(new JobDescription()
                        .setJobType(JobType.FLINK_STREAMING)
                        .setJobId(new JobId(flinkCluster, jobName))
                        .setJobOwner(jobOwner)
                        .setJobDetails(jobDetails)
                        .setJobTags(jobTags))
                .setSubmitType(submitType);

        JobOptions jobOptions = new JobOptions()
                .setCommonConfig(new CommonConfig()
                        .setYarnInfo(new YarnInfo()
                                .setKerberosPrincipal(kerberos)
                                .setYarnQueue(getYarnQueue(department))
                                .setDriverMemory(driverMemory)
                                .setExecutorNumber(executorNumber)
                                .setExecutorMemory(executorMemory))
                        .setAppConfigs(appConfigs)
                        .setMaxRetry(maxRetry))
                .setJobConfig(new JobConfig()
                        .setJobExecution(new JobExecution()
                                .setUserJarPath(userJarPath))
                        .setUserMainClass(userMainClass)
                        .setMainArguments(mainArguments));
        return new SubmitJobRequest()
                .setJobInfo(jobInfo)
                .setJobOptions(jobOptions);
    }

    public boolean startFlinkJob(Alert alert) {
        if (alert == null || StringUtils.isEmpty(alert.getFlinkJobName())) {
            return false;
        }
        StartJobRequest request = new StartJobRequest(new JobId(alert.getFlinkCluster(), alert.getFlinkJobName()));
        try {
            deroraAdmin.startJob(request);
            return true;
        } catch (TException e) {
            log.error(e.toString());
        } catch (Exception e) {
            log.error(e.toString());
        }
        return false;
    }

    public boolean restartFlinkJob(Alert alert) {
        if (alert == null || StringUtils.isEmpty(alert.getFlinkJobName())) {
            return false;
        }
        RestartJobRequest request = new RestartJobRequest(new JobId(alert.getFlinkCluster(), alert.getFlinkJobName()));
        try {
            deroraAdmin.restartJob(request);
            return true;
        } catch (TException e) {
            log.error(e.toString());
        } catch (Exception e) {
            log.error(e.toString());
        }
        return false;
    }


    public boolean stopFlinkJob(Alert alert) {
        if (alert == null || StringUtils.isEmpty(alert.getFlinkJobName())) {
            return false;
        }
        StopJobRequest request = new StopJobRequest(new JobId(alert.getFlinkCluster(), alert.getFlinkJobName()));
        try {
            deroraAdmin.stopJob(request);
            return true;
        } catch (TException e) {
            log.error(e.toString());
        } catch (Exception e) {
            log.error(e.toString());
        }
        return false;
    }

    public void updateFlinkJobYarnQueue(Alert alert, String department) {
        if (alert == null || StringUtils.isEmpty(alert.getFlinkJobName())
                || StringUtils.isEmpty(alert.getFlinkCluster())) {
            return;
        }

        DescribeJobResponse describeJobResponse = getFlinkJob(alert);
        if (describeJobResponse == null) {
            return;
        }

        SubmitJobRequest submitJobRequest = new SubmitJobRequest(
                describeJobResponse.jobInfo, describeJobResponse.jobOptions);

        submitJobRequest.getJobOptions().getCommonConfig().getYarnInfo().setYarnQueue(getYarnQueue(department));
        submitJobRequest.jobInfo.setSubmitType(SubmitType.EDIT_SAVE);

        try {
            deroraAdmin.submitJob(submitJobRequest);
        }  //可能原因:有必填字段未赋值. 详情在"e.getMessage()"中
        catch (TException e) {
            log.error(e.toString());
        } catch (Exception e) {
            log.error(e.toString());
        }
    }

    private DescribeJobResponse getFlinkJob(Alert alert) {
        DescribeJobRequest describeJobRequest = new DescribeJobRequest()
                .setJobId(new JobId(alert.getFlinkCluster(), alert.getFlinkJobName()))
                .setWithJobInfo(true)
                .setWithJobOptions(true)
                .setWithJobRunInfo(false);

        try {
            return deroraAdmin.describeJob(describeJobRequest);
        } catch (TException e) {
            log.error(e.toString());
            return null;
        }
    }

    public boolean updateFlinkAlertRules(Alert alert, List<AlertRule> alertRules) {
        if (alert == null || StringUtils.isEmpty(alert.getFlinkJobName())
                || StringUtils.isEmpty(alert.getFlinkCluster())) {
            return false;
        }

        DescribeJobResponse describeJobResponse = getFlinkJob(alert);
        if (describeJobResponse == null) {
            return false;
        }

        SubmitJobRequest submitJobRequest = new SubmitJobRequest(
                describeJobResponse.jobInfo, describeJobResponse.jobOptions);

        String oldArguments = submitJobRequest.getJobOptions().getJobConfig().getMainArguments();
        if (StringUtils.isEmpty(oldArguments)) {
            return false;
        }

        ParameterTool tool = ParameterTool.fromArgs(oldArguments.split("[ ]"));

        String arguments = baseArguments + serializeFlinkArguments(tool.toMap(), alert.getId(), alertRules);
        submitJobRequest.getJobOptions().getJobConfig().setMainArguments(arguments);
        submitJobRequest.jobInfo.setSubmitType(SubmitType.EDIT_SUBMIT);

        try {
            deroraAdmin.submitJob(submitJobRequest);
            return true;
        }  //可能原因:有必填字段未赋值. 详情在"e.getMessage()"中
        catch (TException e) {
            log.error(e.toString());
        } catch (Exception e) {
            log.error(e.toString());
        }
        return false;
    }


    public boolean updateFlinkJobArguments(Alert alert, AlertParam param, List<AlertRule> alertRules) {
        if (alert == null || StringUtils.isEmpty(alert.getFlinkJobName())
                || StringUtils.isEmpty(alert.getFlinkCluster())) {
            return false;
        }

        DescribeJobResponse describeJobResponse = getFlinkJob(alert);
        if (describeJobResponse == null) {
            return false;
        }

        SubmitJobRequest submitJobRequest = new SubmitJobRequest(
                describeJobResponse.jobInfo, describeJobResponse.jobOptions);


        String arguments = baseArguments + serializeFlinkArguments(alert, param, alertRules);
        submitJobRequest.getJobOptions().getJobConfig().setMainArguments(arguments);
        submitJobRequest.jobInfo.setSubmitType(SubmitType.EDIT_SAVE);

        try {
            deroraAdmin.submitJob(submitJobRequest);
            return true;
        }  //可能原因:有必填字段未赋值. 详情在"e.getMessage()"中
        catch (TException e) {
            log.error(e.toString());
        } catch (Exception e) {
            log.error(e.toString());
        }
        return false;
    }

    public boolean removeFlinkJob(Alert alert) {
        if (StringUtils.isEmpty(alert.getFlinkCluster())
                || StringUtils.isEmpty(alert.getFlinkJobName())) {
            return true;
        }
        RemoveJobRequest removeJobRequest = new RemoveJobRequest(new JobId(alert.getFlinkCluster(), alert.getFlinkJobName()));
        try {
            deroraAdmin.removeJob(removeJobRequest);
            return true;
        } catch (GalaxyStreamingJobException e) {
            log.error(e.toString());
            //flink job not exist
            return true;
        } catch (TException e) {
            log.error(e.toString());
        } catch (Exception e) {
            log.error(e.toString());
        }
        return false;
    }
}
