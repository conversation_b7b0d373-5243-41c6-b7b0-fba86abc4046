package com.xiaomi.mone.log.manager.controller;

import com.xiaomi.mone.log.manager.model.dto.MetaAppInfoDTO;
import com.xiaomi.mone.log.manager.model.Pair;
import com.xiaomi.mone.log.manager.model.dto.DeploySystemAppsDTO.DeploySystemAppData;
import com.xiaomi.mone.log.manager.model.dto.DeploySystemAppsDTO.DeploySystemCluster;
import com.xiaomi.mone.log.manager.model.dto.MatrixAppsDTO;
import com.xiaomi.mone.log.manager.service.InnerLogTailService;
import com.xiaomi.youpin.docean.anno.Controller;
import com.xiaomi.youpin.docean.anno.RequestMapping;
import com.xiaomi.youpin.docean.anno.RequestParam;
import org.apache.ozhera.log.common.Result;
import org.apache.ozhera.log.manager.model.dto.SimpleAppEnvDTO;

import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/4/11 19:40
 */
@Controller
public class InnerMilogConfigController {

    @Resource
    private InnerLogTailService logTailService;

    /**
     * Matrix: 根据appId获取项目下(接入链路)的所有部署空间
     *
     * @param appId
     * @return
     */
    @RequestMapping(path = "/milog/project/matrix/deploySpace", method = "get")
    public Result<MatrixAppsDTO.MatrixAppData> getMatrixDeploySpaceByAppId(@RequestParam(value = "appId") Long appId,
                                                                           @RequestParam(value = "machineRoom") String machineRoom) {
        return logTailService.getMatrixDeploySpaceByAppId(appId, machineRoom);
    }

    /**
     * Matrix: 根据 appInfo 获取对应部署空间下的全部容器ip
     *
     * @param appName
     * @param iamTreeId
     * @param deploySpace
     * @return
     */
    @RequestMapping(path = "/milog/project/matrix/ip", method = "get")
    public Result<MatrixAppsDTO.MatrixDeploySpace> getMatrixPodIPsByAppInfo(@RequestParam(value = "iamTreeId") Long iamTreeId,
                                                                            @RequestParam(value = "appName") String appName,
                                                                            @RequestParam(value = "deploySpace") String deploySpace) {
        return logTailService.getMatrixPodIPsByAppInfo(iamTreeId, appName, deploySpace);
    }

    /**
     * DeploySystem: 根据appId获取项目下(接入链路)的所有集群
     *
     * @param appId
     * @return
     */
    @RequestMapping(path = "/milog/project/deploysystem/cluster", method = "get")
    public Result<DeploySystemAppData> getDeploySystemClusterByAppId(@RequestParam(value = "appId") Long appId,
        @RequestParam(value = "machineRoom") String machineRoom) {
        return logTailService.getDeploySystemClusterByAppId(appId, machineRoom);
    }

    /**
     * DeploySystem: 根据 appInfo 获取对应cluster下的全部机器ip
     *
     * @param appId
     * @param appName
     * @param cluster
     * @return
     */
    @RequestMapping(path = "/milog/project/deploysystem/ip", method = "get")
    public Result<DeploySystemCluster> getDeploySystemClusterIPsByAppInfo(@RequestParam(value = "appId") Long appId,
        @RequestParam(value = "appName") String appName,
        @RequestParam(value = "cluster") String cluster) {
        return logTailService.getDeploySystemClusterIPsByAppInfo(appId, appName, cluster);
    }

    /**
     * MIKS: 根据appId获取项目下(接入链路)的所有集群
     *
     * @param appId
     * @return
     */
    @RequestMapping(path = "/milog/project/miks/cluster", method = "get")
    public Result<MetaAppInfoDTO.MiKSAppData> getMiKSAppByAppInfo(@RequestParam(value = "appId") Long appId,
                                                                  @RequestParam(value = "machineRoom") String machineRoom) {
        return logTailService.getMiKSAppByAppInfo(appId, machineRoom);
    }

    /**
     * DeploySystem: 根据 appInfo 获取对应cluster下的全部机器ip
     *
     * @param appId
     * @param appName
     * @param envName
     * @return
     */
    @RequestMapping(path = "/milog/project/miks/ip", method = "get")
    public Result<MetaAppInfoDTO.Env> getMiKSAppEnvIPsByAppInfo(@RequestParam(value = "appId") Long appId,
                                                                @RequestParam(value = "appName") String appName,
                                                                @RequestParam(value = "envName") String envName) {
        return logTailService.getMiKSAppEnvIPsByAppInfo(appId, appName, envName);
    }

    /**
     * 获取mis应用的机房信息
     *
     * @param milogAppId
     * @return
     */
    @RequestMapping(path = "/milog/project/mis/zone/appId", method = "get")
    public Result<List<SimpleAppEnvDTO>> getRegionZonesByAppId(@RequestParam(value = "milogAppId") Long milogAppId,
                                                               @RequestParam(value = "machineRoom") String machineRoom) {
        return logTailService.getRegionZonesByAppId(milogAppId, machineRoom);
    }

    /**
     * 查询所有没有绑定mq的tail信息
     *
     * @return
     */
    @RequestMapping(path = "/tail/no/mq", method = "get")
    public Result<Map<Long, List<Pair<Long, Long>>>> queryTailNoMq() {
        return logTailService.queryTailNoMq();
    }

    /**
     * 查询接入日志的hera链接
     *
     * @param appName
     * @return
     */
    @RequestMapping(path = "/log/access/list", method = "get")
    public Result<List<String>> queryAccessLogList(@RequestParam(value = "appName") String appName) {
        return logTailService.queryAccessLogList(appName);
    }

}
