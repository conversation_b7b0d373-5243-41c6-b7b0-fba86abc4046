package com.xiaomi.mone.log.manager.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.xiaomi.mone.enums.InnerProjectTypeEnum;
import com.xiaomi.mone.log.manager.dao.LogSpaceDao;
import com.xiaomi.mone.log.manager.dao.LogStreamBalanceConfigDao;
import com.xiaomi.mone.log.manager.model.Pair;
import com.xiaomi.mone.log.manager.model.bo.SpacePartitionBalance;
import com.xiaomi.mone.log.manager.model.po.LogStreamBalanceConfig;
import com.xiaomi.mone.log.manager.model.vo.MachinePartitionParam;
import com.xiaomi.mone.log.manager.model.vo.SpaceIpParam;
import com.xiaomi.mone.log.manager.service.StreamPartitionServiceInner;
import com.xiaomi.mone.miline.api.dto.milog.PipelineInstanceDto;
import com.xiaomi.mone.miline.api.dto.milog.ProjectInstanceDto;
import com.xiaomi.mone.miline.api.dto.milog.SimpleMachine;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import com.xiaomi.youpin.gwdash.bo.MachineBo;
import com.xiaomi.youpin.gwdash.bo.MiLogMachineBo;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.app.api.response.AppBaseInfo;
import org.apache.ozhera.log.manager.common.exception.MilogManageException;
import org.apache.ozhera.log.manager.dao.MilogSpaceDao;
import org.apache.ozhera.log.manager.model.page.PageInfo;
import org.apache.ozhera.log.manager.model.pojo.MilogSpaceDO;
import org.apache.ozhera.log.manager.service.HeraAppService;
import org.apache.ozhera.log.manager.service.extension.common.CommonExtensionServiceFactory;
import org.apache.ozhera.log.manager.service.impl.MilogConfigNacosServiceImpl;
import org.apache.ozhera.log.model.MiLogStreamConfig;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.MIFE_LOG_PREFIX;
import static org.apache.ozhera.log.common.Constant.TAIL_CONFIG_DATA_ID;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/9/19 15:06
 */
@Service
@Slf4j
public class StreamPartitionServiceInnerImpl_Test implements StreamPartitionServiceInner {

    @Resource
    private MilogConfigNacosServiceImpl logConfigNacosService;

    @Getter
    private LogStreamBalanceConfigDao balanceConfigDao;

    @Resource
    private LogSpaceDao logSpaceDao;
    @Resource
    private MilogSpaceDao milogSpaceDao;

    @Resource
    private HeraAppService heraAppService;

    @Resource
    private MilineRpcConsumerServiceImpl milineRpcConsumerService;

    @Value("$log_stream_name")
    private String log_stream_name;

    private static final Cache<String, String> CACHE_LOCAL_IP_HOSTNAME = CacheBuilder.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build();

    public void init() {
        MiLogMachineBo simplePipeEnvBos = milineRpcConsumerService.queryMachineInfoByProject(log_stream_name, "");
        Set<MachineBo> machineInfos = simplePipeEnvBos.getMachineInfos();
        for (MachineBo machineInfo : machineInfos) {
            CACHE_LOCAL_IP_HOSTNAME.put(machineInfo.getIp(), machineInfo.getHostname());
        }
    }


    @Override
    public PageInfo<SpacePartitionBalance> querySpacePartitionBalance(MachinePartitionParam partitionParam) {
        MiLogStreamConfig config = buildMiLogStreamConfig(partitionParam.getMachineRoom());

        Map<Pair<Long, String>, List<String>> spaceIps = buildSpaceIpsMap(config.getConfig());
        List<Long> spaceIds = getSpaceIdsByNameExcludingMife(partitionParam.getSpaceName());

        List<SpacePartitionBalance> spacePartitionBalanceList = buildSpacePartitionBalanceList(spaceIps);

        if (CollectionUtils.isNotEmpty(spaceIds)) {
            spacePartitionBalanceList = spacePartitionBalanceList.stream()
                    .filter(data -> spaceIds.contains(data.getSpaceId()))
                    .collect(Collectors.toList());
        }

        List<SpacePartitionBalance> pageList = CollectionUtil.page(partitionParam.getPageNum() - 1, partitionParam.getPageSize(), spacePartitionBalanceList);

        updateSpaceNames(pageList);

        return buildPageInfo(partitionParam, spacePartitionBalanceList, pageList);
    }

    private void updateSpaceNames(List<SpacePartitionBalance> spacePartitionBalanceList) {
        spacePartitionBalanceList.forEach(data -> {
            MilogSpaceDO milogSpaceDO = milogSpaceDao.queryById(data.getSpaceId());
            data.setSpaceName(milogSpaceDO.getSpaceName());
        });
    }

    private List<Long> getSpaceIdsByNameExcludingMife(String spaceName) {
        List<Long> spaceIds;
        if (StringUtils.isNotBlank(spaceName)) {
            List<MilogSpaceDO> spaceDOS = logSpaceDao.queryByName(spaceName);
            spaceIds = spaceDOS.stream()
                    .filter(data -> !StringUtils.endsWith(data.getSpaceName(), MIFE_LOG_PREFIX))
                    .map(MilogSpaceDO::getId)
                    .toList();
        } else {
            spaceIds = logSpaceDao.queryNonMifeSpaceId();
        }
        return spaceIds;
    }

    @Override
    public PageInfo<Pair<Long, String>> queryIpPartitionBalance(MachinePartitionParam partitionParam) {
        MiLogStreamConfig config = buildMiLogStreamConfig(partitionParam.getMachineRoom());

        List<Long> excludingMifeSpaceIds = getSpaceIdsByNameExcludingMife(partitionParam.getSpaceName());

        Map<Long, String> spaceKeys = config.getConfig().get(partitionParam.getUniqueKey());
        List<Pair<Long, String>> pairList = spaceKeys.entrySet().stream()
                .filter(data -> excludingMifeSpaceIds.contains(data.getKey()))
                .sorted(Map.Entry.comparingByKey())
                .map(data -> Pair.of(data.getKey(), data.getValue()))
                .collect(Collectors.toList());

        List<Pair<Long, String>> pageList = CollectionUtil.page(partitionParam.getPageNum() - 1, partitionParam.getPageSize(), pairList);
        pageList = pageList.stream().map(data -> {
            MilogSpaceDO milogSpaceDO = milogSpaceDao.queryById(data.getKey());
            return Pair.of(data.getKey(), milogSpaceDO.getSpaceName());
        }).collect(Collectors.toList());

        return buildPageInfo(partitionParam, pairList, pageList);

    }

    @Override
    public PageInfo<Pair<String, String>> queryStreamList(MachinePartitionParam partitionParam) {
        MiLogStreamConfig config = buildMiLogStreamConfig(partitionParam.getMachineRoom());
        List<Pair<String, String>> dataList = config.getConfig().keySet()
                .stream()
                .filter(data -> {
                    if (StringUtils.isNotEmpty(partitionParam.getUniqueKey())) {
                        return Objects.equals(partitionParam.getUniqueKey(), data);
                    }
                    return true;
                })
                .map(ip -> Pair.of(ip, queryStreamHostname(ip)))
                .collect(Collectors.toList());

        return buildPageInfo(partitionParam, dataList, dataList);
    }

    private MiLogStreamConfig buildMiLogStreamConfig(String machineRoom) {
        logConfigNacosService.chooseCurrentEnvNacosService(machineRoom);
        MiLogStreamConfig config = logConfigNacosService.getStreamConfigNacosProvider(machineRoom).getConfig(null);
        if (config == null) {
            throw new MilogManageException("当前机房nacos配置不存在");
        }
        return config;
    }


    @Override
    public Boolean addSpaceToIp(SpaceIpParam param) {
        MiLogStreamConfig config = buildMiLogStreamConfig(param.getMachineRoom());

        for (String uniqueKey : param.getUniqueKeys()) {
            config.getConfig().putIfAbsent(uniqueKey, new HashMap<>());
            for (Long spaceId : param.getSpaceIds()) {
                String spaceKey = String.format("%s%s%s", CommonExtensionServiceFactory.getCommonExtensionService().getLogManagePrefix(), TAIL_CONFIG_DATA_ID, spaceId);
                config.getConfig().get(uniqueKey).putIfAbsent(spaceId, spaceKey);
            }
        }

        logConfigNacosService.getStreamConfigNacosPublisher(param.getMachineRoom()).publish(param.getSpaceId(), config);
        return true;
    }

    @Override
    public Boolean delSpaceToIp(SpaceIpParam param) {
        MiLogStreamConfig config = buildMiLogStreamConfig(param.getMachineRoom());
        Map<Long, String> spaceMap = config.getConfig().get(param.getUniqueKey());
        if (null != spaceMap) {
            spaceMap.remove(param.getSpaceId());
            if (spaceMap.isEmpty()) {
                config.getConfig().remove(param.getUniqueKey());
            }
            logConfigNacosService.getStreamConfigNacosPublisher(param.getMachineRoom()).publish(param.getSpaceId(), config);
        }
        return true;
    }

    @Override
    public boolean streamReBalance() {
        List<LogStreamBalanceConfig> balanceConfigs = balanceConfigDao.queryConfigEnabled();
        for (LogStreamBalanceConfig balanceConfig : balanceConfigs) {
            MiLogStreamConfig logStreamConfig = logConfigNacosService.getStreamConfigNacosProvider(balanceConfig.getMachineRoom()).getConfig("", balanceConfig.getStreamBalanceDataId());
            List<String> streamList = logConfigNacosService.getFetchStreamMachineService(balanceConfig.getMachineRoom()).getStreamList(balanceConfig.getStreamServerName());
            if (null == logStreamConfig || CollectionUtils.isEmpty(streamList)) {
                continue;
            }
            Map<Long, String> oldStreamIp = logStreamConfig.getConfig().get(balanceConfig.getOldStreamKey());
            int size = oldStreamIp.size();
            int count = 0;

            for (Map.Entry<Long, String> spaceMap : oldStreamIp.entrySet()) {
                count++;
                if (count % streamList.size() == 0) {

                }
            }
        }
        return false;
    }

    @Override
    public String queryStreamHostname(String ip) {
        try {
            return CACHE_LOCAL_IP_HOSTNAME.get(ip, () -> queryHostnameFromCacheOrService(ip));
        } catch (ExecutionException e) {
            log.error("queryStreamHostname error,ip:{}", ip, e);
            return StringUtils.EMPTY;
        }
    }

    private String queryHostnameFromCacheOrService(String ip) {
        try {
            List<AppBaseInfo> appBaseInfos = heraAppService.queryAppInfoWithLog(log_stream_name, InnerProjectTypeEnum.MIONE_TYPE.getCode());
            ProjectInstanceDto projectInstanceDto = milineRpcConsumerService.queryMachineInfoByProjectNew(Long.valueOf(appBaseInfos.get(0).getBindId()), null);

            return findHostnameInPipelineInstances(ip, projectInstanceDto.getPipelineInstanceDtoList());

        } catch (Exception e) {
            log.error("queryHostnameFromCacheOrService error,ip:{}", ip, e);
            return StringUtils.EMPTY;
        }
    }

    private String findHostnameInPipelineInstances(String ip, List<PipelineInstanceDto> pipelineInstanceDtoList) {
        for (PipelineInstanceDto pipelineInstanceDto : pipelineInstanceDtoList) {
            for (SimpleMachine machine : pipelineInstanceDto.getMachines()) {
                if (StringUtils.equals(ip, machine.getIp())) {
                    return machine.getHostname();
                }
                CACHE_LOCAL_IP_HOSTNAME.put(machine.getIp(), machine.getHostname());
            }
        }
        return StringUtils.EMPTY;
    }

    @Override
    public List<Pair<String, Long>> findUnIncludedSpaceList(SpaceIpParam param) {
        MiLogStreamConfig config = buildMiLogStreamConfig(param.getMachineRoom());

        Map<Long, String> spaceKeys = config.getConfig().get(param.getUniqueKey());
        List<Long> includedSpaceList = new ArrayList<>(spaceKeys.keySet());

        List<MilogSpaceDO> allSpaces = logSpaceDao.queryByName(param.getSpaceName());

        List<Pair<String, Long>> pairList = allSpaces.parallelStream()
                .filter(space -> !includedSpaceList.contains(space.getId())
                        && !StringUtils.endsWith(space.getSpaceName(), MIFE_LOG_PREFIX))
                .map(space -> Pair.of(space.getSpaceName(), space.getId()))
                .collect(Collectors.toList());
        return CollectionUtil.page(param.getPageNum() - 1, param.getPageSize(), pairList);
    }

    @Override
    public List<Pair<String, String>> queryAllUniqueKeyList(SpaceIpParam param) {
        MiLogStreamConfig config = buildMiLogStreamConfig(param.getMachineRoom());

        return config.getConfig().entrySet().stream()
                .filter(data -> !data.getValue().containsKey(param.getSpaceId()))
                .map(data -> Pair.of(data.getKey(), data.getKey())).collect(Collectors.toList());
    }

    private Map<Pair<Long, String>, List<String>> buildSpaceIpsMap(Map<String, Map<Long, String>> configConfig) {
        Map<Pair<Long, String>, List<String>> spaceIps = new HashMap<>();
        for (Map.Entry<String, Map<Long, String>> ipEntry : configConfig.entrySet()) {
            for (Map.Entry<Long, String> spaceEntry : ipEntry.getValue().entrySet()) {
                Pair<Long, String> spacePair = new Pair<>(spaceEntry.getKey(), spaceEntry.getValue());
                spaceIps.computeIfAbsent(spacePair, k -> new ArrayList<>()).add(ipEntry.getKey());
            }
        }
        return spaceIps;
    }

    private List<SpacePartitionBalance> buildSpacePartitionBalanceList(Map<Pair<Long, String>, List<String>> spaceIps) {
        List<SpacePartitionBalance> balanceList = spaceIps.entrySet().stream().map(entry -> {
                    SpacePartitionBalance spacePartitionBalance = new SpacePartitionBalance();
                    Pair<Long, String> spacePair = entry.getKey();
                    spacePartitionBalance.setSpaceId(spacePair.getKey());
                    spacePartitionBalance.setSpaceIdentifiers(spacePair.getValue());
                    spacePartitionBalance.setMachineUniques(entry.getValue());
                    return spacePartitionBalance;
                }).sorted(Comparator.comparing(SpacePartitionBalance::getSpaceId))
                .collect(Collectors.toList());
        return balanceList;
    }
}
