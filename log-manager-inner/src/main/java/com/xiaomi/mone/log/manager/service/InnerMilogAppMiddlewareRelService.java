package com.xiaomi.mone.log.manager.service;


import com.xiaomi.mone.log.manager.model.bo.alert.MqInfoBo;
import org.apache.ozhera.log.manager.model.pojo.MilogMiddlewareConfig;
import org.apache.ozhera.log.manager.service.MilogAppMiddlewareRelService;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/4/10 17:38
 */
public interface InnerMilogAppMiddlewareRelService extends MilogAppMiddlewareRelService {
    void bindingMisAppTailConfigRel(Long tailId, Long milogAppId, String motorRoomEn);

    /**
     * 创建使用平台资源的 logstore 下的 logtail 对应的 topic，并与 logtail 创建绑定关系
     *
     * @param spaceId
     * @param storeId
     * @param tailId
     * @param storeMqResourceId
     */
    void bindingPlatformTailConfigRel(Long spaceId, Long storeId, Long tailId, Long milogAppId, Long storeMqResourceId);

    /**
     * 删除使用平台资源的 logstore 下的 logtail 对应的 topic，并删除绑定关系
     *
     * @param tailId
     * @param milogAppId
     * @param storeMqResourceId
     */
    void unBindingPlatformTailConfigRel(Long storeId, Long tailId, Long milogAppId, Long storeMqResourceId);

    MqInfoBo getMqInfoBo(List<Long> tailIds);

    void createMilogAppMiddlewareRel(Long spaceId, Long storeId, Long tailId, Long milogAppId, MilogMiddlewareConfig milogMiddlewareConfig);
}
