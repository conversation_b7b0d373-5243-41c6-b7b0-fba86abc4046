package com.xiaomi.mone.log.manager.service;

import com.xiaomi.mone.log.manager.model.dto.dt.DtCleanResult;
import com.xiaomi.mone.log.manager.model.vo.ClearDtResourceParam;
import com.xiaomi.mone.log.manager.user.MoneUser;
import org.apache.ozhera.log.common.Result;

/**
 * @author: songyutong1
 * @date: 2024/09/02/17:30
 */
public interface MiLogDataCleanToolService {

    /**
     * 异步清理工厂数据与数据库不一致
     */
    Result<String> asyncCleanInconsistentData(ClearDtResourceParam param);

    /**
     * 清理工厂数据与数据库不一致
     */
    Result<DtCleanResult> cleanInconsistentData(ClearDtResourceParam param, String uuid, MoneUser user);
}
