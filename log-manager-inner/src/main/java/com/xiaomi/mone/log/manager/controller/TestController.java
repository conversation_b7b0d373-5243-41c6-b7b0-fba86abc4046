/*
 *  Copyright 2020 Xiaomi
 *
 *    Licensed under the Apache License, Version 2.0 (the "License");
 *    you may not use this file except in compliance with the License.
 *    You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *    Unless required by applicable law or agreed to in writing, software
 *    distributed under the License is distributed on an "AS IS" BASIS,
 *    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *    See the License for the specific language governing permissions and
 *    limitations under the License.
 */

package com.xiaomi.mone.log.manager.controller;

import com.alibaba.nacos.api.config.ConfigService;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.xiaomi.mone.enums.InnerProjectTypeEnum;
import com.xiaomi.mone.log.manager.dao.InnerLogTailDao;
import com.xiaomi.mone.log.manager.dao.InnerMilogLogStoreDao;
import com.xiaomi.mone.log.manager.dao.ResourceBillAccountRelDao;
import com.xiaomi.mone.log.manager.model.po.ResourceBillAccountRelDO;
import com.xiaomi.mone.log.manager.model.vo.SpaceIpParam;
import com.xiaomi.mone.log.manager.service.InnerLogAgentService;
import com.xiaomi.mone.log.manager.service.StopCollectAppJobService;
import com.xiaomi.mone.log.manager.service.impl.BillingManagementServiceImpl;
import com.xiaomi.youpin.docean.Ioc;
import com.xiaomi.youpin.docean.anno.Controller;
import com.xiaomi.youpin.docean.anno.RequestMapping;
import com.xiaomi.youpin.docean.anno.RequestParam;
import com.xiaomi.youpin.docean.common.StringUtils;
import com.xiaomi.youpin.docean.mvc.ContextHolder;
import com.xiaomi.youpin.docean.mvc.MvcContext;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import com.xiaomi.youpin.docean.plugin.dubbo.anno.Reference;
import com.xiaomi.youpin.docean.plugin.es.antlr4.common.util.EsQueryUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.app.api.response.AppBaseInfo;
import org.apache.ozhera.log.annotation.UnifiedResponse;
import org.apache.ozhera.log.api.model.meta.LogCollectMeta;
import org.apache.ozhera.log.api.service.PublishConfigService;
import org.apache.ozhera.log.common.Constant;
import org.apache.ozhera.log.common.Result;
import org.apache.ozhera.log.manager.common.Version;
import org.apache.ozhera.log.manager.dao.MilogAppTopicRelDao;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.dao.MilogRegionAvailableZoneDao;
import org.apache.ozhera.log.manager.model.bo.RegionZoneBo;
import org.apache.ozhera.log.manager.model.pojo.*;
import org.apache.ozhera.log.manager.service.extension.agent.MilogAgentServiceImpl;
import org.apache.ozhera.log.manager.service.impl.AgentConfigServiceImpl;
import org.apache.ozhera.log.manager.service.impl.HeraAppServiceImpl;
import org.apache.ozhera.log.manager.service.nacos.MultipleNacosConfig;
import org.apache.ozhera.log.manager.service.nacos.impl.SpaceConfigNacosProvider;
import org.apache.ozhera.log.manager.user.MoneUtil;
import org.apache.rocketmq.acl.common.AclClientRPCHook;
import org.apache.rocketmq.acl.common.SessionCredentials;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.client.consumer.rebalance.AllocateMessageQueueAveragely;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.remoting.RPCHook;
import org.nutz.dao.Cnd;
import org.nutz.dao.impl.NutDao;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.nio.file.Files;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.IntStream;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.INNER_LOG_AGENT_SERVICE;
import static com.xiaomi.mone.log.manager.user.MoneUserDetailService.GSON;
import static org.apache.ozhera.log.manager.service.extension.agent.MilogAgentService.DEFAULT_AGENT_EXTENSION_SERVICE_KEY;

/**
 * <AUTHOR>
 * @Date 2021/6/24 16:15
 */
@Slf4j
@Controller
public class TestController {

    @Value(value = "$rocketmq_namesrv_addr")
    private String address;
    @Value(value = "$rocketmq_ak")
    private String ak;
    @Value(value = "$rocketmq_sk")
    private String sk;

    @Resource
    private NutDao dao;

    @Resource
    private SpaceConfigNacosProvider spaceConfigNacosProvider;

    @Reference(interfaceClass = PublishConfigService.class, group = "$dubbo.env.group", check = false, timeout = 14000)
    private PublishConfigService publishConfigService;

    @Resource(name = DEFAULT_AGENT_EXTENSION_SERVICE_KEY)
    private MilogAgentServiceImpl milogAgentService;

    @Resource(name = INNER_LOG_AGENT_SERVICE)
    private InnerLogAgentService innerLogAgentService;

    @Resource
    private MilogLogTailDao milogLogtailDao;

    @Resource
    private InnerLogTailDao innerLogTailDao;

    @Resource
    private MilogAppTopicRelDao milogAppTopicRelDao;

    @Resource
    private ResourceBillAccountRelDao resourceBillAccountRelDao;
    @Resource
    private BillingManagementServiceImpl billingManagementService;
    @Resource
    private InnerMilogLogStoreDao milogLogstoreDao;
    @Resource
    private Gson gson;

    @Resource
    private HeraAppServiceImpl heraAppService;

    @Resource
    private StopCollectAppJobService stopCollectAppJobService;

    @RequestMapping(path = "/test/unifiedResponse", method = "get")
    @UnifiedResponse
    public String testUnifiedResponse() {
        return "我是UnifiedResponse";
    }

    @RequestMapping(path = "/test/response", method = "get")
    public SpaceIpParam testResponse() {
        return new SpaceIpParam();
    }


    @RequestMapping(path = "/version", method = "get")
    public String version() {
        return new Version().toString();
    }

    @RequestMapping(path = "/milog/exception", method = "get")
    public String testException() throws InvocationTargetException {
        throw new InvocationTargetException(new IllegalMonitorStateException("fsdfd"), "哈哈哈哈异常");
    }

    @RequestMapping(path = "/ok", method = "get")
    public String ok() {
        IntStream.range(0, 100).forEach(value -> {
            log.info("hhhahfasfsd,{}", value);
        });
        return "ok:" + dao.fetch(MilogSpaceDO.class);
    }

    @RequestMapping(path = "/test/file/inode", method = "get")
    public String testFileInode(@RequestParam(value = "file") String file) throws IOException {
        log.info("file:{}", file);
        File files = new File(file);

        BasicFileAttributes bfa = Files.readAttributes(files.toPath(), BasicFileAttributes.class);
        Object o = bfa.fileKey();
        log.info("file inode:{}", o);
        return "ok:" + dao.fetch(MilogSpaceDO.class);
    }

    @RequestMapping(path = "/get/nacos/ok", method = "get")
    public String testIsOk() {
        ConfigPushData configPushData = new ConfigPushData();
        configPushData.setId(1L);
        ConfigService configService = MultipleNacosConfig.getConfigService("127.0.0.1:80");
        spaceConfigNacosProvider.setConfigService(configService);
        spaceConfigNacosProvider.getConfig(2L);
        return "ok";
    }

    @RequestMapping(path = "/test/push/config", method = "get")
    public String testConfigPush() {
        List<String> allAgentList = publishConfigService.getAllAgentList();
        return GSON.toJson(allAgentList);
    }


    /**
     * 测试删除配置-通知log-agent停止收集
     */
    @RequestMapping(path = "/test/del/config/stop/col", method = "get")
    public String testDelConfigStopColl(@RequestParam(value = "tailId") Long tailId) {
        MilogLogTailDo milogLogtailDo = milogLogtailDao.queryById(tailId);
        milogAgentService.publishIncrementDel(milogLogtailDo.getId(), milogLogtailDo.getMilogAppId(), milogLogtailDo.getIps());
        return "success";
    }

    /**
     * 根据pod ip查看部署的agentIp
     */
    @RequestMapping(path = "/test/agent/ip/k8s", method = "get")
    public Result queryAgentK8sIp(@RequestParam(value = "ip") String ip) {

        return Result.success("");
    }

    /**
     * k8s中根据agentIP获取该agent下的所有配置
     */
    @RequestMapping(path = "/test/agent/ip/config", method = "get")
    public Result queryAgentConfig(@RequestParam(value = "ip") String ip) {
        AgentConfigServiceImpl agentConfigService = Ioc.ins().getBean(AgentConfigServiceImpl.class);
        LogCollectMeta logCollectMetaFromManager = agentConfigService.getLogCollectMetaFromManager(ip);
        log.info("返回的数据：{}", gson.toJson(logCollectMetaFromManager));
        return Result.success(logCollectMetaFromManager);
    }

    /**
     * 给 agent 推送一个 tail 配置
     */
    @RequestMapping(path = "/test/agent/increment/config", method = "get")
    public Result queryIncrementConfig(@RequestParam(value = "tailId") Long tailId) {
        if (tailId != null) {
            processTailId(tailId);
        } else {
            CompletableFuture.runAsync(this::processAllLogTails);
        }
        return Result.success("ok");
    }

    private void processTailId(Long tailId) {
        MilogLogTailDo milogLogtailDo = milogLogtailDao.queryById(tailId);
        if (milogLogtailDo != null) {
            innerLogAgentService.publishIncrementConfig(tailId, milogLogtailDo.getMilogAppId(), milogLogtailDo.getIps());
        }
    }

    private void processAllLogTails() {
        int loop = 0;
        int interval = 100;

        List<MilogLogTailDo> logTails = innerLogTailDao.queryLogTailByAppType(InnerProjectTypeEnum.MATRIX_TYPE.getCode());
        for (MilogLogTailDo logTail : logTails) {
            if (loop++ != 0 && loop % interval == 0) {
                try {
                    Thread.sleep(20 * 1000);
                } catch (InterruptedException e) {
                    log.error("processLogTail, sleep error", e);
                }
            }
            innerLogAgentService.publishIncrementConfig(logTail.getId(), logTail.getMilogAppId(), logTail.getIps());
            log.info("processLogTail, current loop:{}", loop);
        }
        log.info("processLogTail finish, the final loop:{}", loop);
    }

    /**
     * 根据 milogAppId 查询 app 信息
     */
    @RequestMapping(path = "/test/milog/app", method = "get")
    public Result queryMilogAppInfo(@RequestParam(value = "milogAppId") Long milogAppId) {
        AppBaseInfo appBaseInfo = heraAppService.queryById(milogAppId);
        return Result.success(gson.toJson(appBaseInfo));
    }

    @RequestMapping(path = "/test/bill/update", method = "post")
    public Result updateResourceBillAccountRel(@RequestParam(value = "ResourceBillAccountRelDO") ResourceBillAccountRelDO rel) {
        Boolean result = resourceBillAccountRelDao.updateResourceBillAccountRel(rel);
        return Result.success(gson.toJson(result));
    }

    /**
     * Query the collection configuration of the current machine
     *
     * @param day     in "yyyy.MM.dd" pattern
     * @param storeId esIndex related
     * @return
     */
    @RequestMapping(path = "/test/logstorage/delete/thisday", method = "get")
    public Result collectLogStorage(@RequestParam(value = "day") String day,
                                    @RequestParam(value = "storeId") Long storeId) {
        try {
            if (StringUtils.isEmpty(day)) {
                day = LocalDate.now().plusDays(-1).format(DateTimeFormatter.ofPattern("yyyy.MM.dd"));
            }
            List<MilogEsIndexDO> indexs = new ArrayList<>();
            if (storeId != null && storeId > 0) {
                MilogEsIndexDO esIndexDO = milogLogstoreDao.queryEsIndexDOByStoreId(storeId, false);
                if (esIndexDO != null) {
                    indexs.add(esIndexDO);
                }
            } else {
                indexs = milogLogstoreDao.getPaginatedESIndex(0, 5000);
            }
            boolean result = billingManagementService.collectLogStorage(indexs, day);
            return Result.success(gson.toJson(result));
        } catch (Exception e) {
            log.error("collectLogStorage error:", e);
            return Result.success(gson.toJson(false));
        }
    }


    @SneakyThrows
    @RequestMapping(path = "/test/db", method = "get")
    public String testDb() {
        log.info("testdb");
        List<MilogAppTopicRelDO> milogAppTopicRels = milogAppTopicRelDao.queryAppTopicList(Cnd.NEW(), null);
        TimeUnit.SECONDS.sleep(20);
        return "testDb:" + milogAppTopicRels.size();
    }


    @SneakyThrows
    @RequestMapping(path = "/test/db1", method = "get")
    public String testDb1(MvcContext context) {
        log.info("testdb1:{}", context);
        MvcContext mvcContext = ContextHolder.getContext().get();
        log.info("context:{}", context);
        List<MilogAppTopicRelDO> milogAppTopicRels = milogAppTopicRelDao.queryAppTopicList(Cnd.NEW(), null);
        return "testDb1:" + milogAppTopicRels.size();
    }

    @SneakyThrows
    @RequestMapping(path = "/test/mq/consumer", method = "get")
    public String testConsumer(@RequestParam(value = "topic") String topic,
                               @RequestParam(value = "consumerGroup") String consumerGroup,
                               @RequestParam(value = "tag") String tag) {
        consumer(topic, consumerGroup, tag);
        return "success";
    }

    @RequestMapping(path = "/test/session/set", method = "get")
    public String testSession(MvcContext context) {
        context.session().setAttribute("name", "zzy");
        return "set session success";
    }

    @RequestMapping(path = "/test/session2", method = "get")
    public String session2(MvcContext context) {
        return "session2:" + context.getSession().getAttribute(MoneUtil.MONE_USER_INFO);
    }

    @RequestMapping(path = "/test/session/get", method = "get")
    public String testSessionGet(MvcContext context) {
        return context.session().getAttribute("name").toString();
    }

    @RequestMapping(path = "/test/match/*", method = "get")
    public String match(MvcContext context) {
//        String path = context.getPath();
//        log.info(ContextHolder.getContext().get().getPath());
        return "match:" + System.currentTimeMillis() + ":";
    }


    /**
     * 测试mq消费
     *
     * @param topic
     * @param consumerGroup
     * @param tag
     * @throws MQClientException
     */
    public void consumer(String topic, String consumerGroup, String tag) throws MQClientException {
        SessionCredentials credentials = new SessionCredentials(ak, sk);
        RPCHook rpcHook = new AclClientRPCHook(credentials);
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(consumerGroup, rpcHook, new AllocateMessageQueueAveragely());
        consumer.setNamesrvAddr(address);
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
        consumer.subscribe(topic, tag);

        consumer.registerMessageListener((MessageListenerOrderly) (list, consumeOrderlyContext) -> {
            list.stream().forEach(ele -> {
                byte[] body = ele.getBody();
                log.info("消息：{}", new String(body));
            });
            return ConsumeOrderlyStatus.SUCCESS;
        });
        try {
            consumer.start();
        } catch (MQClientException e) {
            log.error("订阅创建项目时的RocketMq客户端启动异常", e);
        }
        System.out.println("transaction_Consumer Started.");
    }

    @RequestMapping(path = "/test/init/insert/region/sql", method = "get")
    public String initInsertSql() {
        String jsonStr = "";
        RegionZoneBo regionZoneBO = gson.fromJson(jsonStr, RegionZoneBo.class);
        MilogRegionAvailableZoneDao regionAvailableZoneDao = Ioc.ins().getBean(MilogRegionAvailableZoneDao.class.getCanonicalName());
        regionZoneBO.getData().forEach(innerClass -> {
            if (innerClass.getIs_used()) {
                MilogRegionAvailableZoneDO milogRegionAvailableZoneDO = new MilogRegionAvailableZoneDO();
                milogRegionAvailableZoneDO.setRegionNameEN(innerClass.getRegion_en());
                milogRegionAvailableZoneDO.setRegionNameCN(innerClass.getRegion_cn());
                milogRegionAvailableZoneDO.setZoneNameCN(innerClass.getZone_name_cn());
                milogRegionAvailableZoneDO.setZoneNameEN(innerClass.getZone_name_en());
                milogRegionAvailableZoneDO.setCtime(Instant.now().toEpochMilli());
                milogRegionAvailableZoneDO.setUtime(Instant.now().toEpochMilli());
                milogRegionAvailableZoneDO.setCreator(Constant.DEFAULT_OPERATOR);
                milogRegionAvailableZoneDO.setUpdater(Constant.DEFAULT_OPERATOR);
                regionAvailableZoneDao.insert(milogRegionAvailableZoneDO);
            }
        });
        System.out.println(regionZoneBO);
        return "success";
    }

    @RequestMapping(path = "/test/trace/log", method = "get")
    public Result traceLog(@RequestParam(value = "traceId") String traceId) {
        return null;
    }

    @RequestMapping(path = "/test/stop/log/coll", method = "GET")
    public Result<String> sendStopLogCollTest(
            @RequestParam(value = "tailId") Long tailId,
            @RequestParam(value = "directory") String directory,
            @RequestParam(value = "ip") String ip) {
        milogAgentService.delLogCollDirectoryByIp(tailId, directory, Lists.newArrayList(ip));
        return Result.success("success");
    }

    @RequestMapping(path = "/test/es/query/grammar", method = "get")
    public Result<String> esQuerySearchGrammar(@RequestParam(value = "code") String code) {
        return Result.success(EsQueryUtils.getEsQuery(code));
    }

    @RequestMapping(path = "/test/exception", method = "get")
    public String testExceptionT() {
        int res = 1 / 0;
        return "success";
    }

    @RequestMapping(path = "/test/coll/stop", method = "post")
    public Result<String> deleteCollectLogByConfig(MilogLogTailDo data) {
        stopCollectAppJobService.stopCollectLogByIdAndIp(data.getId(), data.getIps());
        return Result.success("success");
    }


}
