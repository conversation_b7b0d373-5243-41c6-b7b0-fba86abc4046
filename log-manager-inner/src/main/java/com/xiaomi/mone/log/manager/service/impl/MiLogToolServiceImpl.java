package com.xiaomi.mone.log.manager.service.impl;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.xiaomi.infra.galaxy.streaming.jobserver.thrift.jobserver.DescribeJobResponse;
import com.xiaomi.mone.enums.*;
import com.xiaomi.mone.log.manager.common.context.MoneUserContext;
import com.xiaomi.mone.log.manager.common.utils.DtUtils;
import com.xiaomi.mone.log.manager.dao.InnerLogMiddlewareConfigDao;
import com.xiaomi.mone.log.manager.dao.InnerMilogLogStoreDao;
import com.xiaomi.mone.log.manager.dao.alert.AlertDao;
import com.xiaomi.mone.log.manager.model.MigrateDtResourceConfig;
import com.xiaomi.mone.log.manager.model.alert.Alert;
import com.xiaomi.mone.log.manager.model.alert.AlertJob;
import com.xiaomi.mone.log.manager.model.alert.AlertJobDetail;
import com.xiaomi.mone.log.manager.model.alert.DelJobRequest;
import com.xiaomi.mone.log.manager.model.bo.ResourceIds;
import com.xiaomi.mone.log.manager.model.bo.alert.AlertParam;
import com.xiaomi.mone.log.manager.model.bo.alert.AlertStatus;
import com.xiaomi.mone.log.manager.service.*;
import com.xiaomi.mone.log.manager.service.alert.AlertService;
import com.xiaomi.mone.log.manager.service.alert.FlinkAlphaService;
import com.xiaomi.mone.log.manager.service.alert.FlinkService;
import com.xiaomi.mone.log.manager.user.MoneUser;
import com.xiaomi.mone.tpc.common.vo.NodeVo;
import com.xiaomi.mone.tpc.common.vo.PageDataVo;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.ozhera.app.api.response.AppBaseInfo;
import org.apache.ozhera.log.api.enums.MachineTypeEnum;
import org.apache.ozhera.log.api.enums.OperateEnum;
import org.apache.ozhera.log.api.enums.ProjectTypeEnum;
import org.apache.ozhera.log.api.enums.RateLimitEnum;
import org.apache.ozhera.log.common.Constant;
import org.apache.ozhera.log.common.Result;
import org.apache.ozhera.log.manager.common.Utils;
import org.apache.ozhera.log.manager.common.exception.MilogManageException;
import org.apache.ozhera.log.manager.dao.*;
import org.apache.ozhera.log.manager.domain.LogTail;
import org.apache.ozhera.log.manager.domain.SearchLog;
import org.apache.ozhera.log.manager.domain.Tpc;
import org.apache.ozhera.log.manager.mapper.MilogEsClusterMapper;
import org.apache.ozhera.log.manager.mapper.MilogEsIndexMapper;
import org.apache.ozhera.log.manager.model.bo.LogTailParam;
import org.apache.ozhera.log.manager.model.dto.DictionaryDTO;
import org.apache.ozhera.log.manager.model.dto.EsInfoDTO;
import org.apache.ozhera.log.manager.model.dto.MapDTO;
import org.apache.ozhera.log.manager.model.pojo.*;
import org.apache.ozhera.log.manager.model.vo.LogQuery;
import org.apache.ozhera.log.manager.model.vo.LogTailSendLokiVo;
import org.apache.ozhera.log.manager.service.extension.common.CommonExtensionService;
import org.apache.ozhera.log.manager.service.extension.common.CommonExtensionServiceFactory;
import org.apache.ozhera.log.manager.service.impl.HeraAppServiceImpl;
import org.apache.ozhera.log.manager.service.impl.LogTailServiceImpl;
import org.apache.ozhera.log.manager.service.impl.MilogConfigNacosServiceImpl;
import org.apache.ozhera.log.manager.service.impl.MilogMiddlewareConfigServiceImpl;
import org.apache.ozhera.log.model.LogtailConfig;
import org.apache.ozhera.log.parse.LogParserFactory;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;

import javax.annotation.Resource;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.*;
import static org.apache.ozhera.log.common.Constant.*;
import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getKeyColonPrefix;
import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getKeyList;
import static org.apache.ozhera.log.parse.LogParser.*;
import static org.elasticsearch.search.sort.SortOrder.ASC;
import static org.elasticsearch.search.sort.SortOrder.DESC;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/1/5 10:55
 */
@Slf4j
@Service
public class MiLogToolServiceImpl implements MiLogToolService {

    @Resource
    private MilogLogTailDao milogLogtailDao;

    @Resource
    private MilogLogstoreDao milogLogstoreDao;

    @Resource(name = INNER_STORE_SERVICE)
    private InnerStoreExtensionService innerStoreExtensionService;

    @Resource
    private InnerMilogLogStoreDao innerMilogLogStoreDao;

    @Resource
    private MilogSpaceDao milogSpaceDao;

    @Resource
    private MilogConfigNacosServiceImpl milogConfigNacosService;

    @Resource
    private AlertDao alertDao;

    @Resource
    private AlertService alertService;

    @Resource
    private MilogAppTopicRelDao milogAppTopicRelDao;

    @Resource
    private MilogAppMiddlewareRelDao milogAppMiddlewareRelDao;
    @Resource
    private InnerLogMiddlewareConfigDao middlewareConfigDao;

    @Resource
    private MilogMiddlewareConfigServiceImpl milogMiddlewareConfigService;

    @Resource
    private MatrixLogServiceImpl matrixLogServiceImpl;
    @Resource
    private MilogMiddlewareConfigDao milogMiddlewareConfigDao;

    @Resource
    private FlinkService flinkService;

    @Resource
    private FlinkAlphaService flinkAlphaService;

    @Resource
    private MilogConfigNacosServiceImpl milogConfigNacosServiceImpl;
    @Resource
    private static InnerMilogLogStoreDao innerMilogLogstoreDao;

    @Resource
    private MilogEsIndexMapper esIndexMapper;

    @Resource
    private MilogEsClusterMapper milogEsClusterMapper;
    @Resource
    private LogTailServiceImpl logTailService;
    @Resource
    private InnerLogMiddlewareConfigDao innerLogMiddlewareConfigDao;
    @Resource
    private Tpc tpc;

    @Value(value = "$hdfs.file")
    private String hdfsJarFilePath;

    @Value("$dt_domain")
    private String dtDomain;

    @Value("$alpha_token")
    private String alphaToken;

    @Value("$alert.main.class")
    private String alertMainClass;
    @Resource
    private TalosMqConfigService talosMqConfigService;
    @Resource
    private InnerEsIndexTemplate innerEsIndexTemplate;
    @Resource
    private InnerEsCluster esCluster;
    private Gson gson = new Gson();
    private final Map<String, String> regionToken = new HashMap<>();

    @Resource(name = INNER_TAIL_SERVICE)
    private InnerTailExtensionService tailExtensionService;

    @Resource
    private LogTail logTail;

    private static boolean test(MilogLogStoreDO milogLogstoreDO) {
        return null == milogLogstoreDO.getMqResourceId();
    }

    @Resource
    private HeraAppServiceImpl heraAppService;

    @Resource
    private SearchLog searchLog;

    public static List<Pair<String, Pair<String, Integer>>> requiredFields = Lists.newArrayList(
            Pair.of(ES_KEY_MAP_MESSAGE, Pair.of("text", 1)),
            Pair.of(ES_KEY_MAP_LOG_SOURCE, Pair.of("text", 1)),
            Pair.of(ES_KEY_MAP_TAIL_ID, Pair.of("integer", 3))
    );
    private Set<String> noHighLightSet = new HashSet<>();

    {
        noHighLightSet.add("logstore");
        noHighLightSet.add("tail");
    }

    @Override
    public Result<String> sendLokiMsg(Long tailId) {
        List<Long> tailIds = Lists.newArrayList();
        if (null != tailId) {
            tailIds.add(tailId);
        } else {
            tailIds = milogLogtailDao.queryAllIds();
        }
        tailIds.parallelStream().forEach(sendTailId -> {
            LogTailSendLokiVo logTailSendLokiVo = generateLokiVo(sendTailId);
            log.info("send data:{}", gson.toJson(logTailSendLokiVo));
        });
        return Result.success();
    }

    @Override
    public Result<List<MapDTO<String, Long>>> getSpacesByUser(String user, Boolean isAdmin) {
        int pageNum = 1;
        List<MapDTO<String, Long>> ret = new ArrayList<>();
        List<NodeVo> nodeVos = new ArrayList<>();
        MoneUser moneUser = new MoneUser();
        moneUser.setUser(user);
        moneUser.setUserType(0);
        moneUser.setIsAdmin(isAdmin);
        MoneUserContext.setCurrentUser(moneUser);
        while (true) {
            com.xiaomi.youpin.infra.rpc.Result<PageDataVo<NodeVo>> tpcRes = tpc.getUserPermSpace("", pageNum,
                    Integer.MAX_VALUE);
            if (tpcRes.getCode() != 0) {
                return Result.fail(401, user);
            }

            List<NodeVo> list = tpcRes.getData() != null ? tpcRes.getData().getList() : null;

            if (CollectionUtils.isEmpty(list)) {
                break;
            }

            nodeVos.addAll(list);
            pageNum++;
        }

        for (NodeVo s : nodeVos) {
            ret.add(new MapDTO<>(s.getNodeName(), s.getOutId()));
        }

        return Result.success(ret);
    }

    @Override
    public void fixAlertAppId() {
        List<Alert> allAlerts = alertDao.getAllAlerts();
        for (Alert alert : allAlerts) {
            List<MilogAppTopicRelDO> milogAppTopicRels = milogAppTopicRelDao
                    .queryAppInfo(Long.parseLong(alert.getApp()), alert.getAppName(), null, "");
            alert.setMilogAppId(milogAppTopicRels.get(0).getId());
            alertDao.update(alert);
        }
    }

    @Override
    public void fixMilogAlertTailId() {
        List<Alert> allAlerts = alertDao.getAllAlerts();
        for (Alert alert : allAlerts) {
            Map<String, String> arguments = alert.getArguments();
            if (StringUtils.isBlank(alert.getFlinkJobName())) {
                continue;
            }
            DescribeJobResponse describeJobResponse = flinkService.queryJob(alert.getFlinkJobName());
            String mainArguments = describeJobResponse.getJobOptions().getJobConfig().mainArguments;
            ParameterTool tool = ParameterTool.fromArgs(mainArguments.split(" "));
            String consumerTopic = tool.get(Constant.CONSUMER_TOPIC);
            String[] array = consumerTopic.split("_");
            String tailIdStr = array[array.length - 2];
            arguments.putIfAbsent(TAILID_KEY, tailIdStr);
            alertDao.update(alert);
        }
    }

    @Override
    public void fixFlinkAlertNotify(Long alertId) {

        List<Alert> allAlerts = alertDao.queryAllStartJob(alertId);
        /**
         * 分3步
         * 1.查询出来所有的日志告警
         * 2.更新 notify
         */
        int size = allAlerts.size();
        Stopwatch stopwatch = Stopwatch.createUnstarted();
        stopwatch.start();
        log.info("日志告警配置一共有:{}个配置需要处理notify", size);
        int count = 0;
        for (Alert alert : allAlerts) {
            ++count;
            String regionEn = alertService.getRegionEnByAlert(alert);
            // 目前只有上海机房的历史报警未修改
            if (!StringUtils.equals(InnerMachineRegionEnum.SH_MACHINE.getEn(), regionEn)) {
                log.info("日志告警配置 修复notify, skip this one");
                continue;
            }
            String authorization = getAuthorization(regionEn);
            String url = String.format("/openapi/develop/jobs/%s", alert.getJobId());
            AlertJobDetail alertJobDetail = DtUtils.get(url, alphaToken, AlertJobDetail.class);

            Map<String, String> headerMap = Maps.newHashMap();
            headerMap.put("Content-Type", "application/json");
            headerMap.put("Authorization", authorization);
            String receivers = "{\"notifyIf\":[\"FAILED\"],\"retryTriggerCondition\":\"custom\",\"customConditionThreshold\":7,\"timeout\":120,\"notifyProvider\":\"Falcon\",\"notifyLevel\":\"P1\",\"notifyingReceiver\":[{\"notifyObjectType\":\"oncall\",\"receivers\":[{\"id\":\"21830\",\"value\":\"Hera日志Oncall\"}]}]}";
            AlertJobDetail.Notice notice = GSON.fromJson(receivers, AlertJobDetail.Notice.class);
            if (!CollectionUtils.isEqualCollection(alertJobDetail.getNoticeList(), Lists.newArrayList(notice))) {
                alertJobDetail.setNoticeList(Lists.newArrayList(notice));
            }

            String updateDateUrl = String.format("/openapi/develop/jobs/%s/op/flinkjar", alert.getJobId());
            alertJobDetail.setUpdateMode("UPDATE_ALL");
            String result = DtUtils.put(updateDateUrl, GSON.toJson(alertJobDetail), headerMap);
            log.info("日志告警配置 修复notify result:{}", result);
            log.info("日志告警配置一共有:{}个配置,开始处理第:{}个配置的notify，还剩下:{}个配置", size, count, size - count);

        }
        stopwatch.stop();
        log.info("日志告警配置 修复notify完成,花费：{}s", stopwatch.elapsed().getSeconds());
    }

    private String getAuthorization(String regionEn) {
        if (!regionToken.containsKey(regionEn)) {
            MilogMiddlewareConfig middlewareConfig = middlewareConfigDao.queryByTypeAndRegionEn(regionEn,
                    InnerMiddlewareEnum.PLATFORM_DEFAULT_TALOS.getCode());
            if (null != middlewareConfig) {
                regionToken.put(regionEn, middlewareConfig.getToken());
            }
        }
        return String.format("workspace-token/1.0 %s", regionToken.getOrDefault(regionEn, alphaToken));
    }

    @Override
    public String fixResourceLabel() {
        return milogMiddlewareConfigService.synchronousResourceLabel(null);
    }

    @Override
    public String fixLogStoreMqResourceId(Long storeId) {
        List<MilogLogStoreDO> doList = Lists.newArrayList();
        if (null != storeId) {
            doList.add(milogLogstoreDao.queryById(storeId));
        } else {
            doList = milogLogstoreDao.queryAll();
        }
        doList = doList.stream()
                .filter(MiLogToolServiceImpl::test)
                .collect(Collectors.toList());
        doList.forEach(milogLogstoreDO -> fixPerStoreMqResourceId(milogLogstoreDO));
        return SUCCESS_MESSAGE;
    }

    private static boolean hasNonExistEsIndex(MilogLogStoreDO milogLogstoreDO) {
        if (null == milogLogstoreDO ||
                null == milogLogstoreDO.getEsIndex() ||
                null == milogLogstoreDO.getEsClusterId()) {
            return false;
        }
        MilogEsIndexDO esIndexDO = innerMilogLogstoreDao.queryEsIndexDOByStoreId(milogLogstoreDO.getId(), false);
        return null == esIndexDO;
    }

    @Override
    public String fixEsIndexNotExistByStore(Long storeId) {
        List<MilogLogStoreDO> doList = Lists.newArrayList();
        if (null != storeId) {
            doList.add(milogLogstoreDao.queryById(storeId));
        } else {
            doList = milogLogstoreDao.queryAll();
        }
        doList = doList.stream()
                .filter(MiLogToolServiceImpl::hasNonExistEsIndex)
                .collect(Collectors.toList());
        doList.forEach(milogLogstoreDO -> fixPerStoreEsIndex(milogLogstoreDO));
        return SUCCESS_MESSAGE;
    }

    private void fixPerStoreEsIndex(MilogLogStoreDO milogLogstoreDO) {
        // 插入 milog_es_index 表记录
        MilogEsIndexDO milogEsIndexDO = new MilogEsIndexDO();
        milogEsIndexDO.setClusterId(milogLogstoreDO.getEsClusterId());
        milogEsIndexDO.setLogType(milogLogstoreDO.getLogType());
        milogEsIndexDO.setIndexName(milogLogstoreDO.getEsIndex());
        esIndexMapper.insert(milogEsIndexDO);
    }

    @Override
    public String fixNacosEsInfo(Long spaceId) {
        List<MilogLogStoreDO> storeDOS;
        if (null != spaceId) {
            storeDOS = milogLogstoreDao.getMilogLogstoreBySpaceId(spaceId);
        } else {
            storeDOS = milogLogstoreDao.queryAll();
        }
        List<MilogLogStoreDO> logStoreDOS = storeDOS.stream().filter(milogLogStoreDO -> {
            Integer logTypeCode = milogLogStoreDO.getLogType();
            if (!Objects.equals(logTypeCode, InnerLogTypeEnum.OPENTELEMETRY.getType()) &&
                    !Objects.equals(logTypeCode, InnerLogTypeEnum.LOKI_APP_LOG.getType()) &&
                    !Objects.equals(logTypeCode, InnerLogTypeEnum.MATRIX_ES_LOG.getType())) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());

        for (MilogLogStoreDO logStoreDO : logStoreDOS) {

            List<MilogLogTailDo> logTailDos = milogLogtailDao.queryTailsByStoreId(logStoreDO.getId());
            milogConfigNacosServiceImpl.chooseCurrentEnvNacosService(logStoreDO.getMachineRoom());

            for (MilogLogTailDo logTailDo : logTailDos) {
                publishNamespaceConfig(logStoreDO, logTailDo);
            }
        }
        log.info("fixNacosEsInfo end");
        return SUCCESS_MESSAGE;
    }

    @Override
    public String updateEsClusterAddress(Long spaceId, Long esClusterId, String newEsAddr, String catalog) {

        MilogEsClusterDO milogEsClusterDO = milogEsClusterMapper.selectById(esClusterId);
        //如果需要一把更新es地址就更新
        if (newEsAddr != null && !newEsAddr.isEmpty() && !catalog.isEmpty()) {
            milogEsClusterDO.setAddr(newEsAddr);
            milogEsClusterDO.setDtCatalog(catalog);
            milogEsClusterMapper.updateById(milogEsClusterDO);
        }
        List<MilogLogStoreDO> storeDos = (spaceId != null)
                ? milogLogstoreDao.getMilogLogstoreBySpaceId(spaceId)
                : milogLogstoreDao.queryAll();

        List<MilogLogStoreDO> logStoreDos = storeDos.stream()
                .filter(store -> Objects.equals(store.getEsClusterId(), esClusterId))
                .toList();

        log.info("updateEsClusterAddress record total size: {}", logStoreDos.size());

        int count = 0;
        for (MilogLogStoreDO logStoreDO : logStoreDos) {
            logOperationProgress(logStoreDos.size(), ++count);

            List<MilogLogTailDo> logTailDos = milogLogtailDao.queryTailsByStoreId(logStoreDO.getId());

            for (MilogLogTailDo logTailDo : logTailDos) {
                publishNamespaceConfig(logStoreDO, logTailDo);
            }
        }

        log.info("updateEsClusterAddress end");
        return SUCCESS_MESSAGE;
    }

    @Override
    public String updateEsClusterInfo(ResourceIds esInfoUpdate) {
        for (Long esClusterId : esInfoUpdate.getEsIdList()) {

            List<MilogLogStoreDO> storeDos = innerMilogLogStoreDao.queryByEsInfoId(esClusterId);

            log.info("updateEsClusterInfo record total size: {}", storeDos.size());

            processLogStores(storeDos);
        }

        log.info("updateEsClusterInfo end");
        return SUCCESS_MESSAGE;
    }

    private void logOperationProgress(int totalSize, int currentCount) {
        log.info("fixNacosEsInfo record total size: {}, current: {}, remain: {}",
                totalSize, currentCount, totalSize - currentCount);
    }

    private void publishNamespaceConfig(MilogLogStoreDO logStoreDO, MilogLogTailDo logTailDo) {
        try {
            milogConfigNacosServiceImpl.publishNameSpaceConfig(logStoreDO.getMachineRoom(),
                    logTailDo.getSpaceId(), logTailDo.getStoreId(), logTailDo.getId(),
                    OperateEnum.UPDATE_OPERATE.getCode(), "");
            TimeUnit.MILLISECONDS.sleep(100);
        } catch (Exception e) {
            log.error("publishNameSpaceConfig error, logTailId: {}, logStoreDO: {}",
                    logTailDo.getId(), gson.toJson(logStoreDO), e);
        }
    }

    @Override
    public void fixLogTailLogAppId(String appName) {
        if (StringUtils.isNotBlank(appName)) {
            List<MilogAppTopicRelDO> appTopicRelDOS = milogAppTopicRelDao.queryAppbyName(appName);
            int count = 0;
            if (CollectionUtils.isNotEmpty(appTopicRelDOS)) {
                for (MilogAppTopicRelDO appTopicRelDO : appTopicRelDOS) {
                    Integer appType = appTopicRelDO.getType();
                    if (Objects.equals(appTopicRelDO.getSource(), InnerProjectSourceEnum.YOUPIN_SOURCE.getSource())) {
                        appType = 20;
                    }
                    List<MilogLogTailDo> logTailDos = milogLogtailDao.getLogTailByMilogAppId(appTopicRelDO.getId());
                    AppBaseInfo appBaseInfo = heraAppService.queryByAppId(appTopicRelDO.getAppId(), appType);
                    for (MilogLogTailDo logTailDo : logTailDos) {
                        if (!Objects.equals(logTailDo.getAppId(), appTopicRelDO.getAppId())) {
                            appBaseInfo = heraAppService.queryByAppId(logTailDo.getAppId(), appType);
                        }
                        logTailDo.setMilogAppId(Long.valueOf(appBaseInfo.getId()));
                        logTailDo.setAppName(appBaseInfo.getAppName());
                        logTailDo.setAppId(Long.valueOf(appBaseInfo.getBindId()));
                        milogLogtailDao.update(logTailDo);
                        log.info("update num:{}", ++count);
                    }
                }
            }
        } else {
            fixTail();
        }
    }

    @Override
    public void fixAlertLogAppId(Integer tailId) {
        List<Alert> allAlerts = alertDao.getAllAlerts();
        for (Alert alert : allAlerts) {
            String targetTailId = alert.getArgument("tailId");
            if (null != tailId && Objects.equals(tailId.toString(), targetTailId)) {
                MilogLogTailDo milogLogTailDo = milogLogtailDao.queryById(tailId.longValue());
                alert.setMilogAppId(milogLogTailDo.getMilogAppId());
                alertDao.update(alert);
                return;
            }
            MilogLogTailDo milogLogTailDo = milogLogtailDao.queryById(Long.valueOf(targetTailId));
            if (null == milogLogTailDo) {
                log.info("current alert tail null:{}", gson.toJson(alert));
            } else {
                alert.setMilogAppId(milogLogTailDo.getMilogAppId());
                alertDao.update(alert);
            }
        }
    }

    @Override
    public void fixAppMiddleRel(Long tailId) {
        if (null != tailId) {
            List<MilogAppMiddlewareRel> middlewareRels = milogAppMiddlewareRelDao.queryByCondition(null, null, tailId);
            handleRes(middlewareRels);
            return;
        }
        int offset = 0, rows = 100;
        while (true) {
            log.info("轮询应用,offset:{},rows:{}", offset, rows);
            List<MilogAppMiddlewareRel> middlewareRels = milogAppMiddlewareRelDao.getAppRelByLimit(offset, rows);
            offset += rows;
            if (CollectionUtils.isEmpty(middlewareRels)) {
                log.info("exit");
                break;
            }
            handleRes(middlewareRels);
        }
    }

    @Override
    public void fixTailAppId() {
        List<MilogLogTailDo> logTailDos = milogLogtailDao.queryTailWithAppIdNull();
        if (CollectionUtils.isNotEmpty(logTailDos)) {
            for (MilogLogTailDo logTailDo : logTailDos) {
                AppBaseInfo appBaseInfo = heraAppService.queryById(logTailDo.getMilogAppId());
                if (null != appBaseInfo) {
                    logTailDo.setAppId(Long.valueOf(appBaseInfo.getBindId()));
                    logTailDo.setAppName(appBaseInfo.getAppName());
                    milogLogtailDao.update(logTailDo);
                }
            }
        }
    }

    private void handleRes(List<MilogAppMiddlewareRel> middlewareRels) {
        for (MilogAppMiddlewareRel middlewareRel : middlewareRels) {
            try {
                MilogLogTailDo milogLogTailDo = milogLogtailDao.queryById(middlewareRel.getTailId());
                if (null != milogLogTailDo) {
                    middlewareRel.setMilogAppId(milogLogTailDo.getMilogAppId());
                    milogAppMiddlewareRelDao.update(middlewareRel);
                }
            } catch (Exception e) {
                log.error("fixAppMiddleRel error middlewareRel:{}", gson.toJson(middlewareRel));
            }
        }
    }

    private void fixTail() {
        int offset = 0, rows = 100;
        int num = 0;
        while (true) {
            log.info("轮询应用,offset:{},rows:{}", offset, rows);
            List<MilogLogTailDo> logTailDos = milogLogtailDao.getLogTailByLimit(offset, rows);
            offset += rows;
            if (CollectionUtils.isEmpty(logTailDos)) {
                log.info("exit");
                break;
            }
            for (MilogLogTailDo logTailDo : logTailDos) {
                try {
                    AppBaseInfo appBaseInfo = getAppBaseInfo(logTailDo);
                    if (null == appBaseInfo) {
                        log.info("query app not exists,logTailDo:{}", gson.toJson(logTailDo));
                    } else {
                        logTailDo.setMilogAppId(Long.valueOf(appBaseInfo.getId()));
                        logTailDo.setAppName(appBaseInfo.getAppName());
                        logTailDo.setAppId(Long.valueOf(appBaseInfo.getBindId()));
                        milogLogtailDao.update(logTailDo);
                        log.info("updated num:{}", ++num);
                    }
                } catch (Exception e) {
                    log.error("fixTail error logTailDo:{}", gson.toJson(logTailDo));
                }
            }
        }
    }

    private AppBaseInfo getAppBaseInfo(MilogLogTailDo logTailDo) {
        AppBaseInfo appBaseInfo;
        if (Objects.equals(InnerProjectTypeEnum.RELEASE_TYPE.getCode(), logTailDo.getAppType()) ||
                Objects.equals(InnerProjectTypeEnum.MICE_TYPE.getCode(), logTailDo.getAppType()) ||
                Objects.equals(InnerProjectTypeEnum.MATRIX_TYPE.getCode(), logTailDo.getAppType())) {
            appBaseInfo = heraAppService.queryByAppId(logTailDo.getMilogAppId(), logTailDo.getAppType());
        } else {
            MilogAppTopicRelDO appTopicRelDO = null;
            if (null != appTopicRelDO) {
                Long appId = appTopicRelDO.getAppId();
                if (!Objects.equals(logTailDo.getAppId(), appId)) {
                    appId = logTailDo.getAppId();
                }
                Integer appType = appTopicRelDO.getType();
                if (Objects.equals(appTopicRelDO.getSource(), InnerProjectSourceEnum.YOUPIN_SOURCE.getSource())) {
                    appType = 20;
                }
                if (Objects.equals(InnerProjectTypeEnum.MIFAAS_TYPE.getCode(), logTailDo.getAppType())) {
                    appType = ProjectTypeEnum.MIONE_TYPE.getCode();
                }
                appBaseInfo = heraAppService.queryByAppId(appId, appType);
            } else {
                appBaseInfo = heraAppService.queryByAppId(logTailDo.getAppId(), logTailDo.getAppType());
            }
        }
        return appBaseInfo;
    }

    private void fixPerStoreMqResourceId(MilogLogStoreDO milogLogstoreDO) {
        // 查询默认的resourceId
        MilogMiddlewareConfig milogMiddlewareConfig = milogMiddlewareConfigDao
                .queryDefaultMqMiddlewareConfigMotorRoom(milogLogstoreDO.getMachineRoom());
        milogLogstoreDO.setMqResourceId(milogMiddlewareConfig.getId());
        milogLogstoreDao.updateMilogLogStore(milogLogstoreDO);

    }

    private LogTailSendLokiVo generateLokiVo(Long sendTailId) {
        LogTailSendLokiVo logTailSendLokiVo = new LogTailSendLokiVo();
        logTailSendLokiVo.setTailId(sendTailId);
        MilogLogTailDo logTailDo = milogLogtailDao.queryById(sendTailId);
        logTailSendLokiVo.setTailName(logTailDo.getTail());
        MilogSpaceDO spaceDO = milogSpaceDao.queryById(logTailDo.getSpaceId());
        logTailSendLokiVo.setSpaceId(logTailDo.getSpaceId());
        logTailSendLokiVo.setSpaceName(spaceDO.getSpaceName());
        MilogLogStoreDO storeDO = milogLogstoreDao.queryById(logTailDo.getStoreId());
        logTailSendLokiVo.setStoreId(logTailDo.getStoreId());
        logTailSendLokiVo.setStoreName(storeDO.getLogstoreName());
        logTailSendLokiVo.setKeyList(Utils.parse2KeyAndTypeList(storeDO.getKeyList(), storeDO.getColumnTypeList()));
        LogtailConfig logtailConfig = milogConfigNacosService.assembleLogTailConfigs(sendTailId);
        logTailSendLokiVo.setConfig(logtailConfig);
        return logTailSendLokiVo;
    }

    public String batchCopyMultiMachineTail(Long targetStoreId, Long sourceStoreId) {
        if (null == targetStoreId || null == sourceStoreId) {
            throw new MilogManageException("targetStoreId and sourceStoreId can not be null");
        }
        List<MilogLogTailDo> logTailDos = milogLogtailDao.queryTailsByStoreId(sourceStoreId);
        logTailDos = logTailDos.stream()
                .filter(milogLogTailDo -> Objects.equals(MachineTypeEnum.PHYSICAL_MACHINE.getType(),
                        milogLogTailDo.getMachineType()))
                .collect(Collectors.toList());
        for (MilogLogTailDo logTailDo : logTailDos) {
            LogTailParam milogLogtailParam = buildLogTailParam(targetStoreId, logTailDo);
            log.info("LogTailParam:{}", gson.toJson(milogLogtailParam));
            if (CollectionUtils.isNotEmpty(milogLogtailParam.getIps())) {
                try {
                    logTailService.newMilogLogTail(milogLogtailParam);
                } catch (Exception e) {
                    log.error("batchCopyMultiMachineTail-newLogTail,LogTailParam:{},error:",
                            gson.toJson(milogLogtailParam), e);
                }
            }
        }
        return SUCCESS_MESSAGE;
    }

    private LogTailParam buildLogTailParam(Long targetStoreId, MilogLogTailDo logTailDo) {
        LogTailParam milogLogtailParam = new LogTailParam();
        MilogLogStoreDO milogLogStoreDO = milogLogstoreDao.queryById(targetStoreId);
        MilogAppTopicRelDO milogAppTopicRelDO = milogAppTopicRelDao.queryById(logTailDo.getMilogAppId());
        milogLogtailParam.setSpaceId(milogLogStoreDO.getSpaceId());
        milogLogtailParam.setStoreId(targetStoreId);
        milogLogtailParam.setMilogAppId(logTailDo.getMilogAppId());
        milogLogtailParam.setAppId(logTailDo.getAppId());
        milogLogtailParam.setAppName(logTailDo.getAppName());
        milogLogtailParam.setIps(queryIps(milogLogStoreDO.getMachineRoom(), milogAppTopicRelDO));
        milogLogtailParam.setTail(String.format("%s_%s", logTailDo.getTail(), milogLogStoreDO.getMachineRoom()));
        milogLogtailParam.setParseType(LogParserFactory.LogParserEnum.CUSTOM_PARSE.getCode());
        milogLogtailParam.setParseScript("#$%");
        milogLogtailParam.setLogPath(logTailDo.getLogPath());
        milogLogtailParam.setValueList("message");
        milogLogtailParam.setTailRate(RateLimitEnum.RATE_LIMIT_MEDIUM.getRateLimit());
        milogLogtailParam.setUtime(Instant.now().toEpochMilli());
        milogLogtailParam.setCtime(Instant.now().toEpochMilli());
        milogLogtailParam.setLogSplitExpress(logTailDo.getLogSplitExpress());
        milogLogtailParam.setAppType(logTailDo.getAppType());
        milogLogtailParam.setMachineType(logTailDo.getMachineType());
        milogLogtailParam.setDeploySpace(logTailDo.getDeploySpace());
        milogLogtailParam.setFirstLineReg(logTailDo.getFirstLineReg());
        return milogLogtailParam;
    }

    private List<String> queryIps(String machineRoom, MilogAppTopicRelDO milogAppTopicRelDO) {
        LinkedHashMap<String, List<String>> nodeIPs = milogAppTopicRelDO.getNodeIPs();
        return nodeIPs.get(machineRoom);
    }

    @Override
    public void fixLogAlertJarPath(Long alertId) {
        /**
         * 分3步
         * 1.查询出来所有开启的日志告警
         * 2.查看jar path是否正确
         * 3.不正确后stop job
         * 4.删除checkpoint
         * 5.修改jar path后启动
         */
        String jarName = "talos-flink-cycle.jar";
        List<Alert> alertList = alertDao.queryAllStartJob(alertId);
        int size = alertList.size();
        Stopwatch stopwatch = Stopwatch.createUnstarted();
        stopwatch.start();
        log.info("日志日志告警配置一共有:{}个配置", size);
        int count = 0;
        for (Alert alert : alertList) {
            ++count;
            String url = String.format("/openapi/develop/jobs/%s", alert.getJobId());
            AlertJobDetail alertJobDetail = DtUtils.get(url, alphaToken, AlertJobDetail.class);

            String authorization = String.format("workspace-token/1.0 %s", alphaToken);
            Map<String, String> headerMap = Maps.newHashMap();
            headerMap.put("Content-Type", "application/json");
            headerMap.put("Authorization", authorization);

            if (Objects.equals(alertMainClass, alertJobDetail.getMainClass()) &&
                    Objects.equals(jarName, alertJobDetail.getJarName())) {
                log.info("this job config is correct,detail jobName:{}", alertJobDetail.getJobName());
                continue;
            }
            if (!Objects.equals(alertMainClass, alertJobDetail.getMainClass())) {
                alertJobDetail.setMainClass(alertMainClass);
            }

            if (!Objects.equals(jarName, alertJobDetail.getJarName())) {
                alertJobDetail.setJarName(jarName);
            }
            String receivers = "{\"notifyIf\":[\"FAILED\"],\"timeout\":120,\"notifyProvider\":\"Falcon\",\"notifyLevel\":\"P1\",\"notifyingReceiver\":[{\"notifyObjectType\":\"oncall\",\"receivers\":[{\"id\":\"21830\",\"value\":\"Hera日志Oncall\"}]}]}";
            AlertJobDetail.Notice notice = GSON.fromJson(receivers, AlertJobDetail.Notice.class);
            if (CollectionUtils.isEmpty(alertJobDetail.getNoticeList())) {
                alertJobDetail.setNoticeList(Lists.newArrayList(notice));
            }

            String updateDateUrl = String.format("/openapi/develop/jobs/%s/op/flinkjar", alert.getJobId());
            alertJobDetail.setUpdateMode("UPDATE_ALL");
            String result = DtUtils.put(updateDateUrl, GSON.toJson(alertJobDetail), headerMap);
            log.info("result:{}", result);

            if (Objects.equals("STARTED", alertJobDetail.getJobStatus())) {
                String stopUrl = String.format("/openapi/develop/jobs/%s/stop", alert.getJobId());
                DelJobRequest delJobRequest = new DelJobRequest();
                delJobRequest.setAuthorization(authorization);
                delJobRequest.setJobId(alertId);

                String postResult = DtUtils.post(stopUrl, GSON.toJson(delJobRequest).getBytes(StandardCharsets.UTF_8),
                        headerMap, 30000);
                log.info("stopResult:{}", postResult);
                flinkAlphaService.deleteCheckPoint(alert.getJobId(), headerMap);
                try {
                    TimeUnit.SECONDS.sleep(30);
                } catch (InterruptedException e) {
                    log.error("sleep can be InterruptedException", e);
                }
                startJob(alert.getJobId(), headerMap);
            } else {
                flinkAlphaService.deleteCheckPoint(alert.getJobId(), headerMap);
            }
            log.info("日志日志告警配置一共有:{}个配置,开始处理第：{}个配置，还剩下:{}个配置", size, count, size - count);
        }
        stopwatch.stop();
        log.info("日志日志告警配置 修复完成,花费：{}s", stopwatch.elapsed().getSeconds());
    }

    @Override
    public void fixAlarmJobRestart(Long alertId) {
        List<Alert> alertList = alertDao.queryAllStartJob(alertId);
        int size = alertList.size();
        Stopwatch stopwatch = Stopwatch.createUnstarted();
        stopwatch.start();
        log.info("日志告警配置重启任务一共有:{}个配置", size);
        AtomicInteger count = new AtomicInteger();

        Map<String, String> headerMap = Maps.newHashMap();
        String authorization = String.format("workspace-token/1.0 %s", alphaToken);
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Authorization", authorization);
        CountDownLatch countDownLatch = new CountDownLatch(alertList.size());

        for (Alert alert : alertList) {
            CompletableFuture.runAsync(() -> {
                count.incrementAndGet();
                if (alert.getStatus() == AlertStatus.ON.getStatus()) {
                    String stopUrl = String.format("/openapi/develop/jobs/%s/stop", alert.getJobId());
                    DelJobRequest delJobRequest = new DelJobRequest();
                    delJobRequest.setAuthorization(authorization);
                    delJobRequest.setJobId(alertId);

                    String postResult = DtUtils.post(stopUrl,
                            GSON.toJson(delJobRequest).getBytes(StandardCharsets.UTF_8), headerMap, 30000);
                    log.info("stopResult:{}", postResult);
                    flinkAlphaService.deleteCheckPoint(alert.getJobId(), headerMap);
                    try {
                        TimeUnit.MINUTES.sleep(1);
                    } catch (InterruptedException e) {
                        log.error("sleep can be InterruptedException", e);
                    }
                    flinkAlphaService.deleteCheckPoint(alert.getJobId(), headerMap);
                    try {
                        TimeUnit.MINUTES.sleep(1);
                    } catch (InterruptedException e) {
                        log.error("sleep can be InterruptedException", e);
                    }
                    flinkAlphaService.deleteCheckPoint(alert.getJobId(), headerMap);
                    startJob(alert.getJobId(), headerMap);
                } else {
                    flinkAlphaService.deleteCheckPoint(alert.getJobId(), headerMap);
                }
                countDownLatch.countDown();
                log.info("日志告警配置重启任务一共有:{}个配置,开始处理第：{}个配置，还剩下:{}个配置", size, count.get(), size - count.get());
            });
        }
        try {
            countDownLatch.await(5, TimeUnit.HOURS);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        stopwatch.stop();
        log.info("日志告警配置重启任务 修复完成,花费：{}s", stopwatch.elapsed().getSeconds());
    }

    @Override
    public void fixAlarmLogPath(Long alertId) {
        List<Alert> alertList = alertDao.queryAllStartJob(alertId);
        for (Alert alert : alertList) {
            String tailIdStr = alert.getArgument(TAILID_KEY);
            List<Long> tailIds = Arrays.stream(tailIdStr.split(SYMBOL_COMMA)).map(Long::valueOf)
                    .collect(Collectors.toList());
            String logPaths = alertService.buildLogPaths(tailIds);
            if (StringUtils.equals(logPaths, alert.getLogPath())) {
                continue;
            }
            log.info("不相同的配置,old:{},new:{}", alert.getLogPath(), logPaths);
            alert.setLogPath(logPaths);
            alertDao.updateLogPath(alert);
        }
        log.info("fixAlarmLogPath finished");
    }

    public void startJob(Long alertId, Map<String, String> headerMap) {
        // 启动作业
        String startUrl = String.format("/openapi/develop/jobs/%s/start", alertId);
        String startResult = DtUtils.postNoParam(startUrl, headerMap);
        log.info("startResult:{}", startResult);
    }

    @Override
    public List<Long> queryAlarmByCondition(String key, String value) {
        List<Long> res = Lists.newArrayList();
        String alphaToken = "8d1f337ba417483c88cba72d24c5fe7e";
        String url = String.format("%s?%s=%d&%s=%d", "/openapi/develop/jobs/op/list", "page", 1,
                "pageSize", 1000);
        AlertJob alertJob = DtUtils.query(url, alphaToken, AlertJob.class);

        alertJob.getJobList().parallelStream().forEach(job -> {
            String urlDetail = String.format("/openapi/develop/jobs/%s", job.getJobId());
            AlertJobDetail alertJobDetail = DtUtils.query(urlDetail, alphaToken, AlertJobDetail.class);
            if (alertJobDetail.getParallelism() > 6) {
                res.add(job.getJobId());
            }
        });
        return res;
    }

    @Override
    public void fixAlertFlinkCluster(Long id) {
        List<Alert> alertList = alertDao.queryAllStartJob(id);
        Map<String, String> headerMap = Maps.newHashMap();

        int size = alertList.size();
        Stopwatch stopwatch = Stopwatch.createUnstarted();
        stopwatch.start();
        log.info("日志告警修复flink-cluster任务一共有:{}个", size);
        AtomicInteger count = new AtomicInteger();

        CountDownLatch countDownLatch = new CountDownLatch(size);
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        ExecutorService executorService = Executors.newFixedThreadPool(Math.min(size, 10)); // 设置适当的线程池大小

        for (Alert alert : alertList) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    count.incrementAndGet();
                    String url = String.format("/openapi/develop/jobs/%s", alert.getJobId());
                    AlertJobDetail alertJobDetail = DtUtils.get(url, alphaToken, AlertJobDetail.class);
                    alert.setFlinkCluster(alertJobDetail.getFlinkCluster());
                    alertDao.update(alert);

                    log.info("日志告警修复flink-cluster任务一共有:{}个配置,开始处理第：{}个配置，还剩下:{}个配置", size, count.get(),
                            size - count.get());
                } catch (Exception e) {
                    log.error("处理任务时出现异常", e);
                } finally {
                    countDownLatch.countDown();
                }
            }, executorService);
            futures.add(future);
        }

        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

        try {
            allOf.whenComplete((result, throwable) -> executorService.shutdown()); // 显式关闭线程池
            allOf.get(5, TimeUnit.HOURS);
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            throw new RuntimeException(e);
        }

        stopwatch.stop();
        log.info("日志告警修复flink-cluster任务 修复完成,花费：{}s", stopwatch.elapsed().getSeconds());
    }

    // 在拆分后的机房重建报警作业
    // 如果已经在拆分后的机房了，则跳过
    @Override
    public String rebuildAlert(Long alertId, String oldflinkCluster, String oldAlphaToken, String newAlphaToken, Boolean forceUpdate) {
        List<Alert> allAlerts = alertDao.queryAlertByIdAndFlinkClusterFuzzy(alertId, oldflinkCluster);

        int size = allAlerts.size();
        if (size == 0) {
            return "未找到需要迁移的告警作业";
        }
        Stopwatch stopwatch = Stopwatch.createUnstarted();
        stopwatch.start();
        log.info("【migrate】start to copy flink total:{}", size);

        AtomicInteger count = new AtomicInteger();
        CountDownLatch countDownLatch = new CountDownLatch(size);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        ExecutorService executorService = Executors.newFixedThreadPool(Math.min(size, 10)); // 设置适当的线程池大小

        List<Alert> failAlerts = new CopyOnWriteArrayList<>();
        String oldAuthorization = String.format("workspace-token/1.0 %s", oldAlphaToken);
        for (Alert alert : allAlerts) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    Boolean exist = false;
                    // 1. 创建新的job
                    // 查询当前 job_id 是否在现在的 cluster 中，若在，则表示已经完成
                    String regionEn = alertService.getRegionEnByAlert(alert);
                    AlertJobDetail alertJobDetail = new AlertJobDetail();
                    try {
                        String url = String.format("/openapi/develop/jobs/%s", alert.getJobId());
                        String tmpToken = getAuthorization(regionEn).replaceAll("workspace-token/1.0 ", "");
                        if (StringUtils.isNotEmpty(newAlphaToken)) {
                            tmpToken = newAlphaToken;
                        }
                        alertJobDetail = DtUtils.get(url, tmpToken, AlertJobDetail.class);

                    } catch (Exception e) {
                        log.info("【migrate】flink job not found in current cluster, ready to copy, dt response:{}",
                                e.getMessage());
                    }
                    // 判断存在的作业是否归属于旧flink集群的
                    if ((alertJobDetail != null
                            && StringUtils.contains(alertJobDetail.getFlinkCluster(), oldflinkCluster))
                            && null != alertJobDetail.getJobStatus()
                            && !alertJobDetail.isJobDeleted()) {
                        if (forceUpdate != null && forceUpdate) {
                            exist = true;
                            log.info("【migrate】flink job found in current cluster, going to update this job:{} for alert:{}", alertJobDetail.getId(), alert.getId());
                        } else {
                            log.info("【migrate】flink job found in current cluster, skip this alert:{}", alert.getId());
                            return;
                        }
                    }
                    AlertParam param = new AlertParam();
                    param.setRegionEn(regionEn);
                    param.setAlertId(alert.getId());
                    param.setMqType(InnerMiddlewareEnum.TALOS.getName());
                    String tailIdStr = alert.getArgument(TAILID_KEY);
                    if (StringUtils.isNotBlank(tailIdStr)) {
                        param.setTailIds(Arrays.stream(tailIdStr.split(SYMBOL_COMMA)).map(Long::valueOf)
                                .collect(Collectors.toList()));
                    }
                    //更新现有job
                    if (exist) {
                        boolean success = alertService.updateFlinkJobArguments(alert, param);
                        if (success) {
                            log.info("【migrate】update flink job success for this alert:{}", alert.getId());
                        } else {
                            log.error("【migrate】update flink job failed for alertId:{}, going to abort this",
                                    alert.getId());
                        }
                        return;
                    }
                    //新建job
                    Long jobId = alertService.submitFlinkJob(alert, param);

                    if (jobId == null) {
                        // 提交Flink作业失败，do nothing
                        log.error("【migrate】submit flink job failed for alertId:{}, going to abort this",
                                alert.getId());
                        failAlerts.add(alert);
                        return;
                    }
                    Long oldJobId = alert.getJobId();
                    // 2. 更新db
                    alert.setJobId(jobId);
                    alert.setFlinkJobName(flinkAlphaService.buildFlinkJobName(alert.getId()));
                    alert.setStatus(AlertStatus.ON.getStatus());
                    alertService.updateAlert(alert);
                    alertService.updateFlinkRules(alert.getId());
                    log.info("【migrate】update job success, oldJobId:{},alertId:{},new jobId:{}", oldJobId, alertId,
                            jobId);

                    // 3. 删除现有的job
                    try {
                        // 要删除的作业版本
                        String url = String.format("/openapi/develop/jobs/%s/delete", oldJobId);
                        Map<String, String> headerMap = Maps.newHashMap();
                        headerMap.put("Content-Type", "application/json");
                        headerMap.put("Authorization", oldAuthorization);
                        String result = DtUtils.delete(url, headerMap);
                        log.info("【migrate】delete job oldJobId:{},alertId:{},result:{}", oldJobId, alertId, result);
                    } catch (HttpClientErrorException | HttpServerErrorException e) {
                        log.error("【migrate】delete alpha job error,oldJobId:{},alertId:{},error:{}",
                                oldJobId, alertId,
                                new String(e.getResponseBodyAsByteArray(), Charset.defaultCharset()), e);
                        throw new MilogManageException(String.format("delete job to alpha job error:%s",
                                new String(e.getResponseBodyAsByteArray(), Charset.defaultCharset())), e);
                    }

                    log.info("【migrate】copy flink total:{},start to process no. {}, left {}", size, count.get(),
                            size - count.get());
                } catch (Exception e) {
                    log.error("【migrate】copy flink call dt fail, alertId:{}, e:", alert.getId(), e);
                    failAlerts.add(alert);
                } finally {
                    countDownLatch.countDown();
                }
            }, executorService);
            futures.add(future);
        }

        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        // 显式关闭线程池
        try {
            allOf.whenComplete((result, throwable) -> executorService.shutdown());
            allOf.get(5, TimeUnit.HOURS);
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            throw new RuntimeException(e);
        }
        stopwatch.stop();
        log.info("【migrate】copy flink finished,cost {}s,failed:{}, please manually check on failedAlert:{}",
                stopwatch.elapsed().getSeconds(), failAlerts.size(), failAlerts);

        return SUCCESS_MESSAGE;
    }

    /*
     * @param resource 枚举 flink、es、topic
     */
    @Override
    public String migrateCollectionResourceToAnotherDtSpace(MigrateDtResourceConfig config) {
        // 1. 复制资源
        List<MilogLogStoreDO> storesToMigrate = getMigrateLogStoreDOList(config.getMachineRoom(), config.getSpaceId(), config.getStoreId(),
                config.getEsClusterId()).stream().filter(store -> store.isPlatformResourceStore()).collect(
                Collectors.toList());
        log.debug("【migrate】got stores to migrate:{}", gson.toJson(storesToMigrate));
        // 1.1 topic
        if (config.getEnableTalos()) {
            copyMiddleWareToAnotherDtSpace(storesToMigrate, config);
        }
        // 1.2 es index
        if (config.getEnableEs()) {
            copyESToAnotherDtSpace(storesToMigrate, config);
        }

        List<Long> spaceIdList = storesToMigrate.stream().map(MilogLogStoreDO::getSpaceId).distinct()
                .collect(Collectors.toList());
        // 2. 资源生效
        // 2.1 fix db es -mysql工单的形式。
        // 这个开关是用来提醒有这步操作。误开不会影响线上。
        if (!config.getDoneMySQLOrder()) {
            log.error("【migrate】 return early due to not finish mysql order");
            return "【migrate】copied";
        }
        // 2.2 fixNacosEsInfo es的信息变了重新写入nacos
        if (config.getEnableNacosRefresh()) {
            for (Long spaceId : spaceIdList) {
                fixNacosEsInfo(spaceId);
            }
        }
        return "【migrate】success";
    }

    @Override
    public void sendAllStorePeriodMq(Long storeId) {
        // 如果storeId不为空，说明只是发送一个到mq，否则，批量发送所有的信息到mq
        if (null != storeId) {
            MilogLogStoreDO logStoreDO = milogLogstoreDao.queryById(storeId);
            setStorePeriodAndSendToMQ(logStoreDO);
        } else {
            int page = 1, pageSize = 100;
            List<MilogLogStoreDO> milogLogStoreDOS;
            do {
                milogLogStoreDOS = innerMilogLogStoreDao.queryLogTailByPage(page, pageSize);
                milogLogStoreDOS.forEach(this::setStorePeriodAndSendToMQ);
                page++;
            } while (!milogLogStoreDOS.isEmpty());
        }
    }

    @Override
    public Result<String> queryEsSearchDSL(LogQuery query) {
//        MilogLogStoreDO milogLogStoreDO = milogLogstoreDao.queryById(query.getStoreId());
        List<String> list = getKeyList("timestamp:1,level:1,traceId:1,threadName:1,className:1,line:1,methodName:1,message:1,podName:1,logstore:3,logsource:3,mqtopic:3,mqtag:3,logip:3,tail:3,linenumber:3", "date,keyword,keyword,text,text,keyword,keyword,text,keyword,keyword,keyword,keyword,keyword,keyword,keyword,long");
        BoolQueryBuilder queryBuilder = searchLog.getQueryBuilder(query, getKeyColonPrefix("timestamp:1,level:1,traceId:1,threadName:1,className:1,line:1,methodName:1,message:1,podName:1,logstore:3,logsource:3,mqtopic:3,mqtag:3,logip:3,tail:3,linenumber:3"));
        SearchSourceBuilder builder = assembleSearchSourceBuilder(query, list, queryBuilder);
        log.info(builder.toString());
        return Result.success(builder.toString());
    }

    @Override
    public String fixNacosMqToken(List<Long> mqIdList) {
        for (Long mqId : mqIdList) {

            List<MilogLogStoreDO> storeDos = innerMilogLogStoreDao.queryByMqInfoId(mqId);

            log.info("fixNacosMqToken record total size: {}", storeDos.size());

            processLogStores(storeDos);
        }

        log.info("fixNacosMqToken end");
        return SUCCESS_MESSAGE;
    }

    @Override
    public String pushConfigToNacos(String machineRoom) {
        //查询store
        List<MilogLogStoreDO> logStoreDOS = innerMilogLogStoreDao.queryByMachine(machineRoom);
        log.info("all logStoreDOS:{}", logStoreDOS.size());
        int count = 0;
        // 过滤掉代理日志
        for (MilogLogStoreDO logStoreDO : logStoreDOS) {
            count++;
            log.info("开始处理第{}个，还剩下{}个", count, logStoreDOS.size() - count);
            MilogSpaceDO milogSpaceDO = milogSpaceDao.queryById(logStoreDO.getSpaceId());
            if (StringUtils.endsWith(milogSpaceDO.getSpaceName(), MIFE_LOG_PREFIX)) {
                continue;
            }
            logTail.handleStoreTail(logStoreDO.getId());
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                log.error("pushConfigToNacos sleep error", e);
            }
        }
        return SUCCESS_MESSAGE;
    }

    private void processLogStores(List<MilogLogStoreDO> storeDos) {
        AtomicInteger count = new AtomicInteger();
        storeDos.parallelStream().forEach(logStoreDO -> {
            logOperationProgress(storeDos.size(), count.incrementAndGet());

            List<MilogLogTailDo> logTailDos = milogLogtailDao.queryTailsByStoreId(logStoreDO.getId());
            logTailDos.parallelStream().forEach(logTailDo -> publishNamespaceConfig(logStoreDO, logTailDo));
        });
    }

    private SearchSourceBuilder assembleSearchSourceBuilder(LogQuery logQuery, List<String> keyList, BoolQueryBuilder boolQueryBuilder) {
        CommonExtensionService commonExtensionService = CommonExtensionServiceFactory.getCommonExtensionService();
        SearchSourceBuilder builder = new SearchSourceBuilder();
        builder.query(boolQueryBuilder);

        builder.sort(commonExtensionService.getSortedKey(logQuery, logQuery.getSortKey()), logQuery.getAsc() ? ASC : DESC);
        if (null != logQuery.getPage()) {
            builder.from((logQuery.getPage() - 1) * logQuery.getPageSize());
        }
        builder.size(logQuery.getPageSize());
        // highlight
        builder.highlighter(getHighlightBuilder(keyList));
        builder.timeout(TimeValue.timeValueMinutes(2L));
        return builder;
    }

    private HighlightBuilder getHighlightBuilder(List<String> keyList) {
        HighlightBuilder highlightBuilder = new HighlightBuilder();
        for (Pair<String, Pair<String, Integer>> requiredField : requiredFields) {
            if (!keyList.contains(requiredField.getKey())) {
                keyList.add(requiredField.getKey());
            }
        }
        for (String key : keyList) {
            if (noHighLightSet.contains(key)) {
                continue;
            }
            HighlightBuilder.Field highlightField = new HighlightBuilder.Field(key);
            highlightBuilder.field(highlightField);
        }
        return highlightBuilder;
    }

    private void setStorePeriodAndSendToMQ(MilogLogStoreDO logStoreDO) {
        logStoreDO.setStorePeriod(180);
        innerStoreExtensionService.sendStoreOperateEvent(logStoreDO, null, OperateEnum.ADD_OPERATE);
    }

    /*
     * 获取处理的 store 列表
     *
     * @param machineRoom 必填，指定迁移的机房
     *
     * @param spaceId 选填，迁移一个 space 下指定机房的 store（单测）
     *
     * @param esClusterId 选填，迁移指定机房的 store（allin）
     */
    public List<MilogLogStoreDO> getMigrateLogStoreDOList(String machineRoom, Long spaceId, Long storeId, Long esClusterId) {
        if (StringUtils.isEmpty(machineRoom)) {
            log.error("【migrate】 return early due to not specify machineRoom");
            return null;
        }
        List<MilogLogStoreDO> stores = new ArrayList<>();
        if (storeId > 0) {
            stores = milogLogstoreDao.getMilogLogstore(Collections.singletonList(storeId));
        } else if (spaceId > 0) {
            stores = milogLogstoreDao.getMilogLogstoreBySpaceIdRegion(spaceId, machineRoom);
        } else if (esClusterId > 0) {
            stores = milogLogstoreDao.queryByEsInfo(machineRoom, esClusterId);
        }
        return stores;
    }

    /*
     * 复制 topic
     *
     * @param stores 待迁移集群的 store 列表
     *
     * 如果 tail 对应的 topic 已在目标集群创建，则跳过
     */
    public void copyMiddleWareToAnotherDtSpace(List<MilogLogStoreDO> stores,
                                               MigrateDtResourceConfig config) {
        if (CollectionUtils.isEmpty(stores)) {
            log.info("【migrate】not found stores to migrate topic");
            return;
        }

        MilogMiddlewareConfig milogMiddlewareConfig = innerLogMiddlewareConfigDao
                .queryPlatformTalosConfigByRegion(config.getMachineRoom());
        if (milogMiddlewareConfig == null) {
            log.error("【migrate】copy topic return early due to null milogMiddlewareConfig");
            return;
        }
        // 判断是否需要替换集群参数
        if (!StringUtils.isEmpty(config.getAlphaToken())) {
            milogMiddlewareConfig.setToken(config.getAlphaToken());
            milogMiddlewareConfig.setDtCatalog(config.getTalosCatalog());
            milogMiddlewareConfig.setAk(config.getTalosAk());
            milogMiddlewareConfig.setSk(config.getTalosSk());
            milogMiddlewareConfig.setServiceUrl(config.getTalosServiceUrl());
        }
        // 从新的 talos 集群直接取 topic 列表
        List<DictionaryDTO> existTopics = talosMqConfigService.queryExistsTopic(milogMiddlewareConfig.getAk(),
                milogMiddlewareConfig.getSk(), milogMiddlewareConfig
                        .getNameServer(),
                milogMiddlewareConfig.getServiceUrl(), null, null, null);
        HashMap<String, String> existTopicMap = new HashMap<>();
        existTopics.forEach(topic -> existTopicMap.put(topic.getLabel(), topic.getLabel()));
        List<MilogLogTailDo> logTails = milogLogtailDao
                .getMilogLogtailByStoreIds(stores.stream().map(MilogLogStoreDO::getId).toList());
        int size = logTails.size();
        Stopwatch stopwatch = Stopwatch.createUnstarted();
        stopwatch.start();
        log.info("【migrate】copy topic total:{}", size);
        List<Long> failedTails = new CopyOnWriteArrayList<>();

        AtomicInteger count = new AtomicInteger();
        CountDownLatch countDownLatch = new CountDownLatch(size);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        ExecutorService executorService = Executors.newFixedThreadPool(Math.min(size, 10)); // 设置适当的线程池大小

        for (MilogLogTailDo logTail : logTails) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                count.incrementAndGet();
                try {
                    // topic not created by hera
                    if (StringUtils.isEmpty(logTail.getDeploySpace())) {
                        return;
                    }
                    String topicName; // create
                    List<MilogAppMiddlewareRel> middlewareRelList = milogAppMiddlewareRelDao.queryByCondition(null, milogMiddlewareConfig.getId(), logTail.getId());

                    if (!middlewareRelList.isEmpty() &&
                            null != middlewareRelList.getLast().getConfig() &&
                            null != middlewareRelList.getLast().getConfig().getTopic() &&
                            !middlewareRelList.getLast().getConfig().getTopic().isEmpty()) {
                        //这里要从数据库读已经绑定的topic名称
                        topicName = middlewareRelList.getLast().getConfig().getTopic();
                    } else {
                        // topic rel 不存在需要直接修复的情况
                        topicName = DtUtils.buildTalosTopic(logTail.getStoreId(), logTail.getId());
                    }
                    // 跳过已经存在的 topic
                    if (existTopicMap.get(topicName) != null) {
                        log.info("【migrate】topic found in current cluster, skip this topic:{}", topicName);
                        return;
                    }
                    // 创建 topic
                    MilogAppMiddlewareRel.Config middlewareRelConfig = talosMqConfigService.generatePlatformConfig(
                            logTail.getSpaceId(),
                            logTail.getStoreId(), logTail.getId(),
                            milogMiddlewareConfig);
                    if (middlewareRelConfig == null || StringUtils.isEmpty(middlewareRelConfig.getTopic())) {
                        log.error("【migrate】copy topic call dt fail, storeId:{},tailId:{}", logTail.getStoreId(),
                                logTail.getId());
                        failedTails.add(logTail.getId());
                        return;
                    }

                    // 写 milog_app_middleware_rel
                    MilogAppMiddlewareRel milogAppMiddlewareRel = generateMiddlewareRel(logTail.getId(),
                            logTail.getMilogAppId(),
                            milogMiddlewareConfig.getId(), middlewareRelConfig);
                    milogAppMiddlewareRelDao.insertUpdate(milogAppMiddlewareRel);
                } catch (Exception e) {
                    log.error("【migrate】copy topic call dt fail, storeId:{},tailId:{},e:", logTail.getStoreId(),
                            logTail.getId(), e);
                    failedTails.add(logTail.getId());

                } finally {
                    countDownLatch.countDown();
                    log.info("【migrate】copy topic total:{},start to process no. {}, left {}", size, count.get(),
                            size - count.get());
                }
            }, executorService);
            futures.add(future);
        }

        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        // 显式关闭线程池
        try {
            allOf.whenComplete((result, throwable) -> executorService.shutdown());
            allOf.get(5, TimeUnit.HOURS);
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            throw new RuntimeException(e);
        }

        stopwatch.stop();
        log.info("【migrate】copy topic finished,cost {}s, please manually check on failedTails:{}",
                stopwatch.elapsed().getSeconds(), failedTails);

    }

    private MilogAppMiddlewareRel generateMiddlewareRel(Long tailId, Long milogAppId, Long configId,
                                                        MilogAppMiddlewareRel.Config config) {
        MilogAppMiddlewareRel milogAppMiddlewareRel = new MilogAppMiddlewareRel();
        milogAppMiddlewareRel.setMilogAppId(milogAppId);
        milogAppMiddlewareRel.setMiddlewareId(configId);
        milogAppMiddlewareRel.setTailId(tailId);
        milogAppMiddlewareRel.setConfig(config);
        milogAppMiddlewareRel.setCtime(Instant.now().toEpochMilli());
        milogAppMiddlewareRel.setUtime(Instant.now().toEpochMilli());
        if (null == MoneUserContext.getCurrentUser() ||
                StringUtils.isEmpty(MoneUserContext.getCurrentUser().getUser())) {
            MilogLogTailDo milogLogTailDo = milogLogtailDao.queryById(tailId);
            if (StringUtils.isEmpty(milogLogTailDo.getCreator())) {
                milogAppMiddlewareRel.setCreator(milogLogTailDo.getCreator());
            } else {
                milogAppMiddlewareRel.setCreator("");
            }
            milogAppMiddlewareRel.setUpdater(milogLogTailDo.getUpdater());
        } else {
            milogAppMiddlewareRel.setCreator(MoneUserContext.getCurrentUser().getUser());
            milogAppMiddlewareRel.setUpdater(MoneUserContext.getCurrentUser().getUser());
        }
        return milogAppMiddlewareRel;
    }

    /*
     * 复制 es index
     *
     * @param stores 待迁移集群的 store 列表
     * 如果 store 对应的 es_index 已在目标集群创建，则跳过
     *
     */
    public void copyESToAnotherDtSpace(List<MilogLogStoreDO> stores, MigrateDtResourceConfig config) {
        if (CollectionUtils.isEmpty(stores)) {
            log.info("【migrate】not found stores to migrate es");
            return;
        }
        // 查询得到 es 集群信息
        MilogEsClusterDO cluster = esCluster.getPlatformEsCluster(config.getMachineRoom());
        if (null == cluster) {
            log.error("【migrate】copy es return early due to query es cluster failed");
            return;
        }
        // 判断是否需要替换集群参数
        if (!StringUtils.isEmpty(config.getAlphaToken())) {
            cluster.setDtCatalog(config.getEsCatalog());
            cluster.setDtDatabase(config.getEsDatabase());
            cluster.setToken(config.getAlphaToken());
        }
        String suffix = config.getSuffix();

        // 查 `milog_es_index` 表
        QueryWrapper queryWrapper = new QueryWrapper<>().eq("cluster_id", cluster.getId());
        List<MilogEsIndexDO> esIndexDOS = esIndexMapper.selectList(queryWrapper);
        HashMap<String, String> existIndexMap = new HashMap<>();
        esIndexDOS.forEach(esIndexDO -> existIndexMap.put(esIndexDO.getIndexName(), esIndexDO.getIndexName()));
        int size = stores.size();
        Stopwatch stopwatch = Stopwatch.createUnstarted();
        stopwatch.start();
        log.info("【migrate】start to copy es total:{}", size);
        List<MilogLogStoreDO> failedStores = new CopyOnWriteArrayList<>();

        AtomicInteger count = new AtomicInteger();
        CountDownLatch countDownLatch = new CountDownLatch(size);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        ExecutorService executorService = Executors.newFixedThreadPool(Math.min(size, 10)); // 设置适当的线程池大小

        for (MilogLogStoreDO store : stores) {
            String finalSuffix = suffix;
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                count.incrementAndGet();
                // es 索引要求全集群不能重名，所以需要加个后缀，后缀从接口读取
                //String indexName = DtUtils.buildEsIndex(store.getId());
                String indexName = store.getEsIndex();
                indexName = indexName + finalSuffix;

                try {
                    // 跳过已经存在的 es_index
                    if (existIndexMap.get(indexName) != null) {
                        log.info("【migrate】esIndex found in current cluster, skip this esIndex:{}", indexName);
                        return;
                    }
                    // 创建 es_index,该步骤会将创建成功的 es_index 同时写入`milog_es_index`表。
                    EsInfoDTO esInfoDTO = innerEsIndexTemplate.createPlatformEsInfoBase(store, cluster, indexName);
                    if (esInfoDTO == null) {
                        log.error("【migrate】create es index fail, storeId:{}, indexName:{},spaceId:{}", store.getId(),
                                indexName, store.getSpaceId());
                        failedStores.add(store);
                        return;
                    }
                } catch (Exception e) {
                    log.error("【migrate】copy es call dt fail, storeId:{}, indexName:{}, e:", store.getId(), indexName,
                            e);
                    failedStores.add(store);
                } finally {
                    countDownLatch.countDown();
                    log.info("【migrate】copy es total:{},start to process no. {}, left {}", size, count.get(),
                            size - count.get());
                }
            }, executorService);
            futures.add(future);
        }

        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        // 显式关闭线程池
        try {
            allOf.whenComplete((result, throwable) -> executorService.shutdown());
            allOf.get(5, TimeUnit.HOURS);
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            throw new RuntimeException(e);
        }
        stopwatch.stop();
        log.info("【migrate】copy es finished,cost {}s, please manually check on failedStores:{}",
                stopwatch.elapsed().getSeconds(), failedStores);
    }

}
