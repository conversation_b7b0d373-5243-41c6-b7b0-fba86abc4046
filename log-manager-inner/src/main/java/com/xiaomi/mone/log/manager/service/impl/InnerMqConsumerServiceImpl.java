package com.xiaomi.mone.log.manager.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.mione.tesla.k8s.bo.LogAgentListBo;
import com.xiaomi.mone.enums.InnerDeployWayEnum;
import com.xiaomi.mone.enums.InnerProjectTypeEnum;
import com.xiaomi.mone.log.manager.model.dto.K8sMachineChangeDTO;
import com.xiaomi.mone.log.manager.model.vo.K8sToAgentVo;
import com.xiaomi.mone.log.manager.service.InnerLogAgentService;
import com.xiaomi.mone.log.manager.service.MqConsumerService;
import com.xiaomi.youpin.docean.anno.Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ozhera.app.api.response.AppBaseInfo;
import org.apache.ozhera.log.common.Constant;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.model.dto.DockerScaleBo;
import org.apache.ozhera.log.manager.model.dto.PodDTO;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;
import org.apache.ozhera.log.manager.service.impl.HeraAppServiceImpl;
import org.apache.ozhera.log.manager.service.impl.MilogStreamServiceImpl;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.INNER_LOG_AGENT_SERVICE;
import static com.xiaomi.mone.log.manager.user.MoneUserDetailService.GSON;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/4/10 14:03
 */
@Service
@Slf4j
public class InnerMqConsumerServiceImpl implements MqConsumerService {

    @Resource
    private MilogLogTailDao milogLogtailDao;

    @Resource
    private HeraAppServiceImpl heraAppService;

    @Resource(name = INNER_LOG_AGENT_SERVICE)
    private InnerLogAgentService milogAgentService;

    @Resource
    private MilogStreamServiceImpl milogStreamService;

    @Override
    public void handleK8sTopicTail(K8sMachineChangeDTO machineChangeDTO) {
        log.info("handleK8sTopicTail:{}", GSON.toJson(machineChangeDTO));
        AppBaseInfo appContent = heraAppService.queryByAppId(machineChangeDTO.getAppId(), InnerProjectTypeEnum.MIONE_TYPE.getCode());
        if (null != appContent) {
            List<MilogLogTailDo> logTailDos = milogLogtailDao
                    .queryByMilogAppAndEnvK8s(Long.valueOf(appContent.getId()), machineChangeDTO.getEnvId(), InnerDeployWayEnum.MILINE.getCode());
            for (MilogLogTailDo logTailDo : logTailDos) {
                List<PodDTO> changedMachines = machineChangeDTO.getChangedMachines();
                List<PodDTO> deletingMachines = machineChangeDTO.getDeletingMachines();
                List<String> changeIps = changedMachines.stream().map(PodDTO::getPodIP).collect(Collectors.toList());
                List<String> originIps = logTailDo.getIps();
                if (CollectionUtils.isEmpty(originIps) ||
                        !CollectionUtils.isEqualCollection(changeIps, originIps)) {
                    //发送配置
                    k8sPodIpsSendMq(logTailDo, changedMachines);
                    //更新tail
                    updateLogTail(logTailDo, changeIps);
                }
                if (CollectionUtils.isNotEmpty(deletingMachines)) {
                    k8sPodDeleteSendMq(logTailDo, deletingMachines);
                }
            }
        } else {
            log.info("handleK8sTopicTail,query AppBaseInfo not exist,params:{}", GSON.toJson(machineChangeDTO));
        }
    }

    public void k8sPodDeleteSendMq(MilogLogTailDo logTailDo, List<PodDTO> deletingMachines) {
        Map<String, List<LogAgentListBo>> agentIpMap = buildAgentIpMap(deletingMachines);
        for (Map.Entry<String, List<LogAgentListBo>> agentIpEntry : agentIpMap.entrySet()) {
            log.info("k8sPodDeleteSendMq,agentIp:{},logTailDo:{},k8sToAgentVo:{}", agentIpEntry.getKey(), GSON.toJson(logTailDo), GSON.toJson(deletingMachines));
            for (LogAgentListBo logAgentListBo : agentIpEntry.getValue()) {
                milogAgentService.stopLogCollDirectoryByIp(logTailDo.getId(), logAgentListBo.getPodName(), Lists.newArrayList(agentIpEntry.getKey()));
            }
        }
    }

    private void k8sPodIpsSendMq(MilogLogTailDo logTailDo, List<PodDTO> changedMachines) {
        if (null != logTailDo.getCollectionReady() && !logTailDo.getCollectionReady()) {
            log.info("k8sPodIpsSendMq skip for tail due to not enable collection, tailId:{}, tail:{}", logTailDo.getId(), logTailDo.getTail());
            return;
        }
        Map<String, List<LogAgentListBo>> agentIpMap = buildAgentIpMap(changedMachines);
        for (Map.Entry<String, List<LogAgentListBo>> agentIpEntry : agentIpMap.entrySet()) {
            milogAgentService.configIssueAgentK8s(logTailDo.getId(), agentIpEntry.getKey(), agentIpEntry.getValue(), K8sToAgentVo.init());
        }
    }

    public void updateLogTail(MilogLogTailDo logTailDo, List<String> ipList) {
        logTailDo.setIps(ipList);
        logTailDo.setUtime(Instant.now().toEpochMilli());
        logTailDo.setUpdater(Constant.DEFAULT_OPERATOR);
        milogLogtailDao.updateIps(logTailDo);
    }

    private Map<String, List<LogAgentListBo>> buildAgentIpMap(List<PodDTO> podDTOList) {
        Map<String, List<LogAgentListBo>> agentIpMap = Maps.newHashMap();
        for (PodDTO dto : podDTOList) {
            agentIpMap.putIfAbsent(dto.getNodeIP(), Lists.newArrayList());
            LogAgentListBo logAgentListBo = new LogAgentListBo();
            logAgentListBo.setPodIP(dto.getPodIP());
            logAgentListBo.setPodName(dto.getPodName());
            logAgentListBo.setAgentIP(dto.getNodeIP());
            logAgentListBo.setAgentName(dto.getNodeName());
            agentIpMap.get(dto.getNodeIP()).add(logAgentListBo);
        }
        return agentIpMap;
    }

    /***
     * miline 动态扩缩容
     * @param projectInfo
     */
    public void dockerScaleDynamic(DockerScaleBo projectInfo) {
        List<MilogLogTailDo> milogLogtailDos = milogLogtailDao.queryByAppAndEnv(projectInfo.getProjectId(), projectInfo.getEnvId());
        if (CollectionUtils.isNotEmpty(projectInfo.getIps()) && CollectionUtils.isNotEmpty(milogLogtailDos)) {
            log.info("动态扩容当前环境下的配置，projectId:{},envId:{},配置信息:{}",
                    projectInfo.getProjectId(), projectInfo.getEnvId(), GSON.toJson(milogLogtailDos));
            for (MilogLogTailDo milogLogtailDo : milogLogtailDos) {
                if (!Objects.isNull(projectInfo.getType()) && !milogLogtailDo.getAppType().equals(projectInfo.getType())) {
                    continue;
                }
                if (!milogLogtailDo.getCollectionReady()) {
                    log.info("dockerScaleDynamic skip for tail due to not enable collection, tailId:{}, tail:{}", milogLogtailDo.getId(), milogLogtailDo.getTail());
                    continue;
                }
                List<String> exitIps = milogLogtailDo.getIps();
                List<String> newIps = projectInfo.getIps();
                if (!CollectionUtils.isEqualCollection(exitIps, newIps)) {
                    //1.修改配置
                    milogLogtailDo.setIps(newIps);
                    milogLogtailDao.updateIps(milogLogtailDo);
                    //2.发送消息
                    compareIpToHandle(exitIps, newIps);
                }
            }
        }
    }

    private void compareIpToHandle(List<String> exitIps, List<String> newIps) {
        List<String> expandIps = newIps.stream().filter(ip -> !exitIps.contains(ip)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(expandIps)) {
            // 扩容---同步配置
            expandIps.forEach(ip -> {
                milogAgentService.configIssueAgent("", ip, "");
                milogStreamService.configIssueStream(ip);
            });
        }
        List<String> narrowIps = exitIps.stream().filter(ip -> !newIps.contains(ip)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(narrowIps)) {
            // 缩容--不用管
        }
    }
}
