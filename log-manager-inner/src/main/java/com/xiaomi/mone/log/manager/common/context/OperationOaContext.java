package com.xiaomi.mone.log.manager.common.context;
import com.xiaomi.youpin.gwdash.bo.openApi.OperationLogRequest;

public class OperationOaContext {

    private static ThreadLocal<OperationLogRequest>  currentOperationOa = new ThreadLocal<>();

    public static void setCurrentOperationOa(OperationLogRequest request) {
        currentOperationOa.set(request);
    }

    public static OperationLogRequest getCurrentOperationOa() {
        return currentOperationOa.get();
    }

    public static void clear() {
        currentOperationOa.remove();
    }

}
