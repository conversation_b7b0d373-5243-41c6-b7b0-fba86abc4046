package com.xiaomi.mone.log.manager.model.dto;

import lombok.Data;
import org.apache.ozhera.log.manager.model.vo.LogQuery;

import java.util.Arrays;
import java.util.Objects;

/**
 * 日志查询类
 * @author: songyutong1
 * @date: 2024/10/28/17:44
 */
@Data
public class MergeLogQuery extends LogQuery {

    private String appType;

    private LogMeta logMeta;

    private String area;

    private String spaceId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MergeLogQuery logQuery = (MergeLogQuery) o;
        return Objects.equals(getLogstore(), logQuery.getLogstore()) && Objects.equals(getTail(), logQuery.getTail()) && Objects.equals(getStartTime(), logQuery.getStartTime()) && Objects.equals(getEndTime(), logQuery.getEndTime()) && Objects.equals(getPage(), logQuery.getPage()) && Arrays.equals(getBeginSortValue(), logQuery.getBeginSortValue()) && Objects.equals(getFullTextSearch(), logQuery.getFullTextSearch()) && Objects.equals(getSortKey(), logQuery.getSortKey()) && Objects.equals(getAsc(), logQuery.getAsc()) && Arrays.equals(getAppIds(), logQuery.getAppIds()) && Objects.equals(getAppType(), logQuery.getAppType()) && Objects.equals(getArea(), logQuery.getArea()) && Objects.equals(getSpaceId(), logQuery.getSpaceId()) && Objects.equals(getLogMeta(), logQuery.getLogMeta());
    }

    @Override
    public int hashCode() {
        int result = Objects.hash(getLogstore(), getTail(), getStartTime(), getEndTime(), getPageSize(), getFullTextSearch(), getSortKey(), getAsc(), getAppType(), getArea(), getSpaceId(), getLogMeta());
        result = 31 * result + Arrays.hashCode(getBeginSortValue());
        result = 31 * result + Arrays.hashCode(getAppIds());
        return result;
    }

    public void validate() {
        if (Objects.isNull(this.logMeta)) {
            LogMeta logMeta = new LogMeta();
            logMeta.setStoreId(getStoreId());
            logMeta.setLogStore(getLogstore());
            logMeta.setTail(getTail());
            logMeta.setAppIds(getAppIds());
            logMeta.setSpaceId(Long.valueOf(getSpaceId()));
            this.logMeta = logMeta;
        }
    }

}
