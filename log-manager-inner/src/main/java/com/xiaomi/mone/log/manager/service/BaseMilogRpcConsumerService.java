package com.xiaomi.mone.log.manager.service;

import com.alibaba.nacos.api.config.ConfigService;
import com.google.common.collect.Lists;
import com.xiaomi.mone.miline.api.dto.PipelineDeployDto;
import com.xiaomi.mone.miline.api.dto.milog.ProjectInstanceDto;
import com.xiaomi.youpin.docean.Ioc;
import com.xiaomi.youpin.gwdash.bo.SimplePipleEnvBo;
import org.apache.dubbo.rpc.RpcContext;

import java.util.List;

import static org.apache.ozhera.log.common.Constant.*;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/9/7 17:25
 */
public abstract class BaseMilogRpcConsumerService implements MilogRpcConsumerService {

    public List<String> getLiveMachines() throws Exception {
        ConfigService configService = Ioc.ins().getBean(ConfigService.class);
        String dashToken = configService.getConfig(String.format("%s%s", LOG_MANAGE_PREFIX, LOG_MANAGE_GWDASH_TOKEN), DEFAULT_GROUP_ID, DEFAULT_TIME_OUT_MS);
        RpcContext.getContext().setAttachment("_milog_special_token", dashToken);
        return queryLiveMachines();
    }

    protected abstract List<String> queryLiveMachines();

    public abstract PipelineDeployDto qryDeployInfo(long projectId, long pipelineId);

    @Override
    public List<SimplePipleEnvBo> queryStagingSimplePipleEnvBoByProjectId(Long projectId, String env) {
        return Lists.newArrayList();
    }

    public ProjectInstanceDto queryMachineInfoByProjectNew(Long projectId, List<Long> pipelineIds) {
        return null;
    }

}
