package com.xiaomi.mone.log.manager.service.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.gson.Gson;
import com.xiaomi.data.push.client.HttpClientV6;
import com.xiaomi.mione.tesla.k8s.bo.LogAgentListBo;
import com.xiaomi.mone.log.manager.model.dto.AgentPodListRespDTO;
import com.xiaomi.youpin.docean.anno.Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.ozhera.log.common.Config;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Description
 * @auther tangdong
 * @create 2024-06-21 12:08 下午
 */
@Service
@Slf4j
public class CloudMLLogServiceImpl {
    private final Gson GSON = new Gson();

    private static final Cache<String, String> CACHE_LOCAL_HOST_IPS = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build();

    /**
     * 用 hostIP 查 trace 接口（/tracing/v1/app/log/search-ip:hostIP）获取到 podList
     * *
     *
     * @param hostIP hostIP， 也就是 agentIP
     */
    public List<LogAgentListBo> queryPodListByIp(String hostIP) {
        String mioapDomain = Config.ins().get("mioap_domain", "http://staging-mioap.api.xiaomi.net");
        String traceAppInfoApi = "/tracing/v1/app/log/search-ip";
        String url = String.format("%s%s?hostIP=%s", mioapDomain, traceAppInfoApi, hostIP);
        List<LogAgentListBo> podInfoList = null;
        try {
            String podListResp = HttpClientV6.get(url, null, 10000);
            AgentPodListRespDTO podListRespDTO = GSON.fromJson(podListResp, AgentPodListRespDTO.class);
            if (null == podListRespDTO.getData()) {
                log.error("query cloudml podlist return error, exit early. hostIP:{}, resp:{}", hostIP, podListResp);
                return null;
            }
            podInfoList = podListRespDTO.getData();
        } catch (Exception e) {
            log.error("query cloudml podlist exception, exit early. hostIP:{}, exception:{}", hostIP, e);
        }
        if (podInfoList == null) {
            return null;
        }

        // agent 初始化时将 agent name->agentIp 存在缓存中作为兜底
        for (LogAgentListBo pod : podInfoList) {
            //agent ip query
            if (Objects.equals(pod.getAgentIP(), hostIP)) {
                CACHE_LOCAL_HOST_IPS.put(pod.getAgentName(), pod.getAgentIP());
                break;
            }
            //pod ip query
            if (StringUtils.isNotEmpty(pod.getAgentName())) {
                CACHE_LOCAL_HOST_IPS.put(pod.getAgentName(), pod.getAgentIP());
            }
        }
        return podInfoList;
    }
}
