package com.xiaomi.mone.log.manager.service.alert;

import com.google.gson.Gson;
import com.xiaomi.infra.galaxy.rpc.thrift.Credential;
import com.xiaomi.infra.galaxy.rpc.thrift.UserType;
import com.xiaomi.infra.galaxy.talos.client.SimpleTopicAbnormalCallback;
import com.xiaomi.infra.galaxy.talos.producer.TalosProducer;
import com.xiaomi.infra.galaxy.talos.producer.TalosProducerConfig;
import com.xiaomi.infra.galaxy.talos.producer.UserMessageCallback;
import com.xiaomi.infra.galaxy.talos.producer.UserMessageResult;
import com.xiaomi.infra.galaxy.talos.thrift.Message;
import com.xiaomi.mone.log.manager.dao.alert.AlertDao;
import com.xiaomi.mone.log.manager.model.alert.AlertRule;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import com.xiaomi.youpin.mione.FlinkAlertRule;
import com.xiaomi.youpin.mione.FlinkRuleParams;
import libthrift091.TException;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

@Service
@Slf4j
public class SendAlertRulesService {

    private Gson gson = new Gson();
    private TalosProducer producer;

    @Resource
    private FlinkService flinkService;

    @Resource
    private AlertDao alertDao;

    private static String CONTROL_TOPIC = "hera_alarm_control";
    @Value(value = "$team.access_key")
    private String accessKey;

    @Value(value = "$team.secret_key")
    private String secretKey;

    @Value(value = "$producer.server")
    private String producerServer;

    @PostConstruct
    public void init() throws TException {
        Properties pros = new Properties();
        pros.setProperty("galaxy.talos.service.endpoint", producerServer);
        pros.setProperty("galaxy.talos.client.falcon.monitor.switch", "false");
        TalosProducerConfig producerConfig = new TalosProducerConfig(pros);

        // credential
        Credential credential = new Credential();
        credential.setSecretKeyId(accessKey)
                .setSecretKey(secretKey)
                .setType(UserType.DEV_XIAOMI);


        producer = new TalosProducer(producerConfig, credential,
                CONTROL_TOPIC, new SimpleTopicAbnormalCallback(), new MyMessageCallback());
    }

    public void updateAlertRules(long alertId, List<AlertRule> rules) {
        try {
            FlinkRuleParams params = new FlinkRuleParams();
            params.setAlertId(alertId);
            params.setActionTime(System.currentTimeMillis());
            ArrayList<FlinkAlertRule> alertRules = new ArrayList<>();
            params.setAlertRules(alertRules);
            for (AlertRule current : rules) {
                FlinkAlertRule flinkAlertRule = new FlinkAlertRule();
                flinkAlertRule.setRuleId(current.getId());
                flinkAlertRule.setFilterRegex(current.getRegex());
                alertRules.add(flinkAlertRule);
            }
            List<Message> messageList = new ArrayList<Message>();
            Message message = new Message(ByteBuffer.wrap(gson.toJson(params).getBytes()));
            messageList.add(message);
            producer.addUserMessage(messageList);
        } catch (Exception e) {
            log.error("updateAlertRules error", e);
        }
    }

    private static class MyMessageCallback implements UserMessageCallback {
        // count when success
        @Override
        public void onSuccess(UserMessageResult userMessageResult) {


        }

        // retry when failed
        @Override
        public void onError(UserMessageResult userMessageResult) {

        }
    }
}
