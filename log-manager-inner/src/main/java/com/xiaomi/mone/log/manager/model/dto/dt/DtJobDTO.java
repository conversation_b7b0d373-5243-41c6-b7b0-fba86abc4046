package com.xiaomi.mone.log.manager.model.dto.dt;

import lombok.Data;

import java.io.Serializable;

/**
 * 工场作业信息
 *
 * @author: songyutong1
 * @date: 2024/09/04/12:41
 */
@Data
public class DtJobDTO implements Serializable {
    private Long jobId;
    private String jobName;
    private String jobProcessingType;
    private String jobType;
    private String jobDesc;
    private Long lastRunningTime;
    private String lastRunningStatus;
    private Integer lastRunningVersion;
    private Long workflowId;
    private String workflowName;
    private Integer workflowVersion;
    private String cron;
    private Integer lastVersion;
    private Integer scheduleVersion;
    private boolean isScheduleLastVersion;
    private String description;
    private String owner;
    private Long createTime;
    private String onlineState;
    private Long workspaceId;
    private String workspaceName;
}
