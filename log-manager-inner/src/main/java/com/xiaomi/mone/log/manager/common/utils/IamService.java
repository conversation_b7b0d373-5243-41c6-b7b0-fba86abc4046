package com.xiaomi.mone.log.manager.common.utils;

import com.xiaomi.cloud.client.Client;
import com.xiaomi.cloud.client.Request;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import org.apache.ozhera.log.common.Config;

@Slf4j
public class IamService {
    // 创建连接池管理器
    private static final PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
    private static CloseableHttpClient client;

    static {
        // 配置连接池参数
        connectionManager.setMaxTotal(200); // 最大连接数
        connectionManager.setDefaultMaxPerRoute(50); // 每个路由的最大连接数

        // 配置请求参数
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(5000) // 连接超时时间
                .setSocketTimeout(5000) // 读取超时时间
                .build();

        // 创建 HTTP 客户端
        client = HttpClients.custom()
                .setConnectionManager(connectionManager) // 设置连接池
                .setDefaultRequestConfig(requestConfig) // 设置默认请求配置
                .build();
    }

    public static String requestIam(String method, String url, String body, Long treeId) {
        Request request = new Request();

        try {
            request.setKey(Config.ins().get("iam_ak", ""));
            request.setSecret(Config.ins().get("iam_sk", ""));
            request.setMethod(method);
            request.setUrl(url);
            request.addHeader("Content-Type", "text/plain");
            request.addHeader("X-IAM-TreeId", String.valueOf(treeId));
            request.setBody(body);
        } catch (Exception e) {
            log.error("requestIam data error", e);
            return null;
        }

        try {
            HttpRequestBase signedRequest = Client.sign(request);

            HttpResponse response = client.execute(signedRequest);

            HttpEntity resEntity = response.getEntity();
            return EntityUtils.toString(resEntity, "UTF-8");
        } catch (Exception exception2) {
            log.error("client execute error", exception2);
        }
        return "";
    }
}
