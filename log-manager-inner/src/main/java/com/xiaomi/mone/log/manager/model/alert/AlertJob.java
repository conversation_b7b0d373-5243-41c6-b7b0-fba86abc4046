package com.xiaomi.mone.log.manager.model.alert;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/10/18 9:53
 */
@Data
public class AlertJob {

    private List<Job> jobList;

    private Page paging;

    @Data
    public static class Job {
        private Long jobId;
        private String jobName;
        private String jobProcessingType;
        private String jobType;
        private String jobDesc;
        private Long lastRunningTime;
        private String lastRunningStatus;
        private Integer lastRunningVersion;
        private Integer workflowId;
        private String cron;
        private Integer lastVersion;
        private Integer scheduleVersion;
        private String description;
        private String owner;
        private Long createTime;
    }

    @Data
    public static class Page {
        private Integer page;
        private Integer pageSize;
        private Integer total;

    }
}
