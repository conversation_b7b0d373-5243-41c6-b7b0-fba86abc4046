package com.xiaomi.mone.log.manager.service;

import com.google.gson.JsonObject;
import com.xiaomi.mone.log.manager.model.dto.MergeLogQuery;
import org.apache.ozhera.log.common.Result;
import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 合并日志查询
 * @date 2024/10/28/11:10
 */
public interface MergeLogDataService {

    Result<JsonObject> logQuery(MergeLogQuery logQuery) throws IOException;

    Result<JsonObject> logStatistic(MergeLogQuery logQuery) throws IOException;
}
