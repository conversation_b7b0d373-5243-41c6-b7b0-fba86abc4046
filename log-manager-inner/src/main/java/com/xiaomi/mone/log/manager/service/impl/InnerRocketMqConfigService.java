package com.xiaomi.mone.log.manager.service.impl;


import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import com.google.gson.reflect.TypeToken;
import com.xiaomi.data.push.client.HttpClientV5;
import com.xiaomi.data.push.client.HttpClientV6;
import com.xiaomi.mone.log.manager.service.InnerMqConfigService;
import com.xiaomi.youpin.docean.anno.Service;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.common.Constant;
import org.apache.ozhera.log.manager.common.Utils;
import org.apache.ozhera.log.manager.common.exception.MilogManageException;
import org.apache.ozhera.log.manager.dao.MilogAppMiddlewareRelDao;
import org.apache.ozhera.log.manager.dao.MilogMiddlewareConfigDao;
import org.apache.ozhera.log.manager.model.dto.DictionaryDTO;
import org.apache.ozhera.log.manager.model.dto.RocketMqResponseDTO;
import org.apache.ozhera.log.manager.model.dto.TopicInfo;
import org.apache.ozhera.log.manager.model.pojo.MilogAppMiddlewareRel;
import org.apache.ozhera.log.manager.model.pojo.MilogMiddlewareConfig;
import org.apache.ozhera.log.manager.service.CommonRocketMqService;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;

import static org.apache.ozhera.log.common.Constant.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2021/9/23 11:31
 */
@Service
@Slf4j
public class InnerRocketMqConfigService implements InnerMqConfigService, CommonRocketMqService {

    private Gson gson = new Gson();
    @Resource
    private MilogAppMiddlewareRelDao milogAppMiddlewareRelDao;
    @Resource
    private MilogMiddlewareConfigDao milogMiddlewareConfigDao;

    private Map<String, DefaultMQProducer> mqMap = new HashMap<>();

    @Override
    public MilogAppMiddlewareRel.Config generateConfig(String ak, String sk, String nameServer, String serviceUrl,
                                                       String authorization, String orgId, String teamId, Long exceedId,
                                                       String name, String source, Long id) {
        MilogAppMiddlewareRel.Config config = new MilogAppMiddlewareRel.Config();
        // 通过Http的方式查询已经存在的topic
        List<TopicInfo> existTopics = Lists.newArrayList();
        String returnGet = HttpClientV6.get(serviceUrl + "/topic/getTopicList", getSendMqHeader(authorization));
        log.info("【RocketMQ查询topic列表】返回值:{}", returnGet);
        RocketMqResponseDTO<List<LinkedTreeMap>> responseDTO = gson.fromJson(returnGet, RocketMqResponseDTO.class);
        try {
            if (responseDTO.getCode().compareTo(Constant.SUCCESS_CODE) == 0) {
                log.info("【RocketMQ查询topic列表】:成功", returnGet);
                List<LinkedTreeMap> dataList = responseDTO.getData();
                dataList.forEach(data -> {
                    TopicInfo topicInfo = gson.fromJson(gson.toJson(data), TopicInfo.class);
                    if (Objects.equals(orgId, topicInfo.getOrgId()) && StringUtils.endsWithIgnoreCase(topicInfo.getName(), COMMON_MESSAGE)) {
                        existTopics.add(topicInfo);
                    }
                });
            } else {
                log.error("【RocketMQ查询topic列表】:失败,失败原因：{}", returnGet);
            }
        } catch (Exception e) {
            log.error(String.format("【RocketMQ查询topic列表】:返回值转化异常，返回值：%s:", returnGet), e);
        }
        TopicInfo topicInfo = dealWithTopicName(existTopics);
        config.setTopic(topicInfo.getName());
        config.setPartitionCnt(topicInfo.getQueueTotalCount());
        return config;
    }

    @Override
    public List<DictionaryDTO> queryExistsTopic(String ak, String sk, String nameServer, String serviceUrl, String authorization, String orgId, String teamId) {
        List<DictionaryDTO> dictionaryDTOS = Lists.newArrayList();
        String returnGet = HttpClientV6.get(rocketmqAddress + "/topic/getTopicList", getSendMqHeader(authorization));
        log.info("【RocketMQ查询topic列表】返回值:{}", returnGet);
        try {
            RocketMqResponseDTO<List<LinkedTreeMap>> responseDTO = gson.fromJson(returnGet, RocketMqResponseDTO.class);

            if (responseDTO.getCode().compareTo(Constant.SUCCESS_CODE) == 0) {
                log.info("【RocketMQ查询topic列表】:成功", returnGet);
                List<LinkedTreeMap> dataList = responseDTO.getData();
                dataList.forEach(data -> {
                    TopicInfo topicInfo = gson.fromJson(gson.toJson(data), TopicInfo.class);
                    if (Objects.equals(orgId, topicInfo.getOrgId())) {
                        DictionaryDTO<String> dictionaryDTO = new DictionaryDTO<>();
                        dictionaryDTO.setValue(topicInfo.getName());
                        dictionaryDTO.setLabel(topicInfo.getName());
                        dictionaryDTOS.add(dictionaryDTO);
                    }
                });
            } else {
                log.error("【RocketMQ查询topic列表】:失败,失败原因：{}", returnGet);
            }
        } catch (Exception e) {
            log.error(String.format("【RocketMQ查询topic列表】:返回值转化异常，返回值：%s:", returnGet), e);
        }
        return dictionaryDTOS;
    }

    @Override
    public List<String> createCommonTagTopic(String ak, String sk, String nameServer, String serviceUrl, String authorization, String orgId, String brokerName) {
        DefaultMQProducer defaultMQProducer = mqMap.get(nameServer);
        if (null == defaultMQProducer) {
            defaultMQProducer = new DefaultMQProducer("hera_log");
            defaultMQProducer.setNamesrvAddr(nameServer);
            try {
                defaultMQProducer.start();
            } catch (MQClientException e) {
                log.info("create mq producer error,nameServer:{}", nameServer, e);
                throw new MilogManageException("create mq producer error", e);
            }
            mqMap.put(nameServer, defaultMQProducer);
        }
        List<String> commonTagTopicNames = generateCommonTagTopicName(orgId);
        try {
            for (String commonTagTopicName : commonTagTopicNames) {
//                String brokerName = "tj1-b2c-systech-infra03.kscn";
                defaultMQProducer.createTopic(brokerName, commonTagTopicName, 1);
            }
        } catch (MQClientException e) {
            log.info("create mq common topic error,nameServer:{}", nameServer, e);
            throw new MilogManageException("create mq common topic error", e);
        }
        return commonTagTopicNames;
    }

    @Override
    public boolean CreateGroup(String s, String s1, String s2) {
        return false;
    }

    /**
     * 1.先查询是否存在
     * 2.不存在则创建
     */
    public boolean createSubscribeGroup(String serviceUrl, String authorization, String orgId,
                                        Long spaceId, Long storeId, Long tailId, Long milogAppId) {
        String groupName = DEFAULT_CONSUMER_GROUP + Utils.createTag(spaceId, storeId, tailId);
        List<RocketMqResponseDTO.SubGroup> subGroups = querySubGroupList(serviceUrl, authorization, orgId);
        List<RocketMqResponseDTO.SubGroup> groupList = subGroups.parallelStream().filter(subGroup -> subGroup.getName().equals(groupName)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(groupList)) {
            return true;
        }
        String returnPost = HttpClientV6.post(serviceUrl + "/subGroup/createSubGroup", createConsumerGroupParams(groupName, orgId),
                getSendMqHeader(authorization));
        log.info("【RocketMQ创建ConsumerGroup】返回值:{}", returnPost);
        RocketMqResponseDTO<Boolean> responseDTO = gson.fromJson(returnPost, RocketMqResponseDTO.class);
        if (responseDTO.getCode().compareTo(Constant.SUCCESS_CODE) == 0) {
            log.info("【RocketMQ创建ConsumerGroup】:成功,{}", returnPost);
            return true;
        } else {
            log.error("【RocketMQ创建ConsumerGroup】:失败,失败原因：{}", returnPost);
        }
        return false;
    }

    public List<RocketMqResponseDTO.SubGroup> querySubGroupList(String serviceUrl, String authorization, String orgId) {
        String querySubGroupListGet = HttpClientV6.get(serviceUrl + "/subGroup/querySubGroupList", getSendMqHeader(authorization));
        log.info("【RocketMQ查询ConsumerGroup】,url:{}返回值:{}", serviceUrl, querySubGroupListGet);
        RocketMqResponseDTO<List<RocketMqResponseDTO.SubGroup>> responseDTO = new Gson().fromJson(querySubGroupListGet, new TypeToken<RocketMqResponseDTO<List<RocketMqResponseDTO.SubGroup>>>() {
        }.getType());
        if (responseDTO.getCode().compareTo(Constant.SUCCESS_CODE) == 0
                && CollectionUtils.isNotEmpty(responseDTO.getData())) {
            return responseDTO.getData().stream().filter(subGroup -> subGroup.getOrgId().equals(orgId)).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    public boolean deleteSubscribeGroup(String serviceUrl, String authorization, String orgId, Long spaceId, Long storeId, Long tailId) {
        String groupName = DEFAULT_CONSUMER_GROUP + Utils.createTag(spaceId, storeId, tailId);
        String rocketmqAddress = CommonRocketMqService.rocketmqAddress + "/subGroup/deleteSubGroup/" + groupName;
        HttpClientV5.HttpResult httpResult = HttpClientV5.request(rocketmqAddress,
                getSendMqHeader2List(authorization), null, "", METHOD_DELETE);
        log.info("【RocketMQ删除ConsumerGroup】返回值:{}", httpResult.content);
        RocketMqResponseDTO<Boolean> responseDTO = gson.fromJson(httpResult.content, RocketMqResponseDTO.class);
        if (responseDTO.getCode().compareTo(Constant.SUCCESS_CODE) == 0) {
            log.info("【RocketMQ删除ConsumerGroup】:成功,{}", httpResult.content);
            return true;
        } else {
            log.error("【RocketMQ删除ConsumerGroup】:失败,失败原因：{}", httpResult.content);
        }
        return false;
    }

    public boolean generateTopicTag(Long configId, Long milogAppId, Long spaceId, Long storeId, Long tailId) {
        String tag = Utils.createTag(spaceId, storeId, tailId);
        List<MilogAppMiddlewareRel> milogAppMiddlewareRels = milogAppMiddlewareRelDao.queryByCondition(milogAppId, configId, tailId);
        if (CollectionUtils.isNotEmpty(milogAppMiddlewareRels)) {
            milogAppMiddlewareRels.forEach(milogAppMiddlewareRel -> {
                MilogAppMiddlewareRel.Config config = milogAppMiddlewareRel.getConfig();
                String oldTag = config.getTag();
                StringBuilder sb = new StringBuilder();
                if (StringUtils.isNotEmpty(oldTag)) {
                    sb.append(oldTag).append(SYMBOL_COMMA);
                }
                sb.append(tag);
                config.setTag(sb.toString());
                milogAppMiddlewareRelDao.updateTopicRelMqConfig(milogAppMiddlewareRel.getId(), config);
            });
        }
        return true;
    }

    private TopicInfo dealWithTopicName(List<TopicInfo> existTopics) {
        Collections.shuffle(existTopics);
        int randomIndex = new Random().nextInt(existTopics.size());
        return existTopics.get(randomIndex);
    }

    public Set<String> queryExistTopic(Long middlewareId) {
        MilogMiddlewareConfig middlewareConfig = milogMiddlewareConfigDao.queryById(middlewareId);
        List<DictionaryDTO> dictionaryDTOS = queryExistsTopic(middlewareConfig.getAk(), middlewareConfig.getSk(), middlewareConfig.getNameServer(),
                middlewareConfig.getServiceUrl(), middlewareConfig.getAuthorization(), middlewareConfig.getOrgId(), middlewareConfig.getTeamId());
        Set<String> existTopics = dictionaryDTOS.stream().map(dictionaryDTO -> dictionaryDTO.getValue().toString()).collect(Collectors.toSet());
        return existTopics;
    }

    @Override
    public MilogAppMiddlewareRel.Config generatePlatformConfig(Long spaceId, Long storeId, Long tailId, MilogMiddlewareConfig milogMiddlewareConfig) {
        return null;
    }

    @Override
    public void deletePlatformTopic(String topicName, MilogMiddlewareConfig milogMiddlewareConfig) {

    }

    @Override
    public MilogAppMiddlewareRel.Config generatePlatformConfigWithoutCreate(Long spaceId, Long storeId, Long tailId, MilogMiddlewareConfig milogMiddlewareConfig) {
        return null;
    }
}
