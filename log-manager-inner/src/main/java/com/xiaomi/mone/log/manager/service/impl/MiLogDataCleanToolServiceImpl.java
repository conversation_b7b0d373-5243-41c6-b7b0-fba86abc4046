package com.xiaomi.mone.log.manager.service.impl;

import com.xiaomi.mone.log.manager.common.context.MoneUserContext;
import com.xiaomi.mone.log.manager.enums.ClearStageEnum;
import com.xiaomi.mone.log.manager.model.dto.dt.DtCleanResult;
import com.xiaomi.mone.log.manager.model.vo.ClearDtResourceParam;
import com.xiaomi.mone.log.manager.service.MiLogDataCleanToolService;
import com.xiaomi.mone.log.manager.service.cleanstrategy.AbstractCleanStrategy;
import com.xiaomi.mone.log.manager.service.cleanstrategy.CleanStrategyFactory;
import com.xiaomi.mone.log.manager.user.MoneUser;
import com.xiaomi.youpin.docean.anno.Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.common.Result;
import org.apache.ozhera.log.exception.CommonError;
import org.apache.ozhera.log.manager.common.exception.MilogManageException;

import java.util.UUID;

/**
 * @author: songyutong1
 * @date: 2024/09/02/17:30
 */
@Slf4j
@Service
public class MiLogDataCleanToolServiceImpl implements MiLogDataCleanToolService {

    @Override
    public Result<String> asyncCleanInconsistentData(ClearDtResourceParam param) {
        String validate = param.validate();
        if (StringUtils.isNotEmpty(validate)) {
            return Result.failParam(validate);
        }
        String uuid = UUID.randomUUID().toString();
        MoneUser moneUser = MoneUserContext.getCurrentUser();
        new Thread(() -> {
            cleanInconsistentData(param, uuid, moneUser);
        }).start();
        return Result.success(uuid);
    }

    @Override
    public Result<DtCleanResult> cleanInconsistentData(ClearDtResourceParam param, String uuid, MoneUser user) {
        MoneUserContext.setCurrentUser(user);
        for (Integer stageCode : param.getStageList()) {
            ClearStageEnum stage = ClearStageEnum.getStage(stageCode);
            if (ObjectUtils.isEmpty(stage)) {
                return Result.fail(CommonError.ParamsError.getCode(), "stageList contains illegal stageCode");
            }
            AbstractCleanStrategy cleanStrategy = CleanStrategyFactory.getCleanStrategy(stage);
            try {
                cleanStrategy.clean(param, uuid);
            } catch (Exception e) {
                log.error("uuid:{}, clean inconsistent data failed, stage:{}, error:{}", uuid, stage.getCode(), e.getMessage(), e);
                throw new MilogManageException(e);
            }
        }
        log.info("uuid:{}, clean inconsistent data finished", uuid);
        return Result.success();
    }
}
