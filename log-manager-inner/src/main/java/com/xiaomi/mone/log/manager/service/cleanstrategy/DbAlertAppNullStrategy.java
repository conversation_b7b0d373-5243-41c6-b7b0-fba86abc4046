package com.xiaomi.mone.log.manager.service.cleanstrategy;

import com.xiaomi.mone.log.manager.dao.alert.AlertDao;
import com.xiaomi.mone.log.manager.model.alert.Alert;
import com.xiaomi.mone.log.manager.model.vo.ClearDtResourceParam;
import com.xiaomi.mone.log.manager.service.alert.AlertService;
import com.xiaomi.youpin.docean.anno.Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.ozhera.app.api.response.AppBaseInfo;
import org.apache.ozhera.log.manager.service.impl.HeraAppServiceImpl;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;

/**
 * 清理应用已经不存在的alert
 *
 * @author: songyutong1
 * @date: 2024/09/19/16:58
 */
@Service
@Slf4j
public class DbAlertAppNullStrategy extends AbstractCleanStrategy{

    @Resource
    private AlertDao alertDao;

    @Resource
    private AlertService alertService;

    @Resource
    private HeraAppServiceImpl heraAppService;

    @Override
    public void clean(ClearDtResourceParam param, String uuid) {
        log.info("uuid:{}, start fix alert tail not exist", uuid);
        List<String> alertDelete = new ArrayList<>();
        List<Alert> alertList = alertDao.queryAlertByIdAndFlinkClusterFuzzy(null, param.getFlinkCluster());
        alertList.forEach(alert -> {
            if (appExist(alert.getMilogAppId())) {
                return;
            }
            alertDelete.add(alert.getFlinkJobName());
            if (Boolean.FALSE.equals(param.getClearFlag())) {
                return;
            }
            alertService.deleteAlert(alert.getId());
        });
        log.info("uuid:{}, fix alert tail not exist, delete alert:{}", uuid, alertDelete);
    }

    private boolean appExist(Long logAppId) {
        if (ObjectUtils.isEmpty(logAppId)) {
            return false;
        }
        AppBaseInfo appBaseInfo = heraAppService.queryById(logAppId);
        return ObjectUtils.isNotEmpty(appBaseInfo);
    }
}
