package com.xiaomi.mone.log.manager.common;

import com.sun.management.HotSpotDiagnosticMXBean;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.lang.management.ManagementFactory;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025/4/7 15:10
 */
@Slf4j
public class OOMHandler implements Thread.UncaughtExceptionHandler {
    private static final String DUMP_PATH = "/home/<USER>/log/log-manager/dump-manager.hprof";

    @Override
    public void uncaughtException(Thread t, Throwable e) {
        if (e instanceof OutOfMemoryError) {
            log.info("OutOfMemoryError detected, generating heap dump...");
            try {
                HotSpotDiagnosticMXBean bean = ManagementFactory.getPlatformMXBean(HotSpotDiagnosticMXBean.class);
                bean.dumpHeap(DUMP_PATH, true);
                log.info("Heap dump generated to: {}", DUMP_PATH);
            } catch (IOException ex) {
                log.error("Failed to generate heap dump", e);
            }
        }
    }
}
