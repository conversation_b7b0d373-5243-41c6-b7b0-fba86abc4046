package com.xiaomi.mone.log.manager.model.bo.alert;

import lombok.Data;

import java.util.List;

import com.xiaomi.mone.log.manager.model.alert.AlertGroup;

@Data
public class AlertUpdateParam {

    private long alertId;
    private String name;
    private boolean updateContacts;
    private String contacts;
    private String feishuGroups;
    private List<AlertGroup> alertGroups;
    private String atMembers;
    private List<Long> tailIds;
    private String regionEn;

    private String mqType;

    private String type;

    private String callbackUrl;

    private Integer windowSize;

    private List<AlertRuleParam> rules;

    public AlertParam toAlertParam() {
        AlertParam alertParam = new AlertParam();
        alertParam.setAlertId(this.alertId);
        alertParam.setName(this.name);
        alertParam.setType(this.type);
        alertParam.setMqType(this.mqType);
        alertParam.setRules(this.rules);
        alertParam.setRegionEn(this.regionEn);
        alertParam.setWindowSize(this.windowSize);
        return alertParam;
    }


}
