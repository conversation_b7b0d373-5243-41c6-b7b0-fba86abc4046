package com.xiaomi.mone.log.manager.model.bo.alert;

import com.xiaomi.mone.log.manager.model.alert.Alert;
import com.xiaomi.mone.log.manager.model.bo.LogServiceMeta;
import com.xiaomi.mone.log.manager.service.alert.RemoteSendAlertLink;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * @description
 * @version 1.0
 * <AUTHOR>
 * @date 2024/7/1 10:37
 *
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CallBackArgument {
    private Alert alert;
    private RemoteSendAlertLink.Meta meta;
    private String traceId;
    private String message;
    private String ruleName;
    private String ruleRegex;
    private String logPath;

    // 报警级别
    private String level;
    // 报警值
    private Integer alarmValue;
    // 报警描述
    private String alarmDesc;

    // 服务元数据信息
    private LogServiceMeta serviceInfo;
}
