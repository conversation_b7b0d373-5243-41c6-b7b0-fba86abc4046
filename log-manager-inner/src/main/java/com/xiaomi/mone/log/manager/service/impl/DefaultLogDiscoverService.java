package com.xiaomi.mone.log.manager.service.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.xiaomi.mone.enums.InnerLogTypeEnum;
import com.xiaomi.mone.log.manager.service.InnerCommonExtensionService;
import com.xiaomi.mone.log.manager.service.LogDiscoverService;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.es.EsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.manager.common.exception.MilogManageException;
import org.apache.ozhera.log.manager.dao.MilogLogstoreDao;
import org.apache.ozhera.log.manager.domain.EsCluster;
import org.apache.ozhera.log.manager.domain.SearchLog;
import org.apache.ozhera.log.manager.model.pojo.MilogLogStoreDO;
import org.apache.ozhera.log.manager.model.vo.LogQuery;
import org.apache.ozhera.log.manager.service.extension.common.DefaultCommonExtensionService;
import org.apache.ozhera.log.manager.service.impl.EsDataServiceImpl;
import org.elasticsearch.action.fieldcaps.FieldCapabilities;
import org.elasticsearch.action.fieldcaps.FieldCapabilitiesRequest;
import org.elasticsearch.action.fieldcaps.FieldCapabilitiesResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.INNER_COMMON_SERVICE;
import static org.apache.ozhera.log.common.Constant.GSON;
import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getKeyColonPrefix;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/11/18 15:28
 */
@Slf4j
@Service
public class DefaultLogDiscoverService implements LogDiscoverService {
    @Resource
    private EsCluster esCluster;
    @Resource
    private MilogLogstoreDao logStoreDao;
    @Resource
    private SearchLog searchLog;
    @Resource(name = INNER_COMMON_SERVICE)
    private InnerCommonExtensionService commonExtensionService;
    @Resource
    private Gson gson;

    private static final Executor customCoroutinePool = Executors.newThreadPerTaskExecutor(
            Thread.ofVirtual()
                    .name("aggregate-fields-pool", 0)
                    .factory());

    @Override
    public List<FieldCapabilities> getAggregateFields(LogQuery logQuery) {
        log.info("getAggregateFields params: {}", gson.toJson(logQuery));
        MilogLogStoreDO logStoreDO = logStoreDao.queryById(logQuery.getStoreId());
        //如果查询的结束时间小于7天，则直接返回
        if (InnerLogTypeEnum.LOKI_APP_LOG.getType().equals(logStoreDO.getLogType()) ||
                (System.currentTimeMillis() - logQuery.getEndTime() >= 7 * 24 * 60 * 60 * 1000)) {
            return Lists.newArrayList();
        }
        try {
            return aggregateFieldsCache.get(logQuery.getStoreId(), () -> loadAggregateFields(logQuery));
        } catch (ExecutionException e) {
            log.error("Error in loading aggregate fields,logQuery:{}", gson.toJson(logQuery), e);
        }
        return Lists.newArrayList();
    }

    private static final Cache<Long, List<FieldCapabilities>> aggregateFieldsCache = CacheBuilder.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();

    private List<FieldCapabilities> loadAggregateFields(LogQuery logQuery) {
        try {
            MilogLogStoreDO logStore = logStoreDao.queryById(logQuery.getStoreId());
            if (logStore == null) {
                log.error("Log store not found for storeId: {}", logQuery.getStoreId());
                return Collections.emptyList();
            }

            EsService esService = esCluster.getEsService(logStore.getEsClusterId());
            RestHighLevelClient client = esService.getEsClient().getEsOriginalClient();

            FieldCapabilitiesResponse fieldCapabilities = getFieldCapabilities(client, logStore);

            SearchResponse searchResponse = performSearch(client, logQuery, logStore);

            return this.processResults(fieldCapabilities, searchResponse);
        } catch (Exception e) {
            log.error("Error in loading aggregate fields,logQuery:{}", gson.toJson(logQuery), e);
            return Collections.emptyList();
        }
    }

    private List<FieldCapabilities> processResults(FieldCapabilitiesResponse fieldCapsResponse, SearchResponse searchResponse) {
        Set<FieldCapabilities> filterFields = new HashSet<>();
        if (null != searchResponse) {
            Set<FieldCapabilities> aggregateFields = new HashSet<>();

            fieldCapsResponse.get().values().forEach(capabilitiesMap ->
                    aggregateFields.addAll(capabilitiesMap.values()));

            processSearchHits(searchResponse, aggregateFields, filterFields);
        }
        return new ArrayList<>(filterFields);
    }

    private FieldCapabilitiesResponse getFieldCapabilities(RestHighLevelClient client, MilogLogStoreDO logStore) {
        try {
            FieldCapabilitiesRequest request = new FieldCapabilitiesRequest()
                    .indices(getCurrentMonthIndexPattern(logStore.getEsIndex()))
                    .fields("*");
            return client.fieldCaps(request, RequestOptions.DEFAULT);
        } catch (Exception e) {
            throw new RuntimeException("Failed to obtain field capabilities", e);
        }
    }

    /**
     * 获取当前年和当前月的索引模式
     */
    private String getCurrentMonthIndexPattern(String baseIndexName) {
        // 获取当前日期
        LocalDate now = LocalDate.now();
        // 格式化当前年和当前月
        String yearMonth = now.format(DateTimeFormatter.ofPattern("yyyy.MM", Locale.ENGLISH));
        // 构建索引模式
        return String.format("%s-%s*", baseIndexName, yearMonth);
    }


    private SearchResponse performSearch(RestHighLevelClient client, LogQuery logQuery, MilogLogStoreDO logStore) {
        try {
            SearchSourceBuilder builder = createSearchSourceBuilder(logQuery, logStore, false, true);
            SearchRequest searchRequest = new SearchRequest(logStore.getEsIndex()).source(builder);
            return client.search(searchRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            throw new RuntimeException("Failed to perform a search", e);
        }
    }

    private void processSearchHits(SearchResponse searchResponse, Set<FieldCapabilities> aggregateFields, Set<FieldCapabilities> filterFields) {
        for (SearchHit hit : searchResponse.getHits().getHits()) {
            Map<String, Object> sourceAsMap = hit.getSourceAsMap();
            for (FieldCapabilities aggregateField : aggregateFields) {
                if (sourceAsMap.containsKey(aggregateField.getName()) && sourceAsMap.get(aggregateField.getName()) != null) {
                    filterFields.add(aggregateField);
                }
            }
        }
    }


    @Override
    public List<Map<String, String>> getTop5Messages(LogQuery logQuery) {
        log.info("getTop5Messages params:{},", gson.toJson(logQuery));
        validateLogQuery(logQuery);
        List<Map<String, String>> topMessages = new ArrayList<>();
        MilogLogStoreDO logStore = getLogStore(logQuery.getStoreId());
        if (logStore == null) return topMessages;

        try {
            EsService esService = esCluster.getEsService(logStore.getEsClusterId());
            RestHighLevelClient client = esService.getEsClient().getEsOriginalClient();

            // Validate field type
            if (isStatFieldAggregatable(client, logStore, logQuery)) {
                topMessages = getTopMessagesUsingAggregation(client, logStore, logQuery);
            } else {
                topMessages = getTopMessagesUsingSearch(client, logStore, logQuery);
            }

        } catch (Exception e) {
            log.error("Error occurred while retrieving top messages: {}", e.getMessage(), e);
        }
        return topMessages;
    }

    @Override
    public void removeCache(Long storeId) {
        aggregateFieldsCache.invalidate(storeId);
    }

    private void validateLogQuery(LogQuery logQuery) {
        if (StringUtils.isBlank(logQuery.getStatField())) {
            throw new MilogManageException("stat field not empty");
        }
    }

    private MilogLogStoreDO getLogStore(Long storeId) {
        MilogLogStoreDO logStore = logStoreDao.queryById(storeId);
        if (logStore == null) {
            log.error("Log store not found for ID: {}", storeId);
        }
        return logStore;
    }

    private boolean isStatFieldAggregatable(RestHighLevelClient client, MilogLogStoreDO logStore, LogQuery logQuery) throws Exception {
        FieldCapabilitiesRequest request = new FieldCapabilitiesRequest()
                .indices(String.format("%s%s", logStore.getEsIndex(), "*"))
                .fields("*");

        FieldCapabilitiesResponse response = client.fieldCaps(request, RequestOptions.DEFAULT);
        Map<String, FieldCapabilities> fieldTypeMap = response.getField(logQuery.getStatField());

        return fieldTypeMap.size() == 1 && fieldTypeMap.values().iterator().next().isAggregatable();
    }

    private List<Map<String, String>> getTopMessagesUsingAggregation(RestHighLevelClient client, MilogLogStoreDO logStore, LogQuery logQuery) throws Exception {
        SearchRequest searchRequest = buildSearchRequest(logStore, logQuery);
        SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);

        long totalDocCount = searchResponse.getHits().getTotalHits().value;
        Terms terms = searchResponse.getAggregations().get("top_messages");

        return buildMessageInfoList(terms, totalDocCount);
    }

    private List<Map<String, String>> getTopMessagesUsingSearch(RestHighLevelClient client, MilogLogStoreDO logStore, LogQuery logQuery) throws Exception {
        SearchSourceBuilder builder = createSearchSourceBuilder(logQuery, logStore, true, false);
        SearchRequest searchRequest = new SearchRequest(logStore.getEsIndex()).source(builder);
        SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);

        List<String> messageList = extractMessagesFromSearchHits(searchResponse, logQuery.getStatField());
        return buildTop5MessagesFromData(messageList);
    }

    private SearchSourceBuilder createSearchSourceBuilder(LogQuery logQuery, MilogLogStoreDO logStore, boolean fetchSource, boolean useSmallSize) {
        BoolQueryBuilder boolQueryBuilder = searchLog.getQueryBuilder(logQuery, getKeyColonPrefix(logStore.getKeyList()));
        if (CollectionUtils.isNotEmpty(logQuery.getTailIds())) {
            for (Long tailId : logQuery.getTailIds()) {
                boolQueryBuilder.should(commonExtensionService.multipleChooseBuilder(DefaultCommonExtensionService.QueryTypeEnum.ID, logQuery.getStoreId(), tailId.toString()));
            }
        }
        SearchSourceBuilder builder = new SearchSourceBuilder()
                .query(boolQueryBuilder)
                .sort(commonExtensionService.getSortedKey(logQuery, logQuery.getSortKey()), SortOrder.DESC)
                .size(1000)
                .timeout(TimeValue.timeValueMinutes(2L));
        if (useSmallSize) {
            builder.size(100);
        }
        if (StringUtils.isNotBlank(logQuery.getStatField())) {
            builder.fetchField(logQuery.getStatField());
        }
        if (fetchSource) {
            builder.fetchSource(logQuery.getStatField(), "");
        }
        return builder;
    }

    private List<String> extractMessagesFromSearchHits(SearchResponse searchResponse, String statField) {
        List<String> messageList = new ArrayList<>();
        SearchHit[] hits = searchResponse.getHits().getHits();
        for (SearchHit hit : hits) {
            Map<String, Object> sourceAsMap = hit.getSourceAsMap();
            if (sourceAsMap.containsKey(statField) && sourceAsMap.get(statField) != null) {
                messageList.add(sourceAsMap.get(statField).toString());
            }
        }
        return messageList;
    }

    private List<Map<String, String>> buildTop5MessagesFromData(List<String> messageList) {
        List<Map.Entry<String, Integer>> top5 = getTopKFrequentData(messageList, 5);
        return top5.stream()
                .map(entry -> {
                    Map<String, String> messageInfo = new HashMap<>();
                    messageInfo.put("message", entry.getKey());
                    messageInfo.put("count", entry.getValue().toString());

                    double percentage = (double) entry.getValue() / messageList.size() * 100;
                    messageInfo.put("percentage", String.format("%.2f%%", percentage));

                    return messageInfo;
                })
                .collect(Collectors.toList());
    }

    private List<Map.Entry<String, Integer>> getTopKFrequentData(List<String> dataList, int k) {
        Map<String, Integer> frequencyMap = new HashMap<>();
        for (String data : dataList) {
            frequencyMap.put(data, frequencyMap.getOrDefault(data, 0) + 1);
        }

        PriorityQueue<Map.Entry<String, Integer>> minHeap = new PriorityQueue<>(Comparator.comparingInt(Map.Entry::getValue));
        for (Map.Entry<String, Integer> entry : frequencyMap.entrySet()) {
            minHeap.offer(entry);
            if (minHeap.size() > k) {
                minHeap.poll();
            }
        }

        List<Map.Entry<String, Integer>> topK = new ArrayList<>(minHeap);
        topK.sort((a, b) -> b.getValue() - a.getValue());

        return topK;
    }

    private List<Map<String, String>> buildMessageInfoList(Terms terms, long totalDocCount) {
        return terms.getBuckets().stream()
                .map(bucket -> {
                    Map<String, String> messageInfo = new HashMap<>();
                    messageInfo.put("message", bucket.getKeyAsString());
                    messageInfo.put("count", String.valueOf(bucket.getDocCount()));

                    double percentage = (double) bucket.getDocCount() / totalDocCount * 100;
                    messageInfo.put("percentage", String.format("%.2f%%", percentage));

                    return messageInfo;
                })
                .collect(Collectors.toList());
    }

    private SearchRequest buildSearchRequest(MilogLogStoreDO logStore, LogQuery logQuery) {
        BoolQueryBuilder boolQueryBuilder = searchLog.getQueryBuilder(logQuery, getKeyColonPrefix(logStore.getKeyList()));
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(boolQueryBuilder)
                .sort(commonExtensionService.getSortedKey(logQuery, logQuery.getSortKey()))
                .aggregation(AggregationBuilders.terms("top_messages")
                        .field(logQuery.getStatField())
                        .size(5)
                        .order(BucketOrder.count(false)))
                .size(0)
                .trackTotalHits(true);

        return new SearchRequest(logStore.getEsIndex()).source(sourceBuilder);
    }

}
