package com.xiaomi.mone.log.manager.service.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.xiaomi.data.push.client.HttpClientV6;
import com.xiaomi.mione.tesla.k8s.bo.LogAgentListBo;
import com.xiaomi.mone.enums.InnerProjectTypeEnum;
import com.xiaomi.mone.log.manager.model.dto.AgentPodListRespDTO;
import com.xiaomi.mone.log.manager.model.dto.K8sMachineChangeDTO;
import com.xiaomi.mone.log.manager.model.dto.MetaAppInfoDTO;
import com.xiaomi.mone.log.manager.model.dto.MisResponseDTO;
import com.xiaomi.mone.log.manager.service.CloudPlatformK8sAppService;
import com.xiaomi.mone.log.manager.service.InnerLogAgentService;
import com.xiaomi.mone.log.manager.service.MqConsumerService;
import com.xiaomi.youpin.docean.anno.Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.common.Config;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.model.dto.PodDTO;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.xiaomi.mone.enums.InnerProjectTypeEnum.*;
import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.INNER_LOG_AGENT_SERVICE;

@Slf4j
@Service
public class CloudPlatformK8sAppServiceImpl implements CloudPlatformK8sAppService, MqConsumerService {

    private final Gson GSON = new Gson();
    private final static String URI_GET_APP_DETAIL = "/tracing/v1/app/one";
    private final static String URI_GET_AGENT_PODS = "/tracing/v1/app/log/agent/pods";
    private final static String URI_APP_LOG_INSTANCES = "/tracing/v1/app/log/instances";
    private final static String URI_APP_LOG_INSTANCE_GROUPS = "/tracing/v1/app/log/instance/groups";
    private final static String LOG_GROUP_MIKS_CLUSTER = "miks_cluster";
    private final static String PLATFORM_MIKS = "miks";

    @Resource(name = INNER_LOG_AGENT_SERVICE)
    private InnerLogAgentService innerLogAgentService;

    @Resource
    private InnerMqConsumerServiceImpl innerMqConsumerService;
    @Resource
    private MilogLogTailDao milogLogtailDao;
    @Resource
    private MatrixLogServiceImpl matrixLogServiceImpl;

    private static final Cache<String, MetaAppInfoDTO> CACHE_LOCAL_MetaAppInfoDTO = CacheBuilder.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build();

    /**
     * 用 hostIP 查 trace 接口（/tracing/v1/app/log/agent/pods）获取到 podList
     * *
     *
     * @param hostIP hostIP， 也就是 agentIP
     */
    @Override
    public List<LogAgentListBo> queryPodListByIp(String hostIP) {
        String mioapDomain = Config.ins().get("mioap_domain", "http://staging-mioap.api.xiaomi.net");
        String url = String.format("%s%s?node_ip=%s", mioapDomain, URI_GET_AGENT_PODS, hostIP);
        List<LogAgentListBo> podInfoList = null;
        try {
            String podListResp = HttpClientV6.get(url, null, 10000);
            AgentPodListRespDTO podListRespDTO = GSON.fromJson(podListResp, AgentPodListRespDTO.class);
            if (null == podListRespDTO.getData()) {
                log.error("query meta podlist return error, exit early. hostIP:{}, resp:{}", hostIP, podListResp);
                return null;
            }
            podInfoList = podListRespDTO.getData();
        } catch (Exception e) {
            log.error("query meta podlist exception, exit early. hostIP:{}, exception:{}", hostIP, e);
        }
        if (podInfoList == null) {
            return null;
        }

        return podInfoList;
    }

    @Override
    public MetaAppInfoDTO getMetaAppByAppId(Long appId, boolean withInstance) {
        if (appId <= 0) {
            return null;
        }
        return getMetaAppByAppInfo(appId, withInstance, "", "");
    }

    private MetaAppInfoDTO getMetaAppByAppInfo(Long appId, boolean withInstance, String sourceAppNode, String appPlatform) {
        if (!withInstance) {
            MetaAppInfoDTO meta = CACHE_LOCAL_MetaAppInfoDTO.getIfPresent(appId + sourceAppNode + appPlatform);
            if (meta != null && meta.getAppId() == appId) {
                return meta;
            }
        }
        String mioapDomain = Config.ins().get("mioap_domain", "http://staging-mioap.api.xiaomi.net");
        String url = String.format("%s%s?app_id=%d&with_instances=%s&source_app_node=%s&app_platform=%s",
                mioapDomain, URI_GET_APP_DETAIL, appId, withInstance, sourceAppNode, appPlatform);
        MetaAppInfoDTO appInfo = new MetaAppInfoDTO();
        String appInfoStr = "";
        try {
            appInfoStr = HttpClientV6.get(url, null, 10000);
            MisResponseDTO<MetaAppInfoDTO> rst = GSON.fromJson(appInfoStr, new TypeToken<MisResponseDTO<MetaAppInfoDTO>>() {
            }.getType());
            if (null == rst || null == rst.getData()) {
                log.error("query meta app info return error, exit early. appId:{}, resp:{}", appId, appInfoStr);
                return null;
            }
            appInfo = rst.getData();
            if (!withInstance) {
                CACHE_LOCAL_MetaAppInfoDTO.put(appId + sourceAppNode + appPlatform, appInfo);
            }
        } catch (Exception e) {
            log.error("query meta app info exception, exit early. mioapDomain:{},appInfoStr:{}, exception:", mioapDomain, appInfoStr, e);
        }

        return appInfo;
    }

    public List<MetaAppInfoDTO.ServerInstance> getAppInstancesByLogGroup(Long appId, String platformType, String logGroupKey, String logGroupValue) {
        String mioapDomain = Config.ins().get("mioap_domain", "http://staging-mioap.api.xiaomi.net");
        String url = String.format("%s%s?app_id=%d&app_platform=%s&log_group_key=%s&log_group_value=%s", mioapDomain, URI_APP_LOG_INSTANCES, appId, platformType, logGroupKey, logGroupValue);
        List<MetaAppInfoDTO.ServerInstance> list = null;
        try {
            String appInfoStr = HttpClientV6.get(url, null, 10000);
            MisResponseDTO<List<MetaAppInfoDTO.ServerInstance>> rst = GSON.fromJson(appInfoStr, new TypeToken<MisResponseDTO<List<MetaAppInfoDTO.ServerInstance>>>() {
            }.getType());
            if (null == rst || null == rst.getData()) {
                log.error("query app instance by log group return error, exit early. appId:{}, resp:{}", appId, appInfoStr);
                return null;
            }
            list = rst.getData();
        } catch (Exception e) {
            log.error("query app instance by log group exception, exit early. appId:{}, exception:", appId, e);
        }

        return list;
    }

    public List<MetaAppInfoDTO.LogInstanceGroup> getAppInstanceLogGroups(Long appId) {
        String mioapDomain = Config.ins().get("mioap_domain", "http://staging-mioap.api.xiaomi.net");
        String url = String.format("%s%s?app_id=%d&with_instances=%s", mioapDomain, URI_APP_LOG_INSTANCE_GROUPS, appId, true);
        List<MetaAppInfoDTO.LogInstanceGroup> list = null;
        try {
            String appInfoStr = HttpClientV6.get(url, null, 10000);
            MisResponseDTO<List<MetaAppInfoDTO.LogInstanceGroup>> rst = GSON.fromJson(appInfoStr, new TypeToken<MisResponseDTO<List<MetaAppInfoDTO.LogInstanceGroup>>>() {
            }.getType());
            if (null == rst || null == rst.getData()) {
                log.error("query app instance by log group return error, exit early. appId:{}, resp:{}", appId, appInfoStr);
                return null;
            }
            list = rst.getData();
        } catch (Exception e) {
            log.error("query app instance by log group exception, exit early. appId:{}, exception:", appId, e);
        }

        return list;
    }

    @Override
    public void handleK8sTopicTail(K8sMachineChangeDTO machineChangeDTO) {
        log.info("handleK8sTopicTail for cloud:{}", GSON.toJson(machineChangeDTO));
        MetaAppInfoDTO appContent = getMetaAppByAppId(machineChangeDTO.getAppId(), false);
        if (null != appContent) {
            List<MilogLogTailDo> logTailDos = milogLogtailDao
                    .queryByAppId((long) appContent.getAppId());
            for (MilogLogTailDo logTailDo : logTailDos) {
                if (Objects.equals(InnerProjectTypeEnum.MATRIX_TYPE.getCode(), logTailDo.getAppType())) {
                    if (logTailDo.getDeploySpace() == null || !logTailDo.getDeploySpace().equals(machineChangeDTO.getEnvName())) {
                        continue;
                    }
                } else if (Objects.equals(InnerProjectTypeEnum.MIKS_TYPE.getCode(), logTailDo.getAppType()) ||
                        Objects.equals(CLOUDML_TYPE.getCode(), logTailDo.getAppType())) {
                    if (logTailDo.getEnvName() == null || !logTailDo.getEnvName().equals(machineChangeDTO.getEnvName())) {
                        continue;
                    }
                } else {
                    continue;
                }
                List<PodDTO> changedMachines = machineChangeDTO.getChangedMachines();
                List<PodDTO> deletingMachines = machineChangeDTO.getDeletingMachines();
                List<String> changeIps = changedMachines.stream().map(PodDTO::getPodIP).collect(Collectors.toList());
                List<String> originIps = logTailDo.getIps();
                if (CollectionUtils.isEmpty(originIps) ||
                        !CollectionUtils.isEqualCollection(changeIps, originIps)) {
                    //发送配置
                    k8sPodIpsSendMq(logTailDo, changedMachines);
                    //更新tail
                    innerMqConsumerService.updateLogTail(logTailDo, changeIps);
                }
                if (CollectionUtils.isNotEmpty(deletingMachines)) {
                    innerMqConsumerService.k8sPodDeleteSendMq(logTailDo, deletingMachines);
                }
            }
        } else {
            log.info("handleK8sTopicTail for cloud failed,query AppBaseInfo not exist,params:{}", GSON.toJson(machineChangeDTO));
        }
    }

    private void k8sPodIpsSendMq(MilogLogTailDo milogLogtailDo, List<PodDTO> changedMachines) {
        if (!milogLogtailDo.getCollectionReady()) {
            log.info("k8sPodIpsSendMq skip for tail due to not enable collection, tailId:{}, tail:{}", milogLogtailDo.getId(), milogLogtailDo.getTail());
            return;
        }
        List<LogAgentListBo> pods = Lists.newArrayList();
        for (PodDTO dto : changedMachines) {
            LogAgentListBo logAgentListBo = new LogAgentListBo();
            logAgentListBo.setPodIP(dto.getPodIP());
            logAgentListBo.setPodName(dto.getPodName());
            logAgentListBo.setAgentIP(dto.getNodeIP());
            logAgentListBo.setAgentName(dto.getNodeName());
            pods.add(logAgentListBo);
        }
        if (MATRIX_TYPE.getCode().equals(milogLogtailDo.getAppType()) ||
                CLOUDML_TYPE.getCode().equals(milogLogtailDo.getAppType())) {
            String appNamespace = matrixLogServiceImpl.getTraceBaseInfoByAppId(milogLogtailDo.getAppId()).getAppNamespace();
            innerLogAgentService.publishIncrementConfigForCloudPlatformK8s(appNamespace, milogLogtailDo, pods);
            return;
        }
        if (MIKS_TYPE.getCode().equals(milogLogtailDo.getAppType())) {
            MetaAppInfoDTO appInfoDTO = getMetaAppByAppId(milogLogtailDo.getAppId(), false);
            innerLogAgentService.publishIncrementConfigForCloudPlatformK8s(appInfoDTO.getExtra().getLogNamespace(), milogLogtailDo, pods);
        }
    }


    @Override
    public MetaAppInfoDTO.MiKSAppData getMiKSAppByAppInfo(Long appId, String machineRoom) {
        List<MetaAppInfoDTO.LogInstanceGroup> appInstanceLogGroups = this.getAppInstanceLogGroups(appId);
        if (appInstanceLogGroups == null) {
            return null;
        }
        MetaAppInfoDTO.MiKSAppData miKSAppData = new MetaAppInfoDTO.MiKSAppData();
        if (StringUtils.isEmpty(machineRoom)) {
            miKSAppData.setClusters(appInstanceLogGroups.stream().map(MetaAppInfoDTO.LogInstanceGroup::toEnv).collect(Collectors.toList()));
        } else {
            miKSAppData.setClusters(appInstanceLogGroups.stream().filter(x -> machineRoom.equalsIgnoreCase(x.getLogArea())).map(MetaAppInfoDTO.LogInstanceGroup::toEnv).collect(Collectors.toList()));
        }
        return miKSAppData;
    }

    @Override
    public MetaAppInfoDTO.Env getMiKSAppEnvIPsByAppInfo(Long appId, String appName, String envName) {
        MetaAppInfoDTO.Env env = new MetaAppInfoDTO.Env();
        env.setName(envName);
        List<MetaAppInfoDTO.ServerInstance> instances = getAppInstancesByLogGroup(appId, PLATFORM_MIKS, LOG_GROUP_MIKS_CLUSTER, envName);
        List<String> ips = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(instances)) {
            instances.forEach(instance -> ips.add(instance.getServerIp()));
        }
        env.setIps(ips);
        return env;
    }

}

