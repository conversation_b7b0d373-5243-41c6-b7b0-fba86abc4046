package com.xiaomi.mone.log.manager.common.context;

import com.xiaomi.mone.log.manager.user.MoneUser;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2021/9/2 15:58
 */
public class MoneUserContext {

    private static ThreadLocal<MoneUser> currentUserHolder = new ThreadLocal<>();

    public static void setCurrentUser(MoneUser user) {
        currentUserHolder.set(user);
    }

    public static MoneUser getCurrentUser() {
        return currentUserHolder.get();
    }

    public static void clear() {
        currentUserHolder.remove();
    }

}