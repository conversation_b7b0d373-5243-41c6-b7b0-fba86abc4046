package com.xiaomi.mone.log.manager.common;

import com.xiaomi.mone.log.manager.model.TalosConfigModel;
import com.xiaomi.youpin.docean.anno.Bean;
import com.xiaomi.youpin.docean.anno.Component;
import com.xiaomi.youpin.docean.anno.Configuration;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.common.Config;
import org.apache.rocketmq.acl.common.AclClientRPCHook;
import org.apache.rocketmq.acl.common.SessionCredentials;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.remoting.RPCHook;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2021/9/8 17:25
 */
@Configuration
@Slf4j
@Component
public class ManagerConfig {

    private String talosAccessKey = Config.ins().get("talos_access_key", "");
    private String talosAccessSecret = Config.ins().get("talos_access_secret", "");
    private String talosAccessClusterInfo = Config.ins().get("talos_access_clusterInfo", "");
    private String talosAccessTopic = Config.ins().get("talos_access_topic", "");

    private static final String INTRANET_NAME = "intra";

    private static final String STAGING_NAME = "staging";

    private static final String LOCAL_NAME = "local";

    @Value("store_operate_event_group")
    private String producerGroup;

    @Value("rocketmq_namesrv_addr")
    private String nameSrvAddr;

    @Value("rocketmq_ak")
    private String ak;

    @Value("rocketmq_sk")
    private String sk;

    @Value("$server.type")
    private String env;

    @Bean
    public DefaultMQProducer initMqProducer() throws MQClientException {
        DefaultMQProducer producer;
        if (StringUtils.isNotEmpty(ak) && StringUtils.isNotEmpty(sk)) {
            RPCHook rpcHook = new AclClientRPCHook(new SessionCredentials(ak, sk));
            producer = new DefaultMQProducer(producerGroup + "x", rpcHook, true, null);
        } else {
            producer = new DefaultMQProducer(producerGroup + "x", true);
        }
        producer.setNamesrvAddr(nameSrvAddr);
        producer.start();
        return producer;
    }

    @Bean
    public TalosConfigModel talosConfigModel() {
        TalosConfigModel talosConfigModel = new TalosConfigModel();
        talosConfigModel.setTalosAccessKey(talosAccessKey);
        talosConfigModel.setTalosAccessSecret(talosAccessSecret);
        talosConfigModel.setTalosAccessClusterInfo(talosAccessClusterInfo);
        talosConfigModel.setTalosAccessTopic(talosAccessTopic);
        return talosConfigModel;
    }

    public static String getIntranetName() {
        return INTRANET_NAME;
    }

    public static String getStagingName() {
        return STAGING_NAME;
    }

    public static String getLocalName() {
        return LOCAL_NAME;
    }

    public boolean isStaging() {
        return STAGING_NAME.equals(env);
    }

    public boolean isLocal() {
        return LOCAL_NAME.equals(env);
    }

    public boolean isProd() {
        return INTRANET_NAME.equals(env);
    }

    public static final ThreadPoolExecutor EXECUTOR_COMMON = new ThreadPoolExecutor(
            8,
            20,
            60,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(1000),
            Executors.defaultThreadFactory(),
            new ThreadPoolExecutor.CallerRunsPolicy()
    );

}
