package com.xiaomi.mone.log.manager.service.remoting;

import com.xiaomi.youpin.docean.anno.Service;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

import javax.annotation.Resource;
import java.io.IOException;

@Service
@Slf4j
public class NeoHttp {

    @Resource
    private OkHttpClient okHttpClient;

    /**
     * 调用neo
     * @param treeId
     * @return
     */
    public String getNeoInfo(String treeId) {
        log.debug("call neo http, treeId:{}", treeId);
        // TODO 待配置成区分环境
        String url = "http://neo.b2cop.b2c.srv/api/v1/neo/tree/%s/apps";
        String headerName = "X-Auth-Token";
        String headerValue = "5aa6f5d2-bc34-4729-aa0e-e0686814de84";
        url = String.format(url, treeId);
        Request request = new Request.Builder().url(url).addHeader(headerName, headerValue).get().build();
        Response response = null;
        String result = null;
        try {
            response = okHttpClient.newCall(request).execute();
            if (response != null && response.body() != null){
                result = response.body().string();
                log.debug("call neo http, treeId:{}, result:{}", treeId, result);
            }
        } catch (IOException e) {
            log.error("call neo fail,", e);
        }
        return result;
    }


}
