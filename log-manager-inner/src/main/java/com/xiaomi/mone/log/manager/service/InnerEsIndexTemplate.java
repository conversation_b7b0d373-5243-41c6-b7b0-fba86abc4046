package com.xiaomi.mone.log.manager.service;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xiaomi.mone.enums.InnerMachineRegionEnum;
import com.xiaomi.mone.log.manager.model.dt.ColumnDTO2;
import com.xiaomi.mone.log.manager.model.dt.EsCreateDTO;
import com.xiaomi.bigdata.workshop.model.FieldType2;
import com.xiaomi.bigdata.workshop.model.WsTableDTO;
import com.xiaomi.mone.log.manager.common.utils.DtUtils;
import com.xiaomi.youpin.docean.anno.Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.manager.mapper.MilogEsIndexMapper;
import org.apache.ozhera.log.manager.model.dto.EsInfoDTO;
import org.apache.ozhera.log.manager.model.pojo.MilogEsClusterDO;
import org.apache.ozhera.log.manager.model.pojo.MilogEsIndexDO;
import org.apache.ozhera.log.manager.model.pojo.MilogLogStoreDO;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

import static com.xiaomi.mone.log.manager.service.MoneUserDetailService.GSON;
import static org.apache.ozhera.log.manager.service.impl.EsDataServiceImpl.requiredFields;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/4/28 10:23
 */
@Service
@Slf4j
public class InnerEsIndexTemplate {

    @Resource
    private InnerEsCluster esCluster;

    @Resource
    private MilogEsIndexMapper esIndexMapper;

    /**
     * 如果是平台侧创建资源，则根据 logstore 的机房，去对应 es 集群创建索引
     *
     * @param machineRoom
     * @return
     */
    public EsInfoDTO createPlatformEsInfo(MilogLogStoreDO ml, String machineRoom) {
        // 查询得到 es 集群信息
        MilogEsClusterDO cluster = esCluster.getPlatformEsCluster(machineRoom);
        if (null == cluster) {
            return null;
        }
        String indexName = DtUtils.buildEsIndex(ml.getId());
        EsInfoDTO esInfoDTO = createPlatformEsInfoBase(ml, cluster, indexName);
        if (null == esInfoDTO) {
            return new EsInfoDTO(cluster.getId(), indexName);
        }
        return esInfoDTO;
    }

    public EsInfoDTO createPlatformEsInfoBase(MilogLogStoreDO ml, MilogEsClusterDO cluster, String indexName) {
        String url = DtUtils.URL_TABLE_ES_CREATE;
        List<ColumnDTO2> columnDTOs = generateColumnDTOs(ml);
        EsCreateDTO esCreateDTO = new EsCreateDTO();
        int preserveTime = ml.getStorePeriod() > 7 ? 7 : ml.getStorePeriod();
        if (InnerMachineRegionEnum.SH_MACHINE.getEn().equals(ml.getMachineRoom())) {
            preserveTime = ml.getStorePeriod() >= 60 ? 60 : ml.getStorePeriod() >= 30 ? 30 : ml.getStorePeriod() >= 15 ? 15 : 7;
        }
        esCreateDTO.autoMapping(true)
                .name(indexName)
                .catalog(cluster.getDtCatalog())
                .dbName(cluster.getDtDatabase())
                .partitionUnit("day")
                .preserveTime(preserveTime)
                .description("es index for hera logstore: " + ml.getLogstoreName())
                .columnDtos(columnDTOs);
        String body = GSON.toJson(esCreateDTO);
        log.info("create es index in dt, request:{}", body);
        WsTableDTO wsTableDTO = DtUtils.post(url, body, cluster.getToken(), WsTableDTO.class);
        if (null == wsTableDTO) {
            log.error("创建 es 索引失败，request:{}, store:{},index:{}", body, ml, indexName);
            return null;
        }

        // 插入 milog_es_index 表记录
        MilogEsIndexDO milogEsIndexDO = new MilogEsIndexDO();
        milogEsIndexDO.setClusterId(cluster.getId());
        milogEsIndexDO.setLogType(ml.getLogType());
        milogEsIndexDO.setIndexName(indexName);
        esIndexMapper.insert(milogEsIndexDO);

        return new EsInfoDTO(cluster.getId(), indexName);
    }

    /**
     * 根据 store 的 keyList 和 columnTypeList 生成创建 es 索引时的初始字段列表
     *
     * @param ml
     * @return
     */
    private List<ColumnDTO2> generateColumnDTOs(MilogLogStoreDO ml) {
        List<ColumnDTO2> columnDTO2List = new ArrayList<>();

        String keyListStr = ml.getKeyList();
        String columnTypeListStr = ml.getColumnTypeList();
        String[] keyList = keyListStr.split(",");
        String[] columnTypeList = columnTypeListStr.split(",");
        for (int i = 0; i < keyList.length; i++) {
            String keyName = keyList[i].split(":")[0];
            ColumnDTO2 columnDTO2 = new ColumnDTO2();
            columnDTO2.fieldName(keyName).type(new FieldType2().type(columnTypeList[i])).isKey(false);
            columnDTO2List.add(columnDTO2);
        }
        try {
            extraFieldHandle(columnDTO2List);
        } catch (Exception e) {
            log.error("生成默认 es 索引字段列表失败，store:{},columnDTO2List:{},e:", ml, columnTypeList, e);
        }
        return columnDTO2List;
    }

    private void extraFieldHandle(List<ColumnDTO2> columnDTO2List) throws IOException {

        List<Pair<String, Pair<String, Integer>>> fields = requiredFields;
        fields.add(new Pair<>("spaceId", new Pair<>("integer", 3)));
        fields.add(new Pair<>("storeId", new Pair<>("integer", 3)));
        fields.add(new Pair<>("deploySpace", new Pair<>("keyword", 3)));

        for (Pair<String, Pair<String, Integer>> requiredField : fields) {
            if (columnDTO2List.stream()
                    .map(ColumnDTO2::getFieldName)
                    .noneMatch(fieldName -> Objects.equals(requiredField.getKey(), fieldName))) {
                ColumnDTO2 columnDTO2 = new ColumnDTO2();
                columnDTO2.fieldName(requiredField.getKey())
                        .type(new FieldType2().type(requiredField.getValue().getKey())).isKey(false);
                columnDTO2List.add(columnDTO2);
            }
        }

        if (!columnDTO2List.stream()
                .map(ColumnDTO2::getFieldName)
                .filter(fieldName -> Objects.equals("timestamp", fieldName))
                .findAny().isPresent()) {
            ColumnDTO2 columnDTO2 = new ColumnDTO2();
            columnDTO2.fieldName("timestamp").type(new FieldType2().type("date")).isKey(false);
            columnDTO2List.add(columnDTO2);
        }
        for (ColumnDTO2 column : columnDTO2List) {
            //text类型的字段都加上分词器
            if ("logsource".equals(column.getFieldName()) || "message".equals(column.getFieldName()) ||
                    "text".equals(column.getType().getType())) {
                column.setType(new FieldType2().type("text"));
                Map<String, Object> objectNode = new HashMap<>();
                objectNode.put("source_excludes", false);
                objectNode.put("index", true);
                objectNode.put("analyzer", "ik_max_word");
                column.setExtra(objectNode);
            }
            //数据工场时间类型字段勾选按时间筛选，创建的索引模式才能带时间筛选框
            if ("timestamp".equals(column.getFieldName())) {
                //String jsonString = "{\"source_excludes\":false,\"index\":true,\"format\":\"epoch_millis\"}";
                Map<String, Object> objectNode = new HashMap<>();
                objectNode.put("source_excludes", false);
                objectNode.put("index", true);
                objectNode.put("format", "epoch_millis");
                column.setExtra(objectNode);
                column.setIsKey(true);
            }
        }
    }

    /**
     * 删除使用平台资源的 logstore 对应的 es 索引信息
     *
     * @param logStoreDO
     * @return
     */
    public void deleteHeraPlatformEsInfo(MilogLogStoreDO logStoreDO) {
        // 查询得到 es 集群信息
        MilogEsClusterDO cluster = esCluster.getPlatformEsCluster(logStoreDO.getMachineRoom());
        if (null == cluster) {
            log.warn("matrix 应用类型 logstore 对应的集群信息为空，store_id: " + logStoreDO.getId() + ", machine_room: "
                    + logStoreDO.getMachineRoom());
            return;
        }
        String indexName = DtUtils.buildEsIndex(logStoreDO.getId());
        String url = DtUtils.URL_TABLE_DELETE;
        List<WsTableDTO> wsTableDTOS = new ArrayList<>();
        WsTableDTO wsTableDTO = new WsTableDTO().catalog(cluster.getDtCatalog()).dbName(cluster.getDtDatabase())
                .tableNameEn(indexName);
        wsTableDTOS.add(wsTableDTO);
        String body = GSON.toJson(wsTableDTOS);
        Boolean result = DtUtils.post(url, body, cluster.getToken(), Boolean.class);
        if (null == result || !result) {
            log.error("删除 matrix 应用类型 logstore 的 es 索引失败，索引信息：" + wsTableDTO.toString());
            return;
        }
        // 删除 es_index 表记录
        QueryWrapper<MilogEsIndexDO> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("cluster_id", cluster.getId());
        deleteWrapper.eq("log_type", logStoreDO.getLogType());
        deleteWrapper.eq("index_name", indexName);
        esIndexMapper.delete(deleteWrapper);
    }
}
