package com.xiaomi.mone.log.manager.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.xiaomi.data.push.common.SafeRun;
import com.xiaomi.mone.enums.InnerLogTypeEnum;
import com.xiaomi.mone.enums.InnerMiddlewareEnum;
import com.xiaomi.mone.enums.InnerProjectTypeEnum;
import com.xiaomi.mone.log.manager.common.InnerManagerConstant;
import com.xiaomi.mone.log.manager.common.context.MoneUserContext;
import com.xiaomi.mone.log.manager.dao.LogSpaceDao;
import com.xiaomi.mone.log.manager.service.impl.InnerRocketMqConfigService;
import com.xiaomi.mone.log.manager.service.impl.MilineRpcConsumerServiceImpl;
import com.xiaomi.mone.log.manager.user.UseDetailInfo;
import com.xiaomi.mone.miline.api.dto.milog.PipelineInstanceDto;
import com.xiaomi.mone.miline.api.dto.milog.ProjectInstanceDto;
import com.xiaomi.mone.miline.api.dto.milog.SimpleMachine;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.app.api.response.AppBaseInfo;
import org.apache.ozhera.log.api.model.bo.MiLogResource;
import org.apache.ozhera.log.api.model.vo.ResourceUserSimple;
import org.apache.ozhera.log.manager.common.ManagerConstant;
import org.apache.ozhera.log.manager.dao.MilogAppMiddlewareRelDao;
import org.apache.ozhera.log.manager.dao.MilogMiddlewareConfigDao;
import org.apache.ozhera.log.manager.model.pojo.*;
import org.apache.ozhera.log.manager.service.HeraAppService;
import org.apache.ozhera.log.manager.service.bind.LogTypeProcessor;
import org.apache.ozhera.log.manager.service.bind.LogTypeProcessorFactory;
import org.apache.ozhera.log.manager.service.extension.resource.ResourceExtensionService;
import org.apache.ozhera.log.manager.service.impl.LogTailServiceImpl;
import org.apache.ozhera.log.manager.service.nacos.MultipleNacosConfig;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.*;
import static org.apache.ozhera.log.common.Constant.*;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/4/11 10:03
 */
@Service(name = INNER_RESOURCE_SERVICE)
@Slf4j
public class InnerResourceExtensionService implements ResourceExtensionService {

    @Resource
    private IdmMoneUserDetailService moneUserDetailService;

    @Resource
    private TalosMqConfigService mqConfigService;

    @Resource
    private InnerRocketMqConfigService rocketMqConfigService;

    @Resource
    private MilogAppMiddlewareRelDao milogAppMiddlewareRelDao;

    @Resource
    private MilogMiddlewareConfigDao milogMiddlewareConfigDao;

    @Resource
    private LogTailServiceImpl logTailService;

    @Resource
    private HeraAppService heraAppService;

    @Resource
    private MilineRpcConsumerServiceImpl milineRpcConsumerService;

    @Resource
    private LogSpaceDao logSpaceDao;

    @Value("$log_stream_name")
    private String log_stream_name;

    @Value("$defaultNacosAddres")
    private String nacosAddress;

    private final String userShowListKey = "user_show_list_key";

    @Resource
    private LogTypeProcessorFactory logTypeProcessorFactory;


    private LogTypeProcessor logTypeProcessor;

    private volatile List<String> userList = new ArrayList<>();

    private static final Cache<String, String> CACHE_LOCAL_IP_HOSTNAME = CacheBuilder.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build();

    public void init() {
        logTypeProcessor = logTypeProcessorFactory.getLogTypeProcessor();
        freshUserList();
    }

    private void freshUserList() {
        ScheduledExecutorService scheduledExecutor = Executors
                .newSingleThreadScheduledExecutor(ThreadUtil.newNamedThreadFactory("user-list-fresh", false));
        scheduledExecutor.scheduleAtFixedRate(() ->
                SafeRun.run(this::userListChangeOperate), 0, 1, TimeUnit.MINUTES);

    }

    private void userListChangeOperate() {
        ConfigService configService = MultipleNacosConfig.getConfigService(nacosAddress);
        String filterConfig;
        try {
            filterConfig = configService.getConfig(userShowListKey, DEFAULT_GROUP_ID, DEFAULT_TIME_OUT_MS);
            if (StringUtils.isNotEmpty(filterConfig)) {
                userList = Arrays.stream(filterConfig.split(",")).collect(Collectors.toList());
            }
        } catch (NacosException e) {
            log.error("get config from nacos error", e);
        }
    }

    @Override
    public List<MilogMiddlewareConfig> userShowAuthority(List<MilogMiddlewareConfig> milogMiddlewareConfigs) {
        if (MoneUserContext.getCurrentUser().getIsAdmin()) {
            return milogMiddlewareConfigs;
        }
        UseDetailInfo.DeptDescriptor deptDescriptor = currentUserMaxDept();
        String userDeptComb = String.format("%s:%s->%s:%s", DEPT_LEVEL_PREFIX,
                deptDescriptor.getLevel(), DEPT_NAME_PREFIX, deptDescriptor.getDeptName());
        String userDeptCombEn = String.format("%s:%s->%s:%s", ManagerConstant.DEPT_LEVEL_PREFIX,
                deptDescriptor.getLevel(), ManagerConstant.DEPT_NAME_PREFIX, deptDescriptor.getDeptName());
        return milogMiddlewareConfigs.stream()
                .filter(milogMiddlewareConfig -> {
                    if (MoneUserContext.getCurrentUser().getUser().equals(milogMiddlewareConfig.getCreator()) ||
                            MoneUserContext.getCurrentUser().getUser().equals(milogMiddlewareConfig.getUpdater())) {
                        return Boolean.TRUE;
                    }
                    if (CollectionUtils.isNotEmpty(milogMiddlewareConfig.getLabels()) &&
                            (milogMiddlewareConfig.getLabels().contains(userDeptComb) || milogMiddlewareConfig.getLabels().contains(userDeptCombEn))) {
                        return Boolean.TRUE;
                    }
                    return Boolean.FALSE;
                }).collect(Collectors.toList());
    }

    @Override
    public void filterEsQueryWrapper(QueryWrapper<?> queryWrapper) {
        // 非管理员则不展示 matrix app 的 es 资源
        if (!MoneUserContext.getCurrentUser().getIsAdmin()) {
            // todo @liulei16 matrix -> platform
            queryWrapper.and(
                    wrapper -> wrapper.isNull("tag").or().ne("tag", "matrix")
            );
        }
    }

    @Override
    public List<String> generateResourceLabels(String id) {
        List<String> labels = Lists.newArrayList();
        try {
            List<UseDetailInfo.DeptDescriptor> fullDeptDescrList = queryUserDeptTreeInfo(id);
            labels = fullDeptDescrList.stream()
                    .sorted(Comparator.comparing(UseDetailInfo.DeptDescriptor::getLevel))
                    .map(deptDescriptor -> String.format(
                            "%s:%s->%s:%s", DEPT_LEVEL_PREFIX, deptDescriptor.getLevel(), DEPT_NAME_PREFIX, deptDescriptor.getDeptName()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("query user idm detail to generate labels error:current User miID:{},msg:{}",
                    id, e.getMessage(), e);
        }
        return labels;
    }

    @Override
    public void addResourcePreProcessing(List<String> resourceLabels, MiLogResource miLogResource) {
        resourceLabels.stream().filter(this::test).findFirst().ifPresent(s -> {
            miLogResource.setIsDefault(1);
        });
    }

    @Override
    public void addEsResourcePreProcessing(MilogEsClusterDO esClusterDO) {

    }

    @Override
    public void addResourceMiddleProcessing(MiLogResource miLogResource) {
//        mqConfigService.createCommonTagTopic(miLogResource.getAk(), miLogResource.getSk(), StringUtils.EMPTY,
//                miLogResource.getServiceUrl(), StringUtils.EMPTY, miLogResource.getOrgId(),
//                miLogResource.getTeamId());
    }

    @Override
    public void addResourcePostProcessing(MilogMiddlewareConfig milogMiddlewareConfig) {

    }

    @Override
    public boolean userResourceListPre(Integer logTypeCode) {
        return Objects.equals(InnerLogTypeEnum.LOKI_APP_LOG.getType(), logTypeCode) ||
                Objects.equals(InnerLogTypeEnum.MATRIX_ES_LOG.getType(), logTypeCode);
    }

    @Override
    public List<MilogMiddlewareConfig> currentUserConfigFilter(List<MilogMiddlewareConfig> middlewareConfigs) {
        //管理员可以看到所有的资源
        if (MoneUserContext.getCurrentUser().getIsAdmin()) {
            return middlewareConfigs;
        }
        List<MilogMiddlewareConfig> middlewareConfigListCn = getMilogMiddlewareConfigs(middlewareConfigs, DEPT_NAME_PREFIX, DEPT_LEVEL_PREFIX);
        List<MilogMiddlewareConfig> middlewareConfigListEn = getMilogMiddlewareConfigs(middlewareConfigs, ManagerConstant.DEPT_NAME_PREFIX, ManagerConstant.DEPT_LEVEL_PREFIX);
        if (CollectionUtils.isNotEmpty(middlewareConfigListCn) && CollectionUtils.isNotEmpty(middlewareConfigListEn)) {
            //两个list合并去重
            return CollUtil.unionDistinct(middlewareConfigListCn, middlewareConfigListEn).stream().toList();
        }
        return CollectionUtils.isEmpty(middlewareConfigListCn) ? middlewareConfigListEn : middlewareConfigListCn;
    }

    private List<MilogMiddlewareConfig> getMilogMiddlewareConfigs(List<MilogMiddlewareConfig> middlewareConfigs, String departName, String departLevel) {
        UseDetailInfo.DeptDescriptor maxDeptDescriptor = currentUserMaxDept();
        String userDeptComb = String.format("%s:%s->%s:%s", departLevel,
                maxDeptDescriptor.getLevel(), departName, maxDeptDescriptor.getDeptName());
        return middlewareConfigs.stream()
                .filter(milogMiddlewareConfig -> {
                    if (MoneUserContext.getCurrentUser().getUser().equals(milogMiddlewareConfig.getCreator()) ||
                            MoneUserContext.getCurrentUser().getUser().equals(milogMiddlewareConfig.getUpdater())) {
                        return Boolean.TRUE;
                    }
                    return milogMiddlewareConfig.getLabels()
                            .contains(userDeptComb);
                })
                .collect(Collectors.toList());
    }

    @Override
    public boolean resourceNotRequiredInit(Integer logTypeCode, List<MilogMiddlewareConfig> middlewareMqConfigs, List<MilogMiddlewareConfig> middlewareEsConfigs, List<MilogEsIndexDO> esIndexDOList) {
        return InnerLogTypeEnum.ORIGIN_LOG == InnerLogTypeEnum.type2enum(logTypeCode) ||
                InnerLogTypeEnum.OPENTELEMETRY == InnerLogTypeEnum.type2enum(logTypeCode) ||
                (CollectionUtils.isNotEmpty(middlewareMqConfigs) &&
                        CollectionUtils.isNotEmpty(middlewareEsConfigs) &&
                        CollectionUtils.isNotEmpty(esIndexDOList));
    }

    @Override
    public boolean resourceShowStatusFlag(ResourceUserSimple configResource) {
        //查看如果是指定的人，则展示，为了模拟其它部门的人能看到的场景
        if (userList.contains(MoneUserContext.getCurrentUser().getUser())) {
            configResource.setShowFlag(Boolean.TRUE);
            return Boolean.TRUE;
        }
        List<UseDetailInfo.DeptDescriptor> deptDescriptors = queryUserDeptTreeInfo(MoneUserContext.getCurrentUser().getUser());
        Optional<UseDetailInfo.DeptDescriptor> descriptorOptional = deptDescriptors.stream()
                .filter(deptDescriptor -> InnerManagerConstant.RESOURCE_DEFAULT_INITIALIZED_DEPT.contains(deptDescriptor.getDeptName())
                ).findAny();
        if (descriptorOptional.isPresent()) {
            configResource.setShowFlag(Boolean.FALSE);
            return Boolean.FALSE;
        }
        configResource.setShowFlag(Boolean.TRUE);
        return Boolean.TRUE;
    }

    @Override
    public Integer getResourceCode() {
        return InnerMiddlewareEnum.TALOS.getCode();
    }

    @Override
    public void deleteMqResourceProcessing(MilogLogTailDo mt, MilogLogStoreDO logStoreDO) {

        List<MilogAppMiddlewareRel> milogAppMiddlewareRels = milogAppMiddlewareRelDao.queryByCondition(mt.getMilogAppId(), null, mt.getId());
        if (null == logTypeProcessor) {
            init();
        }
        boolean supportedConsume = logTypeProcessor.supportedConsume(logStoreDO.getLogType());
        milogAppMiddlewareRels.forEach(middlewareRel -> {
            MilogMiddlewareConfig middlewareConfig = milogMiddlewareConfigDao.queryById(middlewareRel.getMiddlewareId());
            if (supportedConsume) {
                /*** 删除consumerGroup */
                if (InnerMiddlewareEnum.ROCKETMQ.getCode().equals(middlewareConfig.getType())) {
                    rocketMqConfigService.deleteSubscribeGroup(middlewareConfig.getServiceUrl(),
                            middlewareConfig.getAuthorization(), middlewareConfig.getOrgId(),
                            mt.getSpaceId(), mt.getStoreId(), mt.getId());
                }
                /**s 删除topic **/
                if (InnerMiddlewareEnum.TALOS.getCode().equals(middlewareConfig.getType())) {
                    /**等待logstream 停止，休眠1min**/
                    try {
                        TimeUnit.MINUTES.sleep(1);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    if (!middlewareRel.getConfig().getTopic().startsWith(COMMON_MQ_PREFIX)) {
                        mqConfigService.deleteTopic(middlewareConfig.getServiceUrl(),
                                middlewareConfig.getAk(), middlewareConfig.getSk(), middlewareRel.getConfig().getTopic());
                    } else {
                        //删除consumer group
                        mqConfigService.deleteConsumerGroup(mt.getId(), middlewareConfig.getServiceUrl(),
                                middlewareConfig.getAk(), middlewareConfig.getSk(), middlewareRel.getConfig().getTopic());
                    }
                }
            }
        });
    }

    @Override
    public List<Integer> getMqResourceCodeList() {
        return Lists.newArrayList(InnerMiddlewareEnum.TALOS.getCode(), InnerMiddlewareEnum.ROCKETMQ.getCode());
    }

    private boolean test(String label) {
        for (String defaultLabel : RESOURCE_DEFAULT_INITIALIZED_DEPT) {
            if (label.contains(defaultLabel)) {
                return true;
            }
        }
        return false;
    }

    private UseDetailInfo.DeptDescriptor currentUserMaxDept() {
        List<UseDetailInfo.DeptDescriptor> deptDescriptors = queryUserDeptTreeInfo(
                MoneUserContext.getCurrentUser().getUser());
        UseDetailInfo.DeptDescriptor maxDeptDescriptor = deptDescriptors.stream()
                .filter(deptDescriptor -> Objects.equals(
                        USER_DEPT_MAX_DEFAULT_LEVEL, deptDescriptor.getLevel())).findAny().get();
        return maxDeptDescriptor;
    }

    private List<UseDetailInfo.DeptDescriptor> queryUserDeptTreeInfo(String miID) {
        UseDetailInfo useDetailInfo = moneUserDetailService.queryUser(moneUserDetailService.queryUserUIdByUserName(miID));
        List<UseDetailInfo.DeptDescriptor> fullDeptDescrList = useDetailInfo.getFullDeptDescriptorList();
        return fullDeptDescrList;
    }


    @Override
    public String queryHostName(String ip) {
        try {
            return CACHE_LOCAL_IP_HOSTNAME.get(ip, () -> queryHostnameFromCacheOrService(ip));
        } catch (ExecutionException e) {
            log.error("queryStreamHostname error,ip:{}", ip, e);
            return StringUtils.EMPTY;
        }
    }

    @Override
    public List<Long> getSpaceIdsByNameExcluded(String spaceName) {
        List<Long> spaceIds;
        if (StringUtils.isNotBlank(spaceName)) {
            List<MilogSpaceDO> spaceDOS = logSpaceDao.queryByName(spaceName);
            spaceIds = spaceDOS.stream()
                    .filter(data -> !StringUtils.endsWith(data.getSpaceName(), MIFE_LOG_PREFIX))
                    .map(MilogSpaceDO::getId)
                    .toList();
        } else {
            spaceIds = logSpaceDao.queryNonMifeSpaceId();
        }
        return spaceIds;
    }

    private String queryHostnameFromCacheOrService(String ip) {
        try {
            List<AppBaseInfo> appBaseInfos = heraAppService.queryAppInfoWithLog(log_stream_name, InnerProjectTypeEnum.MIONE_TYPE.getCode());
            ProjectInstanceDto projectInstanceDto = milineRpcConsumerService.queryMachineInfoByProjectNew(Long.valueOf(appBaseInfos.get(0).getBindId()), null);

            return findHostnameInPipelineInstances(ip, projectInstanceDto.getPipelineInstanceDtoList());

        } catch (Exception e) {
            log.error("queryHostnameFromCacheOrService error,ip:{}", ip, e);
            return StringUtils.EMPTY;
        }
    }

    private String findHostnameInPipelineInstances(String ip, List<PipelineInstanceDto> pipelineInstanceDtoList) {
        for (PipelineInstanceDto pipelineInstanceDto : pipelineInstanceDtoList) {
            for (SimpleMachine machine : pipelineInstanceDto.getMachines()) {
                if (StringUtils.equals(ip, machine.getIp())) {
                    return machine.getHostname();
                }
                CACHE_LOCAL_IP_HOSTNAME.put(machine.getIp(), machine.getHostname());
            }
        }
        return StringUtils.EMPTY;
    }
}
