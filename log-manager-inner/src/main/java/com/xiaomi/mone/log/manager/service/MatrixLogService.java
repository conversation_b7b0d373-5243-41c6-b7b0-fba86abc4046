package com.xiaomi.mone.log.manager.service;


import org.apache.ozhera.log.common.Result;
import org.apache.ozhera.log.manager.model.dto.EsStatisticResult;
import org.apache.ozhera.log.manager.model.dto.LogDTO;
import org.apache.ozhera.log.manager.model.vo.LogQuery;

public interface MatrixLogService {
    /**
     * 查询 Matrix Es 数据 待下线接口
     * @param logQuery
     * @return
     * @throws Exception
     */
    @Deprecated
    Result<LogDTO> logQuery(LogQuery logQuery) throws Exception;

    /**
     * 查询 Matrix Es 的统计数据 待下线接口
     * @param param
     * @return
     * @throws Exception
     */
    @Deprecated
    Result<EsStatisticResult> MatrixEsStatistic(LogQuery param) throws Exception;
}
