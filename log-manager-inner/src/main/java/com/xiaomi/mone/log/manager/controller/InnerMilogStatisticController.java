package com.xiaomi.mone.log.manager.controller;

import com.xiaomi.mone.log.manager.service.RocketMqService;
import com.xiaomi.mone.log.manager.service.impl.MatrixLogServiceImpl;
import com.xiaomi.youpin.docean.anno.Controller;
import com.xiaomi.youpin.docean.anno.RequestMapping;
import com.xiaomi.youpin.docean.anno.RequestParam;
import org.apache.ozhera.log.common.Result;
import org.apache.ozhera.log.manager.model.dto.EsStatisticResult;
import org.apache.ozhera.log.manager.model.dto.RocketMqStatisticDTO;
import org.apache.ozhera.log.manager.model.vo.LogQuery;
import org.apache.ozhera.log.manager.model.vo.RocketMQStatisCommand;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

@Controller
public class InnerMilogStatisticController {
    private static final Logger LOGGER = LoggerFactory.getLogger(InnerMilogStatisticController.class);

    @Resource
    private RocketMqService rocketMqService;

    @Resource
    private MatrixLogServiceImpl matrixLogService;

    @RequestMapping(path = "/milog/statistic/rmq")
    public Result<RocketMqStatisticDTO> statisticRmq(@RequestParam("param") RocketMQStatisCommand command) {
        return rocketMqService.httpGetProducerTps(command);
    }

    @RequestMapping(path = "/matrix/statistic/es")
    public Result<EsStatisticResult> matrixStatisticEs(@RequestParam("param") LogQuery param) throws Exception {
        return matrixLogService.MatrixEsStatistic(param);
    }

}
