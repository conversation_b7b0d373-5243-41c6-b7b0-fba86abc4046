package com.xiaomi.mone.log.manager.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.xiaomi.mone.enums.*;
import com.xiaomi.mone.log.manager.common.context.MoneUserContext;
import com.xiaomi.mone.log.manager.service.InnerTailExtensionService;
import com.xiaomi.mone.log.manager.service.MilogAppTopicService;
import com.xiaomi.mone.log.manager.service.RocketMqService;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.app.api.response.AppBaseInfo;
import org.apache.ozhera.log.api.enums.OperateEnum;
import org.apache.ozhera.log.api.enums.ProjectSourceEnum;
import org.apache.ozhera.log.api.enums.ProjectTypeEnum;
import org.apache.ozhera.log.api.model.meta.MQConfig;
import org.apache.ozhera.log.common.Constant;
import org.apache.ozhera.log.common.Result;
import org.apache.ozhera.log.exception.CommonError;
import org.apache.ozhera.log.manager.common.Utils;
import org.apache.ozhera.log.manager.common.exception.MilogManageException;
import org.apache.ozhera.log.manager.common.helper.MilogAccessHelper;
import org.apache.ozhera.log.manager.dao.*;
import org.apache.ozhera.log.manager.domain.Tpc;
import org.apache.ozhera.log.manager.model.MilogSpaceParam;
import org.apache.ozhera.log.manager.model.bo.AccessMilogParam;
import org.apache.ozhera.log.manager.model.bo.AppTopicParam;
import org.apache.ozhera.log.manager.model.bo.LogTailParam;
import org.apache.ozhera.log.manager.model.dto.MapDTO;
import org.apache.ozhera.log.manager.model.dto.MilogAppConfigTailDTO;
import org.apache.ozhera.log.manager.model.dto.MilogAppOpenVo;
import org.apache.ozhera.log.manager.model.page.PageInfo;
import org.apache.ozhera.log.manager.model.pojo.*;
import org.apache.ozhera.log.manager.model.vo.AccessMiLogVo;
import org.apache.ozhera.log.manager.model.vo.LogPathTopicVo;
import org.apache.ozhera.log.manager.model.vo.LogStoreParam;
import org.apache.ozhera.log.manager.service.BaseService;
import org.apache.ozhera.log.manager.service.HeraAppService;
import org.apache.ozhera.log.manager.service.impl.LogSpaceServiceImpl;
import org.apache.ozhera.log.manager.service.impl.LogStoreServiceImpl;
import org.apache.ozhera.log.manager.service.impl.LogTailServiceImpl;
import org.apache.ozhera.log.manager.service.impl.MilogAppMiddlewareRelServiceImpl;
import org.apache.ozhera.log.parse.LogParserFactory;
import org.nutz.dao.Cnd;
import org.nutz.dao.Condition;
import org.nutz.dao.pager.Pager;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.INNER_TAIL_SERVICE;
import static org.apache.ozhera.log.common.Constant.*;
import static org.apache.ozhera.log.manager.common.Utils.getKeyValueList;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2021/7/27 11:20
 */
@Service
@Slf4j
public class MilogAppTopicServiceImpl extends BaseService implements MilogAppTopicService {

    @Resource
    private MilogAppTopicRelDao milogAppTopicRelDao;
    @Resource
    private RocketMqService rocketMqService;
    @Resource
    private MilogLogTailDao milogLogtailDao;
    @Resource
    private MilogLogstoreDao logstoreDao;
    @Resource
    private LogStoreServiceImpl logStoreService;
    @Resource
    private MilogSpaceDao milogSpaceDao;
    @Resource
    private LogSpaceServiceImpl milogSpaceService;
    @Resource
    private LogTailServiceImpl logTailService;
    @Resource
    private MilogLogstoreDao milogLogstoreDao;
    @Resource
    private MilogMiddlewareConfigDao milogMiddlewareConfigDao;
    @Resource
    private MilogAppMiddlewareRelDao milogAppMiddlewareRelDao;

    @Resource
    private MilogAccessHelper milogAccessHelper;

    @Resource
    private MilogAppMiddlewareRelServiceImpl milogAppMiddlewareRelService;

    @Resource(name = INNER_TAIL_SERVICE)
    private InnerTailExtensionService tailExtensionService;

    @Value("$app.env")
    private String env;

    @Resource
    private Gson gson;

    @Resource
    private Tpc tpc;

    //    @Reference(interfaceClass = HeraAppService.class, group = "$dubbo.env.group", check = false)
    @Resource
    private HeraAppService heraAppService;

    @Override
    public Result<PageInfo<MilogAppConfigTailDTO>> queryAppTopicList(AppTopicParam param) {
        log.info("查询前的时间：{}", SimpleDateFormat.getInstance().format(new Date()));
        Long currentTime = System.currentTimeMillis();
        Integer totalCount = milogAppTopicRelDao.queryAppTopicPageCount(handleParamToCondition(param));
        List<MilogAppTopicRelDO> topicRels = milogAppTopicRelDao.queryAppTopicList(handleParamToCondition(param), new Pager(param.getPage(), param.getPageSize()));
        List<MilogAppConfigTailDTO> milogAppConfigTailDTOS = topicRels.stream().map(milogAppTopicRel -> {
            MilogAppConfigTailDTO milogAppConfigTailDTO = new MilogAppConfigTailDTO();
            BeanUtil.copyProperties(milogAppTopicRel, milogAppConfigTailDTO);

            List<MilogAppConfigTailDTO.ConfigTailDTO> milogAppMiddlewareRels = milogAppMiddlewareRelDao.queryByAMilogAppId(milogAppTopicRel.getId());
            milogAppConfigTailDTO.setConfigTailDTOList(milogAppMiddlewareRels);
            return milogAppConfigTailDTO;
        }).collect(Collectors.toList());
        log.info("查询后的时间：{}", SimpleDateFormat.getInstance().format(new Date()));
        log.info("查询数据库花费的时间:{}", System.currentTimeMillis() - currentTime);
        PageInfo<MilogAppConfigTailDTO> pageInfo = new PageInfo<>(param.getPage(), param.getPageSize(), totalCount, milogAppConfigTailDTOS);
        return Result.success(pageInfo);
    }

    @Override
    public Result<String> createTopic(Long appId, String appName) {
        if (null == appId) {
            return Result.failParam("appId不能为空");
        }
        if (StringUtils.isEmpty(appName)) {
            return Result.failParam("appName不能为空");
        }
//        String topicNameSimple = createTopicNameSimple(appId, appName);
        String topicNameSimple = Utils.assembleTopicName(appId, appName);
        String createTopicResult = rocketMqService.httpCreateTopic(topicNameSimple);
        if (Constant.SUCCESS_MESSAGE.equalsIgnoreCase(createTopicResult)) {
            // topic 修改权限
            rocketMqService.updateTopiSubGroupAuth(topicNameSimple);
            //修改修改库中的
            milogAppTopicRelDao.updateTopicName(appId, topicNameSimple);
            updateTopicSendMsg(appId);
            return Result.success(topicNameSimple);
        } else {
            return Result.fail(CommonError.SERVER_ERROR.getCode(), "创建topic失败:" + createTopicResult);
        }
    }

    @Override
    public Result<String> updateExistsTopic(Long id, String existTopic) {
        if (null == id) {
            return Result.failParam("id不能为空");
        }
        if (StringUtils.isEmpty(existTopic)) {
            return Result.failParam("existTopic不能为空");
        }
        boolean isExist = topicIsExist(existTopic);
        if (!isExist) {
            return Result.fail(CommonError.SERVER_ERROR.getCode(), "existTopic不存在");
        }
        milogAppTopicRelDao.updateAppTopicRelMqConfigById(id, existTopic);
        MilogAppTopicRelDO milogAppTopicRel = milogAppTopicRelDao.queryById(id);
        if (null != milogAppTopicRel) {
            // topic权限绑定
            rocketMqService.updateTopiSubGroupAuth(existTopic);
            updateTopicSendMsg(milogAppTopicRel.getAppId());
        }
        return Result.success("成功");
    }

    private void updateTopicSendMsg(Long appId) {
        List<MilogLogTailDo> milogLogtailByAppId = milogLogtailDao.queryByAppId(appId);
        milogLogtailByAppId.forEach(milogLogtailDo -> {
//            logTailService.updateSendMsg(milogLogtailDo, Lists.newArrayList());
        });
    }

    @Override
    public Result<List<MapDTO>> queryAllExistTopicList() {
        Set<String> existTopics = rocketMqService.queryExistTopic();
        List<MapDTO> collect = existTopics.stream().map(s -> MapDTO.Of(s, s)).collect(Collectors.toList());
        return Result.success(collect);
    }

    @Override
    public Result<String> delTopicRecord(Long appId) {
        milogAppTopicRelDao.deleteAppTopicRelDb(appId, "", null, MoneUserContext.getCurrentUser().getZone());
        return Result.success();
    }

    @Override
    public Result<String> delTopicRecordAll() {
        milogAppTopicRelDao.delTopicRecordAll();
        return Result.success();
    }

    @Override
    public Result<List<MilogAppOpenVo>> queryAllMilogAppList() {
        List<AppBaseInfo> appBaseInfos = queryAllAccessMilogAppList();
        if (CollectionUtils.isEmpty(appBaseInfos)) {
            return Result.success();
        }
        List<MilogAppOpenVo> collect = appBaseInfos.stream().map(s -> MilogAppOpenVo.builder()
                .label(s.getAppName() + String.format("(%s)", s.getPlatformName())).value(s.getId().longValue())
                .source(s.getPlatformName().toString())
                .appId(s.getBindId())
                .platform(Long.valueOf(s.getPlatformType()))
                .build()
        ).collect(Collectors.toList());
        return Result.success(collect);
    }

    private List<AppBaseInfo> queryAllAccessMilogAppList() {
        List<AppBaseInfo> appBaseInfos = heraAppService.queryAllExistsApp();
        List<Integer> accessAppIds = milogLogtailDao.queryAllAppId();
        if (CollectionUtils.isNotEmpty(appBaseInfos) && CollectionUtils.isNotEmpty(accessAppIds)) {
            return appBaseInfos.parallelStream()
                    .filter(appBaseInfo -> accessAppIds.contains(appBaseInfo.getId()))
                    .collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    @Override
    public List<LogPathTopicVo> queryTopicConfigByAppId(Long milogAppId) {
        if (milogAppId == null) {
            return Collections.emptyList();
        }

        AppBaseInfo appBaseInfo = heraAppService.queryById(milogAppId);
        if (appBaseInfo == null) {
            return Collections.emptyList();
        }

        List<MilogLogTailDo> logTailDos = milogLogtailDao.getLogTailByMilogAppId(milogAppId);
        if (CollectionUtils.isEmpty(logTailDos)) {
            return Collections.emptyList();
        }

        return logTailDos.stream()
                .map(logTailDo -> transformToLogPathTopicVo(logTailDo, appBaseInfo))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private LogPathTopicVo transformToLogPathTopicVo(MilogLogTailDo logTailDo, AppBaseInfo appBaseInfo) {
        try {
            List<MilogAppMiddlewareRel> middlewareRels = milogAppMiddlewareRelDao
                    .queryByCondition(appBaseInfo.getId().longValue(), null, logTailDo.getId());

            if (CollectionUtils.isEmpty(middlewareRels)) {
                return null;
            }

            MilogAppMiddlewareRel milogAppMiddlewareRel = middlewareRels.get(middlewareRels.size() - 1);
            MQConfig mqConfigDest = generateConfig(milogAppMiddlewareRel, logTailDo);

            MilogLogStoreDO milogLogstoreDO = milogLogstoreDao.queryById(logTailDo.getStoreId());
            String valueList = (milogLogstoreDO != null)
                    ? getKeyValueList(milogLogstoreDO.getKeyList(), logTailDo.getValueList())
                    : "";

            return LogPathTopicVo.builder()
                    .logPath(logTailDo.getLogPath())
                    .source(appBaseInfo.getPlatformName())
                    .parseScript(logTailDo.getParseScript())
                    .valueList(valueList)
                    .regionEn(milogLogstoreDO != null ? milogLogstoreDO.getMachineRoom() : null)
                    .regionCn(milogLogstoreDO != null ? InnerMachineRegionEnum.queryCnByEn(milogLogstoreDO.getMachineRoom()) : null)
                    .serveAlias(logTailDo.getTail())
                    .tailId(logTailDo.getId())
                    .mqConfig(mqConfigDest)
                    .build();

        } catch (Exception e) {
            log.error("Error transforming logTailDo to LogPathTopicVo, logTailDo: {}", gson.toJson(logTailDo), e);
            return null;
        }
    }

    @Override
    public Result<AccessMiLogVo> accessToMilog(AccessMilogParam milogParam) {
        //1.param valid
        String errorMsg = milogAccessHelper.validParam(milogParam);
        if (StringUtils.isNotBlank(errorMsg)) {
            return Result.failParam(errorMsg);
        }
        log.info("mifaas access log param:{}", gson.toJson(milogParam));
        // 2.处理应用信息
        AppBaseInfo appBaseInfo = handleMilogAppTopicRel(milogParam);
        //3.handle space
        MilogSpaceDO spaceDO = handleMilogSpace(milogParam.getSpaceName(), milogParam.getAppCreator());
        //4.handle store
        MilogLogStoreDO logStoreDO = handleMilogStore(spaceDO.getId(), milogParam);
        //5.handle tail
        MilogLogTailDo logTailDo = handleMilogTail(logStoreDO.getSpaceId(), logStoreDO, milogParam, appBaseInfo);
        log.info("mifaas access log tail:{}", gson.toJson(logTailDo));
        return Result.success(generateAccessMiLogVo(logTailDo.getSpaceId(), logTailDo.getStoreId(), logTailDo.getId(), logTailDo.getTail()));
    }

    private AccessMiLogVo generateAccessMiLogVo(Long spaceId, Long storeId, Long tailId, String tailName) {
        return AccessMiLogVo.builder()
                .spaceId(spaceId)
                .storeId(storeId)
                .tailId(tailId)
                .tailName(tailName).build();
    }

    private MilogLogTailDo handleMilogTail(Long spaceId, MilogLogStoreDO logStoreDO, AccessMilogParam milogParam,
                                           AppBaseInfo appBaseInfo) {
        String tailName = milogParam.getEnvName();
        // 构建LogTailParam对象
        LogTailParam logTailParam = buildLogTailParam(spaceId, logStoreDO, milogParam, appBaseInfo, tailName);
        // 构建MilogLogTailDo对象
        MilogLogTailDo logTailDo = logTailService.buildLogTailDo(logTailParam, logStoreDO, appBaseInfo, milogParam.getAppCreator());
        // 查询是否存在，如果存在是否有变化，变化则修改
        MilogLogTailDo milogLogTailDo = milogLogtailDao.queryServerlessTailByFuncId(spaceId, logStoreDO.getId(),
                Long.valueOf(appBaseInfo.getId()), milogParam.getEnvId());
        if (null != milogLogTailDo) {
            logTailDo.setId(milogLogTailDo.getId());
            logTailDo.setStoreId(milogLogTailDo.getStoreId());
            tailExtensionService.defaultBindingAppTailConfigRel(logTailDo.getId(), logTailDo.getMilogAppId(), logStoreDO.getMqResourceId(), "", null);
            if (!milogAccessHelper.compareSame(milogLogTailDo, logTailDo)) {
                milogLogtailDao.update(logTailDo);
                logTailService.sengMessageToStream(logTailDo, OperateEnum.ADD_OPERATE.getCode());
            }
        } else {
            milogLogtailDao.add(logTailDo);
            tailExtensionService.defaultBindingAppTailConfigRel(logTailDo.getId(), logTailDo.getMilogAppId(), logStoreDO.getMqResourceId(), "", null);
            logTailService.sengMessageToStream(logTailDo, OperateEnum.ADD_OPERATE.getCode());
        }
        return logTailDo;
    }

    private LogTailParam buildLogTailParam(Long spaceId, MilogLogStoreDO logStoreDO, AccessMilogParam milogParam,
                                           AppBaseInfo appBaseInfo, String tailName) {
        return LogTailParam.builder()
                .spaceId(spaceId)
                .storeId(logStoreDO.getId())
                .appId(Long.valueOf(appBaseInfo.getBindId()))
                .milogAppId(Long.valueOf(appBaseInfo.getId()))
                .envId(milogParam.getEnvId())
                .envName(milogParam.getEnvName())
                .tail(tailName)
                .parseType(LogParserFactory.LogParserEnum.SEPARATOR_PARSE.getCode())
                .parseScript(DEFAULT_TAIL_SEPARATOR)
                .logPath(milogParam.getLogPath().trim())
                .valueList(DEFAULT_VALUE_LIST)
                .appType(InnerProjectTypeEnum.MIFAAS_TYPE.getCode())
                .deployWay(InnerDeployWayEnum.MILINE.getCode())
                .collectionReady(true)
                .build();
    }

    private MilogLogStoreDO handleMilogStore(Long spaceId, AccessMilogParam milogParam) {
        String storeName = milogParam.getStoreName();
        String machineRoom = milogParam.getMachineRoom();
        MilogLogStoreDO milogLogStoreDO = logstoreDao.queryStoreBySpaceStoreName(spaceId, storeName, machineRoom);
        if (null != milogLogStoreDO) {
            buildStoreMqResource(milogLogStoreDO);
            return milogLogStoreDO;
        }
        MilogLogStoreDO logStoreDO = logstoreDao.insert(
                logStoreService.buildLogStoreEsInfo(
                        buildLogStoreParam(spaceId, storeName, machineRoom), milogParam.getAppCreator()));
        buildStoreMqResource(logStoreDO);
        return logStoreDO;
    }

    private void buildStoreMqResource(MilogLogStoreDO logStoreDO) {
        if (null == logStoreDO.getMqResourceId()) {
            MilogMiddlewareConfig milogMiddlewareConfig = milogMiddlewareConfigDao.queryDefaultMqMiddlewareConfigMotorRoom(logStoreDO.getMachineRoom());
            logStoreDO.setMqResourceId(milogMiddlewareConfig.getId());
            logstoreDao.updateMilogLogStore(logStoreDO);
        }
    }


    private LogStoreParam buildLogStoreParam(Long spaceId, String storeName, String machineRoom) {
        return LogStoreParam.builder()
                .spaceId(spaceId)
                .logstoreName(storeName)
                .storePeriod(7)
                .shardCnt(1)
                .keyList(DEFAULT_KEY_LIST)
                .columnTypeList(DEFAULT_COLUMN_TYPE_LIST)
                .logType(InnerLogTypeEnum.APP_LOG_MULTI.getType())
                .machineRoom(machineRoom)
                .build();
    }

    private AppBaseInfo handleMilogAppTopicRel(AccessMilogParam milogParam) {
//        List<MilogAppTopicRelDO> appInfos = milogAppTopicRelDao.queryAppInfo(
//                milogParam.getAppId(), milogParam.getAppName(), null, generateAppSource(
//                        milogParam.getAppType(), milogParam.getAppTypeText()));
//        if (CollectionUtils.isEmpty(appInfos)) {
//            saveAppInfo(milogParam);
//        }
        AppBaseInfo appBaseInfo = heraAppService.queryByAppId(milogParam.getAppId(), ProjectTypeEnum.MIONE_TYPE.getCode());
        if (null != appBaseInfo) {
            return appBaseInfo;
        } else {
            throw new MilogManageException(String.format("app not exist,appId:%s", milogParam.getAppId()));
        }
    }

    private MilogAppTopicRelDO saveAppInfo(AccessMilogParam milogParam) {
        MilogAppTopicRelDO milogAppTopicRelDO = new MilogAppTopicRelDO();
        milogAppTopicRelDO.setAppId(milogParam.getAppId());
        milogAppTopicRelDO.setAppName(milogParam.getAppName());
        milogAppTopicRelDO.setSource(generateAppSource(milogParam.getAppType(), milogParam.getAppTypeText()));
        milogAppTopicRelDO.setType(generateAppType(milogParam.getAppType(), milogParam.getAppTypeText()));
        wrapBaseCommon(milogAppTopicRelDO, OperateEnum.ADD_OPERATE, milogParam.getAppCreator());
        milogAppTopicRelDO.setCreator(milogParam.getAppCreator());
        milogAppTopicRelDO.setCtime(milogParam.getAppCreatTime());
        milogAppTopicRelDao.insert(milogAppTopicRelDO);
        return milogAppTopicRelDO;
    }

    private Integer generateAppType(Integer appType, String appTypeText) {
        return InnerProjectTypeEnum.MIONE_TYPE.getCode();
    }

    private String generateAppSource(Integer appType, String appTypeText) {
        return ProjectSourceEnum.ONE_SOURCE.getSource();
    }

    private MilogSpaceDO handleMilogSpace(String spaceName, String creator) {
        List<MilogSpaceDO> milogSpaceDOS = milogSpaceDao.queryBySpaceName(spaceName);
        if (CollectionUtils.isNotEmpty(milogSpaceDOS)) {
            return milogSpaceDOS.get(milogSpaceDOS.size() - 1);
        } else {
            MilogSpaceDO spaceDO = milogSpaceService.buildMiLogSpace(
                    MilogSpaceParam.builder()
                            .spaceName(spaceName)
                            .description(String.format(DEFAULT_SPACE_DESC, spaceName))
                            .build(), creator);
            spaceDO.setCreator(DEFAULT_OPERATOR);
            spaceDO = milogSpaceDao.insert(spaceDO);
            tpc.saveSpacePerm(spaceDO, creator);
            return spaceDO;
        }
    }

    private MQConfig generateConfig(MilogAppMiddlewareRel milogAppMiddlewareRel, MilogLogTailDo milogLogtailDo) {
        MilogMiddlewareConfig milogMiddlewareConfig = milogMiddlewareConfigDao.queryById(milogAppMiddlewareRel.getMiddlewareId());
        MQConfig mqConfigDest = new MQConfig();
        mqConfigDest.setType(InnerMiddlewareEnum.queryNameByCode(milogMiddlewareConfig.getType()));
        String clusterInfo = "";
        if (InnerMiddlewareEnum.ROCKETMQ.getCode().equals(milogMiddlewareConfig.getType())) {
            clusterInfo = milogMiddlewareConfig.getNameServer();
        }
        if (InnerMiddlewareEnum.TALOS.getCode().equals(milogMiddlewareConfig.getType()) || InnerMiddlewareEnum.PLATFORM_DEFAULT_TALOS.getCode().equals(milogMiddlewareConfig.getType())) {
            clusterInfo = milogMiddlewareConfig.getServiceUrl();
        }
        mqConfigDest.setClusterInfo(clusterInfo);
        MilogAppMiddlewareRel.Config config = milogAppMiddlewareRel.getConfig();
        mqConfigDest.setAk(milogMiddlewareConfig.getAk());
        mqConfigDest.setSk(milogMiddlewareConfig.getSk());
        mqConfigDest.setTopic(config.getTopic());
        String tag = Utils.createTag(milogLogtailDo.getSpaceId(), milogLogtailDo.getStoreId(), milogLogtailDo.getId());
        mqConfigDest.setProducerGroup(DEFAULT_CONSUMER_GROUP + tag);
        mqConfigDest.setTag(tag);
        mqConfigDest.setPartitionCnt(config.getPartitionCnt());
        mqConfigDest.setEsConsumerGroup(config.getEsConsumerGroup());
        mqConfigDest.setBatchSendSize(config.getBatchSendSize());
        return mqConfigDest;
    }

    private static String timeToStr() {
        LocalDateTime ldt = LocalDateTime.now();
        DateTimeFormatter formatter =
                DateTimeFormatter.ofPattern("yyyyMMddhh");
        return ldt.format(formatter);
    }

    private boolean topicIsExist(String existTopic) {
        Set<String> existTopics = rocketMqService.queryExistTopic();
        if (existTopics.contains(existTopic)) {
            return true;
        }
        return false;
    }

    private Condition handleParamToCondition(AppTopicParam param) {
        Cnd cnd = Cnd.NEW();
        if (null != param.getAppId()) {
            cnd.and("app_id", EQUAL_OPERATE, param.getAppId());
        }
        if (StringUtils.isNotEmpty(MoneUserContext.getCurrentUser().getZone())) {
            cnd.and("source", EQUAL_OPERATE, MoneUserContext.getCurrentUser().getZone());
        }
        if (StringUtils.isNotEmpty(param.getAppName())) {
            cnd.and("app_name", "like", "%" + param.getAppName() + "%");
        }
        return cnd;
    }

}
