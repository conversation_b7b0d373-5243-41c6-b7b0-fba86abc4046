package com.xiaomi.mone.log.manager.service.cleanstrategy;

import com.xiaomi.mone.log.manager.common.utils.DtUtils;
import com.xiaomi.mone.log.manager.dao.InnerMilogLogStoreDao;
import com.xiaomi.mone.log.manager.model.po.InnerMilogLogStoreDO;
import com.xiaomi.mone.log.manager.model.vo.ClearDtResourceParam;
import com.xiaomi.mone.log.manager.service.InnerMilogAppMiddlewareRelService;
import com.xiaomi.mone.log.manager.service.InnerTailExtensionService;
import com.xiaomi.youpin.docean.anno.Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.manager.common.exception.MilogManageException;
import org.apache.ozhera.log.manager.dao.MilogAppMiddlewareRelDao;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.dao.MilogMiddlewareConfigDao;
import org.apache.ozhera.log.manager.model.bo.LogTailParam;
import org.apache.ozhera.log.manager.model.pojo.MilogAppMiddlewareRel;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;
import org.apache.ozhera.log.manager.model.pojo.MilogMiddlewareConfig;
import org.apache.ozhera.log.manager.service.bind.LogTypeProcessor;
import org.apache.ozhera.log.manager.service.bind.LogTypeProcessorFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.INNER_TAIL_SERVICE;

/**
 * 清理数据库topic为空的情况
 *
 * @author: songyutong1
 * @date: 2024/09/13/16:42
 */
@Service
@Slf4j
public class DbTopicNullStrategy extends AbstractCleanStrategy {

    @Resource
    private InnerMilogLogStoreDao innerMilogLogStoreDao;

    @Resource
    private MilogMiddlewareConfigDao milogMiddlewareConfigDao;

    @Resource
    private MilogLogTailDao milogLogtailDao;

    @Resource
    private MilogAppMiddlewareRelDao milogAppMiddlewareRelDao;

    @Resource
    private InnerMilogAppMiddlewareRelService milogAppMiddlewareRelService;

    @Resource(name = INNER_TAIL_SERVICE)
    private InnerTailExtensionService tailExtensionService;

    @Resource
    private LogTypeProcessorFactory logTypeProcessorFactory;
    private LogTypeProcessor logTypeProcessor;

    public void init() {
        logTypeProcessor = logTypeProcessorFactory.getLogTypeProcessor();
    }

    @Override
    public void clean(ClearDtResourceParam param, String uuid) {
        log.info("uuid:{}, start fix topic not exist", uuid);
        List<InnerMilogLogStoreDO> doList = innerMilogLogStoreDao.queryPlatformResourceLogStoreByMachineRoom(param.getMachineRoom(), true);
        List<String> topicAddInDb = new ArrayList<>();
        doList.forEach(logStoreDO -> {
            if (ObjectUtils.isEmpty(logStoreDO.getMqResourceId())) {
                log.info("uuid:{}, logStoreId:{} has no mqResourceId", uuid, logStoreDO.getId());
                return;
            }
            MilogMiddlewareConfig milogMiddlewareConfig = milogMiddlewareConfigDao.queryById(logStoreDO.getMqResourceId());
            if (ObjectUtils.isEmpty(milogMiddlewareConfig)) {
                log.info("uuid:{}, fix topic not exist failed, logStoreId:{} has no middlewareConfig", uuid, logStoreDO.getId());
                throw new MilogManageException("fix topic not exist failed, logStoreId:" + logStoreDO.getId() + " has no middlewareConfig");
            }
            List<MilogLogTailDo> tailList = milogLogtailDao.queryTailsByStoreId(logStoreDO.getId());
            tailList.forEach(logTailDo -> {
                List<MilogAppMiddlewareRel> middlewareRels = milogAppMiddlewareRelDao.queryByCondition(null, null, logTailDo.getId());
                // 数据库记录为空
                if (CollectionUtils.isEmpty(middlewareRels)) {
                    topicAddInDb.add(DtUtils.buildTalosTopic(logStoreDO.getId(), logTailDo.getId()));
                    if (Boolean.FALSE.equals(param.getClearFlag())) {
                        return;
                    }
                    handleMiddlewareRel(logStoreDO, logTailDo, param, milogMiddlewareConfig, uuid);
                    return;
                }
                // topic字段为空
                middlewareRels.forEach(middlewareRel -> {
                    if (StringUtils.isNotEmpty(middlewareRel.getConfig().getTopic())) {
                        return;
                    }
                    topicAddInDb.add(DtUtils.buildTalosTopic(logStoreDO.getId(), logTailDo.getId()));
                    if (Boolean.FALSE.equals(param.getClearFlag())) {
                        return;
                    }
                    milogAppMiddlewareRelDao.delete(middlewareRel.getId());
                    handleMiddlewareRel(logStoreDO, logTailDo, param, milogMiddlewareConfig, uuid);
                });
            });
        });
        log.info("uuid:{}, fix topic not exist finished, topicAddInDb:{}", uuid, topicAddInDb);
    }

    private void handleMiddlewareRel(InnerMilogLogStoreDO logStoreDO, MilogLogTailDo logTailDo, ClearDtResourceParam param, MilogMiddlewareConfig milogMiddlewareConfig, String uuid) {
        milogAppMiddlewareRelService.createMilogAppMiddlewareRel(logStoreDO.getSpaceId(), logStoreDO.getId(), logTailDo.getId(), logTailDo.getMilogAppId(), milogMiddlewareConfig);

        // 创建topic后重新下发配置
        LogTailParam logTailparam = LogTailParam.builder()
                .spaceId(logTailDo.getSpaceId())
                .storeId(logTailDo.getStoreId())
                .middlewareConfigId(milogMiddlewareConfig.getId())
                .collectionReady(logTailDo.getCollectionReady())
                .build();

        boolean supportedConsume = logTypeProcessor.supportedConsume(logStoreDO.getLogType());
        tailExtensionService.sendMessageOnCreate(logTailparam, logTailDo, logTailDo.getMilogAppId(), supportedConsume);
        log.info("uuid:{}, send config to agent finished, tail id:{}", uuid, logTailDo.getId());
    }
}
