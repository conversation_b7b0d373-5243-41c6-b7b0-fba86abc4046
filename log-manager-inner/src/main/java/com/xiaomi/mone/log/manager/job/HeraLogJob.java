package com.xiaomi.mone.log.manager.job;

import cn.hutool.core.thread.ThreadUtil;
import com.xiaomi.mone.log.manager.service.InnerAppService;
import com.xiaomi.youpin.docean.anno.Component;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2021/12/9 14:09
 */
@Component
@Slf4j
public class HeraLogJob {

    @Resource
    private InnerAppService appService;

    @Value("$job_start_flag")
    public String jobStartFlag;

    /**
     * 删除mione已经被删除的应用
     */
    public void init() {

        if (!Boolean.parseBoolean(jobStartFlag)) {
            log.info("job disabled, skip HeraLogJob");
            return;
        }

        log.info("删除被删除的应用 execute！ time:{}", LocalDateTime.now());
        ScheduledExecutorService scheduledExecutor = Executors.newSingleThreadScheduledExecutor(
                ThreadUtil.newNamedThreadFactory("log-logJob", false)
        );
        long initDelay = 1;
        long intervalTime = 12;
        scheduledExecutor.scheduleAtFixedRate(() -> {
            appService.delMoneApp();
        }, initDelay, intervalTime, TimeUnit.HOURS);
    }

}
