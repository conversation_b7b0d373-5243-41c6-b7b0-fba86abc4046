package com.xiaomi.mone.log.manager.service.impl;

import com.alibaba.nacos.api.config.ConfigService;
import com.xiaomi.mone.log.manager.service.BaseMilogRpcConsumerService;
import com.xiaomi.mone.miline.api.dto.PipelineDeployDto;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.dubbo.anno.Reference;
import com.xiaomi.youpin.gwdash.bo.*;
import com.xiaomi.youpin.gwdash.bo.openApi.ProjectDeployInfoQuery;
import com.xiaomi.youpin.gwdash.service.MilogProviderService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2021/9/13 16:25
 */
@Slf4j
@Service
public class ChinaMilogRpcConsumerServiceImpl extends BaseMilogRpcConsumerService {

    @Reference(interfaceClass = MilogProviderService.class, group = "$dubbo.china.group", check = false, timeout = 5000)
    private MilogProviderService milogProviderService;

    @Resource
    private ConfigService configService;


    @Override
    public MiLogMachineBo queryMachineInfoByProject(String projectName, String name) {
        return milogProviderService.queryMachineInfoByProject(projectName, name);
    }

    @Override
    public Page<ProjectDeployInfoDTO> queryProjectDeployInfoList(ProjectDeployInfoQuery query) {
        return milogProviderService.queryProjectDeployInfoList(query);
    }

    @Override
    public List<String> queryAppsByIp(String ip) {
        return milogProviderService.queryAppsByIp(ip);
    }

    @Override
    public List<MachineBo> queryIpsByAppId(Long projectId, Long projectEnvId, String envName) {
        return milogProviderService.queryIpsByAppId(projectId, projectEnvId, envName);
    }

    @Override
    public List<SimplePipleEnvBo> querySimplePipleEnvBoByProjectId(Long projectId) {
        return milogProviderService.querySimplePipleEnvBoByProjectId(projectId);
    }

    @Override
    public List<SimplePipleEnvBo> querySimplePipleEnvBoByProjectId(Long projectId, String env) {
        return null;
    }

    @Override
    public List<ProjectMemberDTO> queryProjectIdsByUserName(String userName) {
        return milogProviderService.queryProjectIdsByUserName(userName);
    }

    @Override
    public List<Long> queryDeleteProjectId() {
        return milogProviderService.queryDeleteProjectId();
    }

    @Override
    protected List<String> queryLiveMachines() {
        return milogProviderService.listLiveMachines();
    }

    @Override
    public PipelineDeployDto qryDeployInfo(long projectId, long pipelineId) {
        return null;
    }

}
