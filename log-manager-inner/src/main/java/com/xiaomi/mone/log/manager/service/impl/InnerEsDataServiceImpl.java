package com.xiaomi.mone.log.manager.service.impl;

import com.xiaomi.mone.cache.redis.RedisClientFactory;
import com.xiaomi.mone.log.manager.common.utils.EsUtils;
import com.xiaomi.mone.log.manager.service.InnerEsCluster;
import com.xiaomi.mone.log.manager.service.InnerEsDataService;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import com.xiaomi.youpin.docean.plugin.es.EsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.manager.common.exception.MilogManageException;
import org.apache.ozhera.log.manager.dao.MilogLogstoreDao;
import org.apache.ozhera.log.manager.model.pojo.MilogEsClusterDO;
import org.apache.ozhera.log.manager.model.pojo.MilogLogStoreDO;
import org.elasticsearch.action.admin.indices.settings.get.GetSettingsRequest;
import org.elasticsearch.action.admin.indices.settings.get.GetSettingsResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.collect.ImmutableOpenMap;
import org.elasticsearch.common.settings.Settings;
import redis.clients.jedis.JedisCluster;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Arrays;
import java.util.Comparator;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.xiaomi.mone.constant.RedisConstants.HERA_LOG_ES_LIFECYCLE_KEY;
import static com.xiaomi.mone.enums.InnerLogTypeEnum.LOKI_APP_LOG;

/**
 * @author: songyutong1
 * @date: 2024/10/29/15:04
 */
@Service
@Slf4j
public class InnerEsDataServiceImpl implements InnerEsDataService {

    @Resource
    private MilogLogstoreDao logStoreDao;

    @Resource
    private InnerEsCluster esCluster;

    private static final Long DEFAULT_LIFECYCLE = 5L;

    private static final Long LOKI_TYPE_LIFECYCLE = -2L;

    private static final String DEFAULT_LIFECYCLE_STR = "1Warm_5Del";

    private final JedisCluster jedisCluster = RedisClientFactory.getJedisCluster();

    @Value(value = "$lifecycle.redis.expire.duration")
    private String lifeCycleExpiration;

    @Override
    public Long queryIndexLifeCycle(Long storeId) throws IOException {
        MilogLogStoreDO logStoreDO = logStoreDao.queryById(storeId);

        if (LOKI_APP_LOG.getType().equals(logStoreDO.getLogType())) {
            return LOKI_TYPE_LIFECYCLE;
        }
        String esIndex = logStoreDO.getEsIndex();

        if(null == jedisCluster){
            throw new MilogManageException("redis cluster is null");
        }

        String key = HERA_LOG_ES_LIFECYCLE_KEY + esIndex;
        String lifeCycle = jedisCluster.get(key);
        if (StringUtils.isEmpty(lifeCycle)) {
            MilogEsClusterDO clusterDO = esCluster.getById(logStoreDO.getEsClusterId());
            if (null != clusterDO) {
                lifeCycle = getEsLifeCycleSetting(clusterDO, esIndex, logStoreDO.getId());
                jedisCluster.psetex(key, Long.parseLong(lifeCycleExpiration), lifeCycle);
            }
        }

        return EsUtils.getEsIndexStoragePeriod(lifeCycle, DEFAULT_LIFECYCLE);
    }

    private String getEsLifeCycleSetting(MilogEsClusterDO clusterDO, String esIndex, Long storeId) throws IOException {
        EsService esService = esCluster.getEsService(clusterDO.getId());
        if (esService == null || StringUtils.isEmpty(esIndex)) {
            log.warn("[EsDataService.logQuery] logStore:[{}] configuration exceptions", storeId);
            throw new MilogManageException("logStore configuration exceptions");
        }
        GetSettingsRequest request = new GetSettingsRequest();
        request.indices(esIndex);
        RestHighLevelClient client = esService.getEsClient().getEsOriginalClient();

        // 获取索引settings
        GetSettingsResponse settingsResponse = client.indices().getSettings(request, RequestOptions.DEFAULT);
        ImmutableOpenMap<String, Settings> indexToSettings = settingsResponse.getIndexToSettings();

        if (ObjectUtils.isEmpty(indexToSettings) || indexToSettings.isEmpty()) {
            return DEFAULT_LIFECYCLE_STR;
        }

        // 组装索引配置
        String[] indices = indexToSettings.keys().toArray(String.class);
        String indexNameKey = Arrays.stream(indices)
                .max(Comparator.comparing(InnerEsDataServiceImpl::extractTimestamp))
                .orElseThrow(() -> new MilogManageException("lifecycle dont exist"));

        if (StringUtils.isEmpty(indexNameKey)) {
            return DEFAULT_LIFECYCLE_STR;
        }
        Settings setting = indexToSettings.get(indexNameKey);
        String lifeCycle = setting.get("index.lifecycle.name");
        if (StringUtils.isEmpty(lifeCycle)) {
            lifeCycle = DEFAULT_LIFECYCLE_STR;
        }
        return lifeCycle;
    }

    private static String extractTimestamp(String index) {
        String regex = "\\b\\d{4}\\.\\d{2}\\.\\d{2}\\b";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(index);
        // 提取日期部分
        if (matcher.find()) {
            return matcher.group();
        }
        return "";
    }
}
