/*
 *  Copyright 2020 Xiaomi
 *
 *    Licensed under the Apache License, Version 2.0 (the "License");
 *    you may not use this file except in compliance with the License.
 *    You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *    Unless required by applicable law or agreed to in writing, software
 *    distributed under the License is distributed on an "AS IS" BASIS,
 *    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *    See the License for the specific language governing permissions and
 *    limitations under the License.
 */

package com.xiaomi.mone.log.manager;

import com.xiaomi.mone.cache.redis.RedisClientFactory;
import com.xiaomi.mone.es.EsClient;
import com.xiaomi.mone.log.manager.common.EsAddressReplaceService;
import com.xiaomi.mone.log.manager.common.OOMHandler;
import com.xiaomi.mone.log.manager.controller.interceptor.CasHttpRequestInterceptor;
import com.xiaomi.youpin.docean.Aop;
import com.xiaomi.youpin.docean.Ioc;
import com.xiaomi.youpin.docean.anno.RequestMapping;
import com.xiaomi.youpin.docean.aop.EnhanceInterceptor;
import com.xiaomi.youpin.docean.common.Cons;
import com.xiaomi.youpin.docean.config.HttpServerConfig;
import com.xiaomi.youpin.docean.mvc.DoceanHttpServer;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.common.Config;

import java.util.LinkedHashMap;

import static com.xiaomi.mone.log.manager.service.listener.StoreOperateListener.regStoreOperateListener;
import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;

/**
 * <AUTHOR>
 * @Date 2021/6/24 11:29
 */
@Slf4j
public class MiLogManagerBootstrap {

    public static void main(String[] args) throws InterruptedException {
        Thread.setDefaultUncaughtExceptionHandler(new OOMHandler());
        //查询的时候关闭
        EsClient.startedSniffer = false;
        getConfigFromNanos();
        init();
        LinkedHashMap<Class, EnhanceInterceptor> m = new LinkedHashMap<>();
        m.put(RequestMapping.class, new CasHttpRequestInterceptor());
        Aop.ins().init(m);
        Ioc.ins().putBean(Cons.AUTO_FIND_IMPL, "true").init("com.xiaomi.mone", "com.xiaomi.youpin", "org.apache.ozhera.log.manager");
        regStoreOperateListener();
        Config ins = Config.ins();
        int port = Integer.parseInt(ins.get("serverPort", ""));
        DoceanHttpServer server = new DoceanHttpServer(HttpServerConfig.builder().websocket(false).port(port).build());

        EsAddressReplaceService esAddressReplaceService = Ioc.ins().getBean(EsAddressReplaceService.class);
        if (null != esAddressReplaceService) {
            esAddressReplaceService.replaceEsAddress();
        }

        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("hera log manager shutdown");
            server.stop();
        }));

        server.start();
    }

    private static void init() {
        RedisClientFactory.init();
    }

}
