package com.xiaomi.mone.log.manager.service.listener;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ozhera.log.manager.model.BaseCommon;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/2/29 14:21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StorePeriodData extends BaseCommon {
    private Long spaceId;
    private Long storeId;
    /**
     * HERALOG_spaceId_storeId
     */
    private String uniqueId;
    private String storeName;
    private Integer storePeriod;
    private Integer operateCode;
    private String operateDesc;

    private String machineRoom;
    private String cluster;

}
