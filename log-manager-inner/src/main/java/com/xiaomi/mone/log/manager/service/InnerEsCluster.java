package com.xiaomi.mone.log.manager.service;

import com.xiaomi.youpin.docean.anno.Service;
import org.apache.ozhera.log.manager.domain.EsCluster;
import org.apache.ozhera.log.manager.mapper.MilogEsClusterMapper;
import org.apache.ozhera.log.manager.model.pojo.MilogEsClusterDO;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/4/28 10:26
 */
@Service
public class InnerEsCluster extends EsCluster {

    @Resource
    private MilogEsClusterMapper esClusterMapper;

    /**
     * 如果是平台侧创建资源，获取 logstore 对应的 es 集群信息
     * @param area
     * @return
     */
    public MilogEsClusterDO getPlatformEsCluster(String area) {
        // todo @liulei16 matrix -> platform
        return esClusterMapper.selectByTagAndArea("matrix", area);
    }
}
