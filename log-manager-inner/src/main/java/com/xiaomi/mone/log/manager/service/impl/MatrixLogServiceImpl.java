package com.xiaomi.mone.log.manager.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.xiaomi.data.push.client.HttpClientV6;
import com.xiaomi.mone.es.EsClient;
import com.xiaomi.mone.log.manager.common.utils.CommonUtil;
import com.xiaomi.mone.log.manager.common.utils.IamService;
import com.xiaomi.mone.log.manager.common.utils.MatrixEsService;
import com.xiaomi.mone.log.manager.dao.InnerMilogLogStoreDao;
import com.xiaomi.mone.log.manager.mapper.MilogMatrixEsInfoMapper;
import com.xiaomi.mone.log.manager.model.dto.*;
import com.xiaomi.mone.log.manager.model.po.MilogMatrixEsInfoDO;
import com.xiaomi.mone.log.manager.model.vo.MatrixTraceLogParam;
import com.xiaomi.mone.log.manager.service.MatrixLogService;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.es.EsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.ozhera.log.api.model.dto.TraceLogDTO;
import org.apache.ozhera.log.common.Config;
import org.apache.ozhera.log.common.Result;
import org.apache.ozhera.log.exception.CommonError;
import org.apache.ozhera.log.manager.common.utils.ExportUtils;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.dao.MilogLogstoreDao;
import org.apache.ozhera.log.manager.domain.ClusterIndexVO;
import org.apache.ozhera.log.manager.domain.SearchLog;
import org.apache.ozhera.log.manager.domain.TraceLog;
import org.apache.ozhera.log.manager.mapper.MilogEsIndexMapper;
import org.apache.ozhera.log.manager.model.dto.*;
import org.apache.ozhera.log.manager.model.pojo.MilogEsIndexDO;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;
import org.apache.ozhera.log.manager.model.vo.LogQuery;
import org.apache.ozhera.log.manager.service.EsDataBaseService;
import org.apache.ozhera.log.manager.service.impl.EsDataServiceImpl;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.ISODateTimeFormat;
import run.mone.excel.ExportExcel;

import javax.annotation.Resource;
import java.net.InetAddress;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.apache.ozhera.log.common.Constant.GSON;
import static org.elasticsearch.search.sort.SortOrder.ASC;
import static org.elasticsearch.search.sort.SortOrder.DESC;

@Slf4j
@Service
public class MatrixLogServiceImpl implements MatrixLogService, EsDataBaseService {
    private Gson gson = new Gson();

    @Resource
    private SearchLog searchLog;

    @Resource
    private MilogMatrixEsInfoMapper milogMatrixEsInfoMapper;

    @Resource
    private EsDataServiceImpl esDataService;

    @Resource
    private MilogLogTailDao milogLogtailDao;

    @Resource
    private MilogLogstoreDao milogLogstoreDao;

    @Resource
    private InnerMilogLogStoreDao innerMilogLogstoreDao;

    @Resource
    private MilogEsIndexMapper esIndexMapper;

    @Resource
    private TraceLog traceLog;

    private final Map<EsSearchParamDTO, EsService> serviceMap = new ConcurrentHashMap<>();

    @Resource
    private final Map<Long, TraceAppInfoDTO> appIdTraceAppBaseInfoCache = new ConcurrentHashMap<>();

    private static final Cache<String, Long> CACHE_LOCAL_DEPLOY_SPACES = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(60, TimeUnit.MINUTES)
            .build();
    private static final Cache<String, String> CACHE_LOCAL_HOST_IPS = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build();

    String esKeyMap_timestamp = "alpha_timestamp";
    String esKeyMap_dir_name = "alpha_dir_name";
    String esKeyMap_file_name = "alpha_file_name";
    String esKeyMap_host_name = "alpha_host_name";
    String esKeyMap_ip_address = "alpha_ip_address";
    String esKeyMap_pod_name = "alpha_pod_name";
    String esKeyMap_message = "message";
    String esKeyMap_trace_id = "trace_id";

    private final Set<String> noHighLightSet = new HashSet<>();
    private final Set<String> hiddenSet = new HashSet<>();

    {
        noHighLightSet.add(esKeyMap_timestamp);

        hiddenSet.add(esKeyMap_host_name);
        hiddenSet.add(esKeyMap_dir_name);
    }

    private final List<String> matrixDefaultKeyList = new ArrayList<>();

    {
        matrixDefaultKeyList.add(esKeyMap_message);
        matrixDefaultKeyList.add(esKeyMap_ip_address);
        matrixDefaultKeyList.add(esKeyMap_file_name);
        matrixDefaultKeyList.add(esKeyMap_pod_name);
        matrixDefaultKeyList.add(esKeyMap_trace_id);
    }

    String ActiveTrue = "True";
    String ActiveFalse = "False";

    /**
     * 查询 matrix es 数据
     */
    @Override
    public Result<LogDTO> logQuery(LogQuery logQuery) throws Exception {
        try {
            List<TraceAppInfoDTO> traceAppInfoDTOList = queryTraceAppInfos(logQuery.getAppIds());
            List<CompletableFuture<List<LogDTO>>> futureList = traceAppInfoDTOList.stream().map(traceAppInfoDTO -> CompletableFuture.supplyAsync(() -> {
                try {
                    return logQueryForOne(logQuery, traceAppInfoDTO);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return new ArrayList<LogDTO>();
            })).collect(Collectors.toList());
            List<LogDTO> logDTOList = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]))
                    .thenApply(s -> futureList.stream().map(CompletableFuture::join)
                            .flatMap(Collection::stream).collect(Collectors.toList())).get();
            LogDTO dto = generateResultDto(logDTOList, logQuery);
            return Result.success(dto);
        } catch (Exception e) {
            log.error("query matrix es log exception, logQuery:{}", logQuery, e);
            throw e;
        }
    }

    private List<LogDTO> logQueryForOne(LogQuery logQuery, TraceAppInfoDTO traceAppInfoDTO) throws Exception {
        MatrixAppsDTO matrixAppsDTO = queryMatrixApps(traceAppInfoDTO.getIamTreeId());
        Long deploySpaceId = filterDeploySpace(traceAppInfoDTO.getAppName(), traceAppInfoDTO.getMatrixDeploySpace(), matrixAppsDTO);
        MatrixLogCollectionsDTO matrixLogCollectionDTO = queryMatrixLogCollections(deploySpaceId, traceAppInfoDTO.getIamTreeId());
        List<EsSearchParamDTO> esSearchParamDTOList = queryMatrixEsIndexList(matrixLogCollectionDTO, traceAppInfoDTO.getMatrixDeploySpace());
        List<LogDTO> tmpLogDTOList = queryMatrixLog(esSearchParamDTOList, logQuery);
        return tmpLogDTOList;
    }

    /**
     * 将查询多个 es 索引得到的数据组装成一个返回
     */
    private LogDTO generateResultDto(List<LogDTO> logDTOList, LogQuery logQuery) {
        Boolean asc = logQuery.getAsc();
        LogDTO dto = new LogDTO();
        Long total = 0L;
        Long sortTimestamp = logDTOList.size() == 0 ? new Date().getTime() : (Long) logDTOList.get(0).getThisSortValue()[0];
        List<LogDataDTO> logDataDTOList = new ArrayList<>();
        for (LogDTO logDTO : logDTOList) {
            total += logDTO.getTotal();
            sortTimestamp = getSortTimestamp(logDTO, asc, sortTimestamp);
            logDataDTOList.addAll(logDTO.getLogDataDTOList());
        }

        dto.setTotal(total);
        Object[] sortValues = new Object[1];
        sortValues[0] = sortTimestamp;
        dto.setThisSortValue(sortValues);
        dto.setLogDataDTOList(logDataDTOList);
        return dto;
    }

    // 查询 matrix 的话可能会查多个索引，所以 sortValue 若升序取最小的，若降序去最大的时间，在有多个日志收集的时候可能会有重复日志被查出
    private Long getSortTimestamp(LogDTO logDTO, Boolean asc, Long sortTimestamp) {
        List<Object> tmpL = Arrays.asList(logDTO.getThisSortValue());
        if (tmpL.size() > 0) {
            Long sortValue = (Long) tmpL.get(0);
            if ((asc && sortValue <= sortTimestamp) || (!asc && sortValue > sortTimestamp)) {
                sortTimestamp = sortValue;
            }
        }
        return sortTimestamp;
    }

    /**
     * 查询 es matrix 统计数据
     *
     * @param logQuery
     * @return
     * @throws Exception
     */
    @Override
    public Result<EsStatisticResult> MatrixEsStatistic(LogQuery logQuery) throws Exception {
        List<EsStatisticResult> resultList = new ArrayList<>();
        List<TraceAppInfoDTO> traceAppInfoDTOList = queryTraceAppInfos(logQuery.getAppIds());
        for (TraceAppInfoDTO traceAppInfoDTO : traceAppInfoDTOList) {
            MatrixAppsDTO matrixAppsDTO = queryMatrixApps(traceAppInfoDTO.getIamTreeId());
            Long deploySpaceId = filterDeploySpace(traceAppInfoDTO.getAppName(), traceAppInfoDTO.getMatrixDeploySpace(), matrixAppsDTO);
            MatrixLogCollectionsDTO matrixLogCollectionDTO = queryMatrixLogCollections(deploySpaceId, traceAppInfoDTO.getIamTreeId());
            List<EsSearchParamDTO> esSearchParamDTOList = queryMatrixEsIndexList(matrixLogCollectionDTO, traceAppInfoDTO.getMatrixDeploySpace());
            List<EsStatisticResult> tmpEsStatisticResultList = statisticMatrixEs(esSearchParamDTOList, logQuery);
            resultList.addAll(tmpEsStatisticResultList);
        }
        EsStatisticResult ret = generateStatisticResult(resultList);
        ret.setName(esDataService.constructEsStatisticRet(logQuery));
        ret.calTotalCounts();
        return new Result<>(CommonError.Success.getCode(), CommonError.Success.getMessage(), ret);
    }

    /**
     * 将查询多个 es 得到的统计数据进行合并：counts 每个相加，timestamps 取任意一个即可
     *
     * @param esStatisticResultList
     * @return
     */
    private EsStatisticResult generateStatisticResult(List<EsStatisticResult> esStatisticResultList) {
        EsStatisticResult ret = new EsStatisticResult();
        List<Long> counts = new ArrayList<>();
        List<String> timestamps = new ArrayList<>();
        for (EsStatisticResult esStatisticResult : esStatisticResultList) {
            if (counts.size() > 0) {
                for (int i = 0; i < counts.size(); i++) {
                    counts.set(i, counts.get(i) + esStatisticResult.getCounts().get(i));
                }
            } else {
                counts = esStatisticResult.getCounts();
                timestamps = esStatisticResult.getTimestamps();
            }
        }
        ret.setCounts(counts);
        ret.setTimestamps(timestamps);
        ret.calTotalCounts();
        return ret;
    }

    /**
     * 根据 es 参数查询 es 索引统计数据
     *
     * @param esSearchParamDTOList
     * @param logQuery
     * @return
     * @throws Exception
     */
    private List<EsStatisticResult> statisticMatrixEs(List<EsSearchParamDTO> esSearchParamDTOList, LogQuery logQuery) throws Exception {
        List<EsStatisticResult> resultList = new ArrayList<>();
        for (EsSearchParamDTO esSearchParamDTO : esSearchParamDTOList) {
            List<String> keyList = matrixDefaultKeyList;
            // get interval
            String interval = searchLog.esHistogramInterval(logQuery.getEndTime() - logQuery.getStartTime());
            String esIndex = esSearchParamDTO.getIndex();
            EsService esService = getEsService(esSearchParamDTO);
            if (!com.xiaomi.youpin.docean.common.StringUtils.isEmpty(interval)) {
                BoolQueryBuilder builder = searchLog.getMatrixQueryBuilder(logQuery, keyList);
                EsClient.EsRet esRet = esService.dateHistogram(esIndex, interval, logQuery.getStartTime(), logQuery.getEndTime(), builder);
                EsStatisticResult tmpResult = new EsStatisticResult();
                tmpResult.setCounts(esRet.getCounts());
                tmpResult.setTimestamps(esRet.getTimestamps());
                tmpResult.calTotalCounts();

                resultList.add(tmpResult);
            }
        }
        return resultList;
    }

    /**
     * 不能来一个请求就创建一个esClient,不然会造成线程数量太多,cpu飙高
     *
     * @param esSearchParamDTO
     * @return
     */
    private EsService getEsService(EsSearchParamDTO esSearchParamDTO) {
        EsService esService = serviceMap.get(esSearchParamDTO);
        if (null == esService) {
            String esDomain = String.format("%s:80", esSearchParamDTO.getEsDomain());
            esService = new MatrixEsService(
                    esDomain,
                    "xiaomi",
                    "xiaomi",
                    esSearchParamDTO.getCatalog(),
                    esSearchParamDTO.getDbName(),
                    esSearchParamDTO.getEsToken());
            serviceMap.put(esSearchParamDTO, esService);
        }
        return esService;
    }

    /**
     * 用 appId 查 trace 接口/本地缓存获取 Matrix 平台部署时的 appNamespace
     */
    public TraceAppInfoDTO getTraceBaseInfoByAppId(Long appId) {
        TraceAppInfoDTO baseTraceAppInfo = appIdTraceAppBaseInfoCache.getOrDefault(appId, TraceAppInfoDTO.builder().build());
        // 链路同步应用信息时可能MatrixNamespace为空，以该字段作为判断标准确保从链路获取最新的数据
        if (StringUtils.isEmpty(baseTraceAppInfo.getAppNamespace())) {
            //get treeId and appName as pods-query params
            List<TraceAppInfoDTO> traceAppInfoDTOList = queryTraceAppInfos(new Long[]{appId});
            if (CollectionUtils.isEmpty(traceAppInfoDTOList)) {
                return baseTraceAppInfo;
            }
            baseTraceAppInfo.setAppName(traceAppInfoDTOList.get(0).getAppName());
            baseTraceAppInfo.setIamTreeId(traceAppInfoDTOList.get(0).getIamTreeId());
            baseTraceAppInfo.setAccountId(traceAppInfoDTOList.get(0).getAccountId());
            for (TraceAppInfoDTO dto : traceAppInfoDTOList) {
                if (StringUtils.isNotEmpty(dto.getAppNamespace())) {
                    baseTraceAppInfo.setAppNamespace(dto.getAppNamespace());
                    break;
                }
            }
            appIdTraceAppBaseInfoCache.put(appId, baseTraceAppInfo);
        }
        return baseTraceAppInfo;
    }

    /**
     * 用  appId, deploySpaceName 查 trace接口/本地缓存获取应用基础信息，然后查询 matrix 接口获取到 部署空间下的 pod 信息
     *
     * @param appId           链路的appId
     * @param deploySpaceName 部署空间名称
     * @return MatrixAppsDTO.MatrixDeploySpace
     */
    public MatrixAppsDTO.MatrixDeploySpace getMatrixPodInfoByAppInfo(Long appId, String deploySpaceName) {
        MatrixAppsDTO.MatrixDeploySpace space = new MatrixAppsDTO.MatrixDeploySpace();
        if (appId <= 0 || deploySpaceName == null || Objects.equals(deploySpaceName, "")) {
            return space;
        }
        try {

            TraceAppInfoDTO baseTraceAppInfo = getTraceBaseInfoByAppId(appId);
            if (null == baseTraceAppInfo || baseTraceAppInfo.getAppName() == null || baseTraceAppInfo.getAppName().length() == 0) {
                return space;
            }
            //get pods
            MatrixAppsDTO.MatrixDeploySpace result = queryMatrixDeploySpaceLevelPods(baseTraceAppInfo.getIamTreeId(), baseTraceAppInfo.getAppName(), deploySpaceName);
            if (null == result) {
                return space;
            }
            space.setPodIp2PodMap(result.getPodIp2PodMap());
            space.setName(deploySpaceName);
            space.setId(result.getId());
            space.setPodIPs(result.getPodIPs());
            space.setAppNameSpace(baseTraceAppInfo.getAppNamespace());
            return space;
        } catch (Exception e) {
            log.error("[MilogLogtailService.getMatrixPodInfoByAppInfo] err, appId:{}, deploySpace:{}, exception:{}", appId, deploySpaceName, e);
            return space;
        }
    }

    public TraceAppInfoDTO queryTraceAppInfos(Long appId) {
        List<TraceAppInfoDTO> result = queryTraceAppInfos(new Long[]{appId});
        if (CollectionUtils.isNotEmpty(result)) {
            return result.get(0);
        }
        return null;
    }

    /**
     * 用 appId 查 trace 接口（/tracing/v1/app/:heraAppId）获取到 app_name, iamTreeId, matrixDeploySpace
     *
     * @param appIds 链路的appIds
     */
    public List<TraceAppInfoDTO> queryTraceAppInfos(Long[] appIds) {
        List<TraceAppInfoDTO> traceAppInfoDTOList = new ArrayList<>();
        String mioapDomain = Config.ins().get("mioap_domain", "http://staging-mioap.api.xiaomi.net");
        String traceAppInfoApi = "/tracing/v1/app/log/";
        for (Long appId : appIds) {
            String returnGet = "";
            try {
                String url = String.format("%s%s%d", mioapDomain, traceAppInfoApi, appId);
                returnGet = HttpClientV6.get(url, null, 10000);
                //判断当前字符串是不是正常的json格式
                if (!returnGet.startsWith("{")) {
                    continue;
                }
                TraceAppInfoResponseDTO traceAppInfoResponseDTO = JSON.parseObject(returnGet, TraceAppInfoResponseDTO.class);
                if (null == traceAppInfoResponseDTO.getData()) {
                    continue;
                }
                List<TraceAppInfoDTO> tmpTraceAppInfoDTOList = convertToTraceAppInfo(traceAppInfoResponseDTO);
                traceAppInfoDTOList.addAll(tmpTraceAppInfoDTOList);
            } catch (Exception e) {
                log.error("queryTraceAppInfos err when processing appId, exit early. appId:{},returnGet:{}, exception:{}", appIds, returnGet, e);
            }
        }
        return traceAppInfoDTOList;
    }

    public static void main(String[] args) {
        String str = "{\"limit\":0,\"offset\":0,\"total\":0,\"code\":0,\"retryable\":false}";
        TraceAppInfoResponseDTO traceAppInfoResponseDTO = JSON.parseObject(str, TraceAppInfoResponseDTO.class);
        System.out.println(traceAppInfoResponseDTO);
    }

    private List<TraceAppInfoDTO> convertToTraceAppInfo(TraceAppInfoResponseDTO traceAppInfoResponseDTO) {
        List<TraceAppInfoDTO> traceAppInfoDTOList = new ArrayList<>();
        if (null == traceAppInfoResponseDTO || null == traceAppInfoResponseDTO.getData() || null == traceAppInfoResponseDTO.getData().getServices()) {
            return traceAppInfoDTOList;
        }
        // 线上可能一个应用对应多个机房，这里 service 会有多个
        // todo 后面再考虑按机房查日志，前端选择机房
        for (TraceAppInfoResponseDTO.TraceAppService service : traceAppInfoResponseDTO.getData().getServices()) {
            TraceAppInfoResponseDTO.TraceAppExtra traceAppExtra = gson.fromJson(service.getExtraConfigJson(), TraceAppInfoResponseDTO.TraceAppExtra.class);
            TraceAppInfoResponseDTO.ExtraDeployInfo extraDeployInfo = gson.fromJson(traceAppExtra.getAgentExtra(), TraceAppInfoResponseDTO.ExtraDeployInfo.class);
            Long iamTreeId = traceAppInfoResponseDTO.getData().getIamTreeId();
            if (iamTreeId == null || iamTreeId <= 0) {
                iamTreeId = service.getIamTreeId();
            }
            TraceAppInfoDTO traceAppInfoDTO = TraceAppInfoDTO.builder()
                    .tracingCluster(service.getTracingCluster())
                    .serviceCluster(service.getServiceCluster())
                    .serviceClusterId(service.getServiceClusterId())
                    .tracingService(service.getServiceName())
                    .appId(traceAppInfoResponseDTO.getData().getAppId())
                    .appName(traceAppInfoResponseDTO.getData().getAppName())
                    .accountId(traceAppInfoResponseDTO.getData().getOrgId())
                    .deployPlatform(traceAppInfoResponseDTO.getData().getDeployPlatform())
                    .iamTreeId(iamTreeId)
                    .matrixDeploySpace(extraDeployInfo.getMatrixDeploySpace())
                    .matrixNamespace(extraDeployInfo.getMatrixNamespace())
                    .appNamespace(extraDeployInfo.getMatrixNamespace())
                    .build();
            // 替换 cloudml 的 namespace
            if (StringUtils.isNotBlank(extraDeployInfo.getCloumlNamespace())) {
                traceAppInfoDTO.setMatrixNamespace(extraDeployInfo.getCloumlNamespace());
                traceAppInfoDTO.setAppNamespace(extraDeployInfo.getCloumlNamespace());
                String cloumlInstances = extraDeployInfo.getCloumlInstanceList();
                if (StringUtils.isNotBlank(cloumlInstances)) {
                    List<CloudmlPod> cloudmlPods = new Gson().fromJson(cloumlInstances, new TypeToken<List<CloudmlPod>>() {
                    }.getType());
                    traceAppInfoDTO.setCloudmlPodList(cloudmlPods.stream().map(CloudmlPod::toLogAgentListBo).collect(Collectors.toList()));
                }
            }
            traceAppInfoDTOList.add(traceAppInfoDTO);
        }
        return traceAppInfoDTOList;
    }

    /**
     * 用 iamTreeId,deploySpaceId,deployUnitId 查融合云 matrix 接口（/api/v1/apps/deploy-spaces/:id/deploy-units）获取 pods
     *
     * @param iamTreeId     iam树节点
     * @param deploySpaceId matrix的部署空间id，唯一
     */
    private List<MatrixDataDTO.Pod> queryMatrixPodsByDeployInfo(Long iamTreeId, Long deploySpaceId, Long deployUnitId) {
        if (iamTreeId <= 0 || deploySpaceId <= 0 || deployUnitId <= 0) {
            return null;
        }
        String matrixDomain = Config.ins().get("matrix_domain", "http://production-matrix.api.xiaomi.net");
        String url = matrixDomain + "/api/v1/apps/deploy-spaces/" + deploySpaceId + "/deploy-units/" + deployUnitId + "/pods";

        String result = IamService.requestIam("GET", url, "", iamTreeId);
        if ("".equals(result)) {
            return null;
        }
        try {
            MatrixPodDTO matrixPodDTO = gson.fromJson(result, MatrixPodDTO.class);
            if (0 != matrixPodDTO.getCode()) {
                log.info("query matrix pods failed, response:{}, iamTreeId:{}, deploySpaceId:{}, deployUnitId:{}", result, iamTreeId, deploySpaceId, deployUnitId);
                return null;
            }
            return matrixPodDTO.getData();
        } catch (Exception exception) {
            log.error("query matrix pods failed for exception, response:{}, iamTreeId:{}, deploySpaceId:{}, deployUnitId:{}, exception:{}", result, iamTreeId, deploySpaceId, deployUnitId, exception.getMessage());
        }
        return null;
    }

    /**
     * 用 iamTreeId 和 deploySpace 查融合云 matrix 接口（/api/v1/apps/deploy-spaces/:id/deploy-units）获取 deploySpaceUnit
     */
    private List<MatrixDataDTO.DeployUnit> queryMatrixDeploySpaceUnitBySpaceId(Long iamTreeId, Long deploySpaceId) {
        if (iamTreeId <= 0 || deploySpaceId <= 0) {
            return null;
        }
        String matrixDomain = Config.ins().get("matrix_domain", "http://production-matrix.api.xiaomi.net");
        String url = String.format("%s%s%d%s", matrixDomain, "/api/v1/apps/deploy-spaces/", deploySpaceId, "/deploy-units");

        String result = IamService.requestIam("GET", url, "", iamTreeId);
        if ("".equals(result)) {
            log.info("query matrix pods failed for empty response. iamTreeId:{}, deploySpaceId:{}", iamTreeId, deploySpaceId);
            return null;
        }
        try {
            MatrixDeployUnitDTO matrixDeployUnitsDTO = gson.fromJson(result, MatrixDeployUnitDTO.class);
            if (0 != matrixDeployUnitsDTO.getCode()) {
                log.info("query matrix deploy units failed, response:{}, iamTreeId:{}, deploySpaceId:{}", result, iamTreeId, deploySpaceId);
                return null;
            }
            return matrixDeployUnitsDTO.getData();
        } catch (Exception exception) {
            log.error("query matrix deploy units failed for exception, response:{}, iamTreeId:{}, deploySpaceId:{}, exception:{}", result, iamTreeId, deploySpaceId, exception.getMessage());
        }
        return null;
    }

    private String deploySpaceCacheKey(Long iamTreeId, String appName, String deploySpace) {
        return iamTreeId + "_" + appName + "_" + deploySpace;
    }

    /**
     * 用 iamTreeId,appName,deploySpace 查融合云 matrix 接口获取 deploySpace下的所有pod信息
     * Matrix DO层级： app->deploySpace->deployUnit->podIP/hostIP
     */
    public MatrixAppsDTO.MatrixDeploySpace queryMatrixDeploySpaceLevelPods(Long iamTreeId, String appName, String deploySpace) throws Exception {
        MatrixAppsDTO.MatrixDeploySpace result = new MatrixAppsDTO.MatrixDeploySpace();
        HashMap<String, MatrixDataDTO.Pod> mapPodIp2Pod = new HashMap<>();
        List<String> podIPs = new ArrayList<>();
        String cacheKey = deploySpaceCacheKey(iamTreeId, appName, deploySpace);
        Long deploySpaceId = CACHE_LOCAL_DEPLOY_SPACES.getIfPresent(cacheKey);
        if (null == deploySpaceId || deploySpaceId <= 0) {
            //query matrix app list
            MatrixAppsDTO matrixAppsDTO = queryMatrixApps(iamTreeId);
            if (null == matrixAppsDTO) {
                return null;
            }

            //filter deploySpace id
            deploySpaceId = filterDeploySpace(appName, deploySpace, matrixAppsDTO);
            CACHE_LOCAL_DEPLOY_SPACES.put(cacheKey, deploySpaceId);
        }

        //get all deployUnit under deploySpace
        List<MatrixDataDTO.DeployUnit> deployUnits = queryMatrixDeploySpaceUnitBySpaceId(iamTreeId, deploySpaceId);

        if (null == deployUnits) {
            return result;
        }
        //query ips under deployUnit and assemble
        Long finalDeploySpaceId = deploySpaceId;
        deployUnits.forEach(currentUnit -> {
            List<MatrixDataDTO.Pod> pods = queryMatrixPodsByDeployInfo(iamTreeId, finalDeploySpaceId, currentUnit.getId());
            if (!CollectionUtils.isEmpty(pods)) {
                pods.forEach(currentPod -> {
                    //ignore pod under init/crashed status for next round
                    if (StringUtils.isNotEmpty(currentPod.getPodIP())
                            && currentPod.isAlivePod()) {
                        mapPodIp2Pod.put(currentPod.getPodIP(), currentPod);
                        podIPs.add(currentPod.getPodIP());
                    }
                });
            }
        });

        result.setId(deploySpaceId);
        result.setName(deploySpace);
        result.setPodIp2PodMap(mapPodIp2Pod);
        result.setPodIPs(podIPs);
        return result;
    }

    public String hostNameToIP(String hostName) {
        if (StringUtils.isNotEmpty(CACHE_LOCAL_HOST_IPS.getIfPresent(hostName))) {
            return CACHE_LOCAL_HOST_IPS.getIfPresent(hostName);
        }
        try {
            InetAddress address = InetAddress.getByName(hostName);
            String ip = address.getHostAddress();
            CACHE_LOCAL_HOST_IPS.put(hostName, ip);
            return ip;
        } catch (Exception e) {
            log.error("matrix: convert hostName to ip failed, hostName:{}, exception:{}", hostName, e);
            return hostName;
        }
    }


    /**
     * 用 ip 查融合云 matrix 接口（/api/v1/tools/search-ip）
     */
    public List<MatrixDataDTO.PodInfo> queryMatrixPodInfoByIp(String ip) {
        String matrixDomain = Config.ins().get("matrix_domain", "http://production-matrix.api.xiaomi.net");
        String matrixAppsApi = "/api/v1/tools/search-ip";
        String url = String.format("%s%s?ip=%s", matrixDomain, matrixAppsApi, ip);
        String result = IamService.requestIam("GET", url, "", 0L);
        if ("".equals(result)) {
            return null;
        }
        MatrixPodInfoDTO matrixPodInfoDTO = gson.fromJson(result, MatrixPodInfoDTO.class);
        if (0 != matrixPodInfoDTO.getCode()) {
            log.info("query matrix pod info by ip failed, ip:{}, response:{}", ip, result);
            return null;
        }
        //agent 初始化时将 agent name->agentIp 存在缓存中作为兜底
        for (MatrixDataDTO.PodInfo pod : matrixPodInfoDTO.getData()) {
            //agent ip query
            if (Objects.equals(pod.getNodeIp(), ip)) {
                CACHE_LOCAL_HOST_IPS.put(pod.getNodeName(), pod.getNodeIp());
                break;
            }
            //pod ip query
            if (StringUtils.isNotEmpty(pod.getNodeName())) {
                CACHE_LOCAL_HOST_IPS.put(pod.getNodeName(), pod.getNodeIp());
            }
        }

        //不过滤掉停止运行的 pod，在调用的地方按需过滤
        return matrixPodInfoDTO.getData();
    }

    /**
     * 用 iamTreeId 查融合云 matrix 接口（/api/v1/apps）
     */
    private MatrixAppsDTO queryMatrixApps(Long iamTreeId) {
        String matrixDomain = Config.ins().get("matrix_domain", "http://production-matrix.api.xiaomi.net");
        String matrixAppsApi = "/api/v1/apps";
        String url = String.format("%s%s", matrixDomain, matrixAppsApi);

        String result = IamService.requestIam("GET", url, "", iamTreeId);
        if ("".equals(result) || !CommonUtil.isJson(result)) {
            return null;
        }
        try {
            MatrixAppsDTO matrixAppsDTO = gson.fromJson(result, MatrixAppsDTO.class);
            if (0 != matrixAppsDTO.getCode()) {
                log.info("query matrix app failed, iamTreeId:{}, response:{}", iamTreeId, result);
                return null;
            }
            return matrixAppsDTO;
        } catch (Exception e) {
            log.error("query matrix app failed, iamTreeId:{}, response:{}", iamTreeId, result, e);
        }
        return null;
    }

    /**
     * 用 app_name 和 matrixDeploySpace 过滤出 deploySpace 的 id
     */
    private Long filterDeploySpace(String appName, String matrixDeploySpace, MatrixAppsDTO matrixAppsDTO) {
        if (null == matrixAppsDTO || null == matrixAppsDTO.getData()) {
            return (long) -1;
        }
        List<MatrixAppsDTO.MatrixAppData> appDataList = matrixAppsDTO.getData();
        for (MatrixAppsDTO.MatrixAppData app : appDataList) {
            if ((!app.getName().isEmpty() && !appName.equals(app.getName())) || null == app.getDeploySpaces()) {
                continue;
            }
            List<MatrixAppsDTO.MatrixDeploySpace> deploySpaceList = app.getDeploySpaces();
            for (MatrixAppsDTO.MatrixDeploySpace deploySpace : deploySpaceList) {
                if (!deploySpace.getName().isEmpty() && !matrixDeploySpace.equals(deploySpace.getName())) {
                    continue;
                }
                return deploySpace.getId();
            }
        }
        return (long) -1;
    }

    /**
     * 用 deploySpaceId 去查融合云 matrix 接口（/api/v1/log/collections?deploySpaceId=xxxx）得到日志信息
     */
    private MatrixLogCollectionsDTO queryMatrixLogCollections(Long deploySpaceId, Long iamTreeId) {
        String matrixDomain = Config.ins().get("matrix_domain", "http://production-matrix.api.xiaomi.net");
        ;
        String matrixAppsApi = "/api/v1/log/collections?deploySpaceId=";
        String url = String.format("%s%s%d", matrixDomain, matrixAppsApi, deploySpaceId);
        String result = IamService.requestIam("GET", url, "", iamTreeId);
        if ("".equals(result)) {
            return null;
        }

        MatrixLogCollectionsDTO matrixLogCollectionsDTO = gson.fromJson(result, MatrixLogCollectionsDTO.class);
        if (0 != matrixLogCollectionsDTO.getCode()) {
            log.info("query log collections from matrix failed, deploySpaceId:{}, iamTreeId:{}, response:{}", deploySpaceId, iamTreeId, result);
            return null;
        }

        return matrixLogCollectionsDTO;
    }

    /**
     * 用日志配置 id 和 cluster 组装参数去工场查 es 地址
     */
    private List<EsSearchParamDTO> queryMatrixEsIndexList(MatrixLogCollectionsDTO matrixLogCollectionDTO, String deploySpaceName) {
        List<EsSearchParamDTO> esSearchParamDTOList = new ArrayList<>();
        List<DTTableGetParamDTO> dtTableGetParamDTOList = generateEsIndexList(matrixLogCollectionDTO, deploySpaceName);

        String dtDomain = Config.ins().get("dt_domain", "https://api-gateway.dp.pt.xiaomi.com");
        String dtTableGetApi = "/openapi/metadata/table/get";
        String urlBase = String.format("%s%s", dtDomain, dtTableGetApi);
        Map<String, String> headerMap = Maps.newHashMap();
        headerMap.put("Content-Type", "application/x-www-form-urlencoded");

        for (DTTableGetParamDTO dto : dtTableGetParamDTOList) {
            headerMap.put("Authorization", String.format("workspace-token/1.0 %s", dto.getEsToken()));
            String url = String.format("%s?catalog=%s&dbName=%s&tableNameEn=%s", urlBase, dto.getCatalog(), dto.getDbName(), dto.getTableNameEn());
            String returnGet = HttpClientV6.get(url, headerMap, 5000);
            try {
                DTTableGetResponseDTO dtTableGetResponseDTO = gson.fromJson(returnGet, DTTableGetResponseDTO.class);
                if ("".equals(dtTableGetResponseDTO.getEsClusterDomain())) {
                    log.warn("query matrix es index from datum failed, request_url:{}, response:{}", url, returnGet);
                    continue;
                }

                EsSearchParamDTO esSearchParamDTO = new EsSearchParamDTO(
                        dtTableGetResponseDTO.getEsClusterDomain(),
                        dto.getTableNameEn(),
                        dto.getCatalog(),
                        dto.getDbName(),
                        dto.getEsToken());
                esSearchParamDTOList.add(esSearchParamDTO);
            } catch (Exception exception) {
                log.error("query matrix es index from datum failed, response:{}, exception:{}", returnGet, exception.getMessage());
            }
        }

        return esSearchParamDTOList;
    }

    private List<DTTableGetParamDTO> generateEsIndexList(MatrixLogCollectionsDTO matrixLogCollectionsDTO, String deploySpaceName) {
        List<DTTableGetParamDTO> dtTableGetParamDTOList = new ArrayList<>();
        if (null == matrixLogCollectionsDTO || null == matrixLogCollectionsDTO.getData()) {
            return dtTableGetParamDTOList;
        }
        for (MatrixLogCollectionsDTO.MatrixLogCollection matrixLogCollection : matrixLogCollectionsDTO.getData()) {
            if (null == matrixLogCollection.getJobs()) {
                continue;
            }
            List<DTTableGetParamDTO> tmpList = generateEsIndex(matrixLogCollection, deploySpaceName);
            dtTableGetParamDTOList.addAll(tmpList);
        }
        return dtTableGetParamDTOList;
    }

    private List<DTTableGetParamDTO> generateEsIndex(MatrixLogCollectionsDTO.MatrixLogCollection matrixLogCollection, String deploySpaceName) {
        List<DTTableGetParamDTO> dtTableGetParamDTOList = new ArrayList<>();
        if (null == matrixLogCollection) {
            return dtTableGetParamDTOList;
        }
        for (MatrixLogCollectionsDTO.MatrixLogCollectionJob job : matrixLogCollection.getJobs()) {
            if (job.getActive().equals(ActiveFalse) || !job.isJobStatus()) {
                continue;
            }
            List<MilogMatrixEsInfoDO> milogMatrixEsInfoDOList = null;
            synchronized (this) {
                milogMatrixEsInfoDOList = milogMatrixEsInfoMapper.selectByCluster(job.getCluster());
            }
            if (0 == milogMatrixEsInfoDOList.size()) {
                continue;
            }
            DTTableGetParamDTO dto = convertToDTTableGetParam(milogMatrixEsInfoDOList.get(0));
            String index = StringUtils.replace(String.format("%s_%s_%d", job.getCluster(), deploySpaceName, matrixLogCollection.getId()), "-", "_");
            dto.setTableNameEn(index);
            dtTableGetParamDTOList.add(dto);
        }
        return dtTableGetParamDTOList;
    }

    private DTTableGetParamDTO convertToDTTableGetParam(MilogMatrixEsInfoDO milogMatrixEsInfoDO) {
        DTTableGetParamDTO dto = new DTTableGetParamDTO();
        dto.setCatalog(milogMatrixEsInfoDO.getEsCatalog());
        dto.setDbName(milogMatrixEsInfoDO.getEsDatabase());
        dto.setEsToken(milogMatrixEsInfoDO.getEsToken());
        return dto;
    }

    /**
     * 查 es 数据
     */
    private List<LogDTO> queryMatrixLog(List<EsSearchParamDTO> esSearchParamDTOList, LogQuery logQuery) throws Exception {
        List<LogDTO> logList = new ArrayList<>();
        for (EsSearchParamDTO esSearchParamDTO : esSearchParamDTOList) {
            String esIndexName = esSearchParamDTO.getIndex();
            EsService esService = getEsService(esSearchParamDTO);
            // 构建查询参数
            // 构造 keyList
            List<String> keyList = matrixDefaultKeyList;
            BoolQueryBuilder boolQueryBuilder = searchLog.getMatrixQueryBuilder(logQuery, keyList);
            SearchSourceBuilder builder = new SearchSourceBuilder();
            builder.query(boolQueryBuilder);
            LogDTO dto = new LogDTO();
            // 统计
            CountRequest countRequest = new CountRequest();
            countRequest.indices(esIndexName);
            countRequest.source(builder);
            Long total = esService.count(countRequest);
            dto.setTotal(total);
            // 查询
            builder.sort("alpha_timestamp", logQuery.getAsc() ? ASC : DESC);
            // 分页
//            if (logQuery.getBeginSortValue() != null && logQuery.getBeginSortValue().length != 0) {
//                builder.searchAfter(logQuery.getBeginSortValue());
//            }
            builder.from((logQuery.getPage() - 1) * logQuery.getPageSize());
            builder.size(logQuery.getPageSize());
            // 高亮
            builder.highlighter(getHighlightBuilder(keyList));
            builder.timeout(TimeValue.timeValueMinutes(1L));
            SearchRequest searchRequest = new SearchRequest(esIndexName);
            searchRequest.source(builder);
            log.info("[MatrixLogService.queryMatrixLog] searchRequest: [{}]", searchRequest.toString());
            SearchResponse searchResponse = esService.search(searchRequest);
            SearchHit[] hits = searchResponse.getHits().getHits();
            if (hits == null || hits.length == 0) {
                continue;
            }
            List<LogDataDTO> logDataList = new ArrayList<>();
            LogDataDTO logData;
            for (SearchHit hit : hits) {
                logData = hit2DTO(hit, keyList);
                // 封装高亮
                logData.setHighlight(getHightlinghtMap(hit));
                logDataList.add(logData);
            }
            dto.setThisSortValue(hits[hits.length - 1].getSortValues());
            dto.setLogDataDTOList(logDataList);
            logList.add(dto);
        }

        return logList;
    }

    private HighlightBuilder getHighlightBuilder(List<String> keyList) {
        HighlightBuilder highlightBuilder = new HighlightBuilder();
        for (String key : keyList) {
            if (noHighLightSet.contains(key)) {
                continue;
            }
            HighlightBuilder.Field highlightField = new HighlightBuilder.Field(key);
            highlightBuilder.field(highlightField);
        }
        return highlightBuilder;
    }

    private LogDataDTO hit2DTO(SearchHit hit, List<String> keyList) {
        LogDataDTO logData = new LogDataDTO();
        Map<String, Object> ferry = hit.getSourceAsMap();
        Set<String> keySet = ferry.keySet();
        for (String key : keySet) {
            if (hiddenSet.contains(key)) {
                continue;
            }
            logData.setValue(key, ferry.get(key));
        }
        // 将 ISO8601 时间格式转换为毫秒，作为 timestamp 返回
        DateTimeFormatter parser = ISODateTimeFormat.dateTime();
        Long timestamp = parser.parseMillis(String.valueOf(ferry.get(esKeyMap_timestamp)));
        logData.setTimestamp(ferry.get(esKeyMap_timestamp) == null ? "" : String.valueOf(timestamp));
        logData.setValue("timestamp", ferry.get(esKeyMap_timestamp) == null ? new Date().getTime() : timestamp);
        logData.setLogOfString(GSON.toJson(logData.getLogOfKV()));
        return logData;
    }

    public void logExport(LogQuery logQuery) throws Exception {
        // 生成excel
        int maxLogNum = 10000;
        logQuery.setPageSize(maxLogNum);
        Result<LogDTO> logDTOResult = this.logQuery(logQuery);
        List<Map<String, Object>> exportData =
                logDTOResult.getCode() != CommonError.Success.getCode()
                        || logDTOResult.getData().getLogDataDTOList() == null
                        || logDTOResult.getData().getLogDataDTOList().isEmpty() ?
                        null : logDTOResult.getData().getLogDataDTOList().stream().map(logDataDto -> ExportUtils.SplitTooLongContent(logDataDto)).collect(Collectors.toList());
        HSSFWorkbook excel = ExportExcel.HSSFWorkbook4Map(exportData, generateTitle(logQuery));
        // 下载
        String fileName = String.format("%s_log.xls", logQuery.getLogstore());
        searchLog.downLogFile(excel, fileName);
    }

    private String generateTitle(LogQuery logQuery) {
        return String.format("%s日志，搜索词:[%s]，时间范围%d-%d", logQuery.getLogstore(), logQuery.getFullTextSearch() == null ? "" : logQuery.getFullTextSearch(), logQuery.getStartTime(), logQuery.getEndTime());
    }

    public Result<LogDTO> queryMatrixTraceLog(MatrixTraceLogParam matrixTraceLogParam) {
        // 根据 appIds 获取 logtails
        List<MilogLogTailDo> milogLogTailDoList = milogLogtailDao.queryByAppIds(matrixTraceLogParam.appIds);
        if (null == milogLogTailDoList || milogLogTailDoList.isEmpty()) {
            return Result.success();
        }

        // 根据 logtail 获取 logstore 的 es 索引列表
        List<MilogEsIndexDO> indexList = queryMatrixEsIndexByLogtails(milogLogTailDoList);
        if (null == indexList || indexList.isEmpty()) {
            return Result.success();
        }
        List<ClusterIndexVO> clusterIndexVOS = indexList.stream()
                .map(MilogEsIndexDO::toClusterIndexVO).distinct().collect(Collectors.toList());
        // 拼接参数：QueryBuilder
        SearchSourceBuilder qb = generateQb(matrixTraceLogParam.traceId, milogLogTailDoList, matrixTraceLogParam.getStartTime(), matrixTraceLogParam.getEndTime());

        // 异步查询
        TraceLogDTO traceLogDTO = traceLog.EsAsyncSearch(clusterIndexVOS, qb);
        return generateTraceLogResult(traceLogDTO);
    }

    public Result<LogDTO> generateTraceLogResult(TraceLogDTO traceLogDTO) {
        LogDTO logDTO = new LogDTO();
        logDTO.setTotal((long) traceLogDTO.getDataList().size());
        List<LogDataDTO> logDataDTOList = new ArrayList<>();
        for (String log : traceLogDTO.getDataList()) {
            LogDataDTO logDataDTO = new LogDataDTO();
            logDataDTO.setLogOfString(log);
            logDataDTOList.add(logDataDTO);
        }
        logDTO.setLogDataDTOList(logDataDTOList);
        return Result.success(logDTO);
    }

    public SearchSourceBuilder generateQb(String traceId, List<MilogLogTailDo> milogLogTailDoList, Long startTime, Long endTime) {
        SearchSourceBuilder qb = new SearchSourceBuilder();

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("timestamp").from(startTime).to(endTime));

        BoolQueryBuilder traceIdQueryBuilder = QueryBuilders.boolQuery();
        traceIdQueryBuilder.should(QueryBuilders.termQuery("traceId", traceId));
        traceIdQueryBuilder.should(QueryBuilders.termQuery("trace_id", traceId));
        traceIdQueryBuilder.should(QueryBuilders.matchQuery("message", traceId));
        traceIdQueryBuilder.minimumShouldMatch(1);
        boolQueryBuilder.filter(traceIdQueryBuilder);

        BoolQueryBuilder tailQueryBuilder = QueryBuilders.boolQuery();
        for (MilogLogTailDo tail : milogLogTailDoList) {
            tailQueryBuilder.should(QueryBuilders.termQuery("tail", tail.getTail()));
        }
        tailQueryBuilder.minimumShouldMatch(1);
        boolQueryBuilder.filter(tailQueryBuilder);

        qb.query(boolQueryBuilder);
        return qb;
    }

    public List<MilogEsIndexDO> queryMatrixEsIndexByLogtails(List<MilogLogTailDo> milogLogTailDoList) {
        List<MilogEsIndexDO> resultList = new ArrayList<>();
        for (MilogLogTailDo milogLogTailDo : milogLogTailDoList) {
            MilogEsIndexDO milogEsIndexDO = innerMilogLogstoreDao.queryEsIndexDOByStoreId(milogLogTailDo.getStoreId(), true);
            if (null != milogEsIndexDO) {
                resultList.add(milogEsIndexDO);
            }
        }
        return resultList;
    }
}
