package com.xiaomi.mone.log.manager.service.remoting;

import cn.hutool.core.lang.Pair;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.JsonArray;
import com.google.gson.reflect.TypeToken;
import com.xiaomi.mone.enums.InnerMachineRegionEnum;
import com.xiaomi.mone.log.manager.model.dt.ColumnDTO2;
import com.xiaomi.mone.log.manager.model.dt.EsCreateDTO;
import com.xiaomi.bigdata.workshop.model.FieldType2;
import com.xiaomi.mone.log.manager.model.dt.TalosCreateDTO;
import com.xiaomi.bigdata.workshop.model.WsTableDTO;
import com.xiaomi.mone.log.manager.common.utils.DtUtils;
import com.xiaomi.mone.log.manager.model.alert.AlertJobDetail;
import com.xiaomi.mone.log.manager.model.dto.dt.DtJobListResponse;
import com.xiaomi.mone.log.manager.model.dto.dt.DtTableDetailDTO;
import com.xiaomi.mone.log.manager.model.dto.dt.DtTableInfoDTO;
import com.xiaomi.mone.log.manager.model.dto.dt.DtTokenDetail;

import static com.xiaomi.mone.log.manager.user.InnerMoneUtil.gson;
import static com.xiaomi.mone.log.manager.user.MoneUserDetailService.GSON;
import static org.apache.ozhera.log.manager.service.impl.EsDataServiceImpl.requiredFields;

import com.xiaomi.youpin.docean.anno.Service;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.JsonNode;
import org.json.JSONArray;

import java.io.IOException;
import java.util.*;

/**
 * @author: songyutong1
 * @date: 2024/09/03/20:29
 */
@Service
@Slf4j
public class DtRemoteService {

    private static final int PARTITION_NUMBER = 1;

    /**
     * 查询数据工厂表列表
     */
    public List<DtTableInfoDTO> queryDtTableList(String catalog,
                                                 String dbName,
                                                 boolean isGranted,
                                                 boolean isIncludePartitionTable,
                                                 String keyword,
                                                 String service,
                                                 String token) {
        String url = String.format(DtUtils.URL_TABLE_LIST_QUERY + "?catalog=%s&dbName=%s&isGranted=%b&isIncludePartitionTable=%b&keyword=%s&service=%s", catalog, dbName, isGranted, isIncludePartitionTable, keyword, service);
        JsonArray jsonArray = DtUtils.get(url, token, JsonArray.class);
        return gson.fromJson(jsonArray, new TypeToken<List<DtTableInfoDTO>>() {
        }.getType());
    }


    /**
     * 查询数据工厂表详情
     */
    public DtTableDetailDTO queryDtTableDetail(String catalog, String dbName, String tableNameEn, String token) {
        String url = String.format(DtUtils.URL_TABLE_DETAIL_QUERY + "?catalog=%s&dbName=%s&tableNameEn=%s", catalog, dbName, tableNameEn);
        return DtUtils.get(url, token, DtTableDetailDTO.class);
    }

    /**
     * 创建esTable
     */
    public boolean createEsTable(String region,String catalog, String dbName, String tableNameEn, String token, String keyList, String columnTypeListStr, Integer storePeriod, String description) {
        String url = DtUtils.URL_TABLE_ES_CREATE;
        List<ColumnDTO2> columnDTOs = generateColumnDTOs(keyList, columnTypeListStr);
        EsCreateDTO esCreateDTO = new EsCreateDTO();
        int preserveTime = storePeriod > 7 ? 7 : storePeriod;
        if (InnerMachineRegionEnum.SH_MACHINE.getEn().equals(region)) {
            preserveTime = storePeriod > 60 ? 60 : storePeriod > 30 ? 30 : storePeriod > 15 ? 15 : 7;
        }
        esCreateDTO.autoMapping(true)
                .name(tableNameEn)
                .catalog(catalog)
                .dbName(dbName)
                .partitionUnit("day")
                .preserveTime(preserveTime)
                .description(description)
                .columnDtos(columnDTOs);
        String body = GSON.toJson(esCreateDTO);
        log.info("create es index in dt, request:{}", body);
        WsTableDTO wsTableDTO = DtUtils.post(url, body, token, WsTableDTO.class);
        if (null == wsTableDTO) {
            log.error("创建 es 索引失败, request:{}, index:{}", body, tableNameEn);
            return false;
        }
        return true;
    }

    /**
     * 创建talosTable
     */
    public boolean createTalosTable(String catalog, String dbName, String tableNameEn, String token, String description) {
        String url = DtUtils.URL_TABLE_TALOS_CREATE;
        List<ColumnDTO2> columnDTOs = generateExampleColumnDTOs();
        TalosCreateDTO talosCreateDTO = new TalosCreateDTO();
        talosCreateDTO.name(tableNameEn)
                .partitionNum(PARTITION_NUMBER)
                .catalog(catalog)
                .dbName(dbName)
                .preserveTime(24)
                .serializationLib("json")
                .description(description)
                .columnDtos(columnDTOs);
        String body = GSON.toJson(talosCreateDTO);
        WsTableDTO wsTableDTO = DtUtils.post(url, body, token, WsTableDTO.class);
        if (null == wsTableDTO) {
            log.error("创建talos表失败, request:{}, topic:{}", body, tableNameEn);
            return false;
        }
        return true;
    }

    /**
     * 删除工场表
     */
    public boolean deleteDtTable(String catalog, String dbName, String tableNameEn, String token) {
        String url = DtUtils.URL_TABLE_DELETE;
        List<WsTableDTO> wsTableDTOList = new ArrayList<>();
        WsTableDTO wsTableDTO = new WsTableDTO().catalog(catalog).dbName(dbName)
                .tableNameEn(tableNameEn);
        wsTableDTOList.add(wsTableDTO);
        String body = GSON.toJson(wsTableDTOList);
        return DtUtils.post(url, body, token, Boolean.class);
    }

    /**
     * 查询任务列表
     */
    public DtJobListResponse queryJobList(Long page, Long pageSize, Long jobId, boolean owner, String searchKey, String jobTypes, String token) {
        String url = String.format(DtUtils.URL_JOB_LIST_QUERY + "?page=%d&pageSize=%d&jobTypes=%s&owner=%b&searchKey=%s", page, pageSize, jobTypes, owner, searchKey);
        if (jobId != null) {
            url = String.format(url + "&jobId=%d", jobId);
        }
        return DtUtils.get(url, token, DtJobListResponse.class);
    }

    /**
     * 查询作业详情
     */
    public AlertJobDetail queryJobDetail(Long jobId, String token) {
        String url = String.format(DtUtils.URL_JOB_DETAIL_QUERY, jobId);
        return DtUtils.get(url, token, AlertJobDetail.class);
    }

    /**
     * 启动作业
     */
    public Integer startJob(Long jobId, String token) {
        String url = String.format(DtUtils.URL_JOB_START, jobId);
        return DtUtils.post(url, "{}", token, Integer.class);
    }

    public Boolean stopJob(Long jobId, String token) {
        String url = String.format(DtUtils.URL_JOB_STOP, jobId);
        return DtUtils.post(url, "{}", token, Boolean.class);
    }

    /**
     * 删除任务
     */
    public Integer deleteJobForce(Long jobId, String token) {
        String url = String.format(DtUtils.URL_JOB_DELETE, jobId);
        return DtUtils.post(url, "{}", token, Integer.class);
    }

    /**
     * 查询token详情
     */
    public DtTokenDetail queryTokenDetail(String token) {
        String url = DtUtils.URL_TOKEN_DETAIL;
        return DtUtils.get(url, token, DtTokenDetail.class);
    }

    private List<ColumnDTO2> generateColumnDTOs(String keyListStr, String columnTypeListStr) {
        List<ColumnDTO2> columnDTO2List = new ArrayList<>();
        String[] keyList = keyListStr.split(",");
        String[] columnTypeList = columnTypeListStr.split(",");
        for (int i = 0; i < keyList.length; i++) {
            String keyName = keyList[i].split(":")[0];
            ColumnDTO2 columnDTO2 = new ColumnDTO2();
            columnDTO2.fieldName(keyName).type(new FieldType2().type(columnTypeList[i])).isKey(false);
            columnDTO2List.add(columnDTO2);
        }
        try {
            extraFieldHandle(columnDTO2List);
        } catch (Exception e) {
            log.error("生成默认 es 索引字段列表失败，keyListStr:{},columnTypeListStr:{}, columnDTO2List:{},e:", keyListStr, columnTypeList, columnDTO2List, e);
        }
        return columnDTO2List;
    }

    private void extraFieldHandle(List<ColumnDTO2> columnDTO2List) throws IOException {

        List<Pair<String, Pair<String, Integer>>> fields = requiredFields;
        fields.add(new Pair<>("spaceId", new Pair<>("integer", 3)));
        fields.add(new Pair<>("storeId", new Pair<>("integer", 3)));

        for (Pair<String, Pair<String, Integer>> requiredField : fields) {
            if (columnDTO2List.stream()
                    .map(ColumnDTO2::getFieldName)
                    .noneMatch(fieldName -> Objects.equals(requiredField.getKey(), fieldName))) {
                ColumnDTO2 columnDTO2 = new ColumnDTO2();
                columnDTO2.fieldName(requiredField.getKey())
                        .type(new FieldType2().type(requiredField.getValue().getKey())).isKey(false);
                columnDTO2List.add(columnDTO2);
            }
        }

        if (!columnDTO2List.stream()
                .map(ColumnDTO2::getFieldName)
                .filter(fieldName -> Objects.equals("timestamp", fieldName))
                .findAny().isPresent()) {
            ColumnDTO2 columnDTO2 = new ColumnDTO2();
            columnDTO2.fieldName("timestamp").type(new FieldType2().type("date")).isKey(false);
            columnDTO2List.add(columnDTO2);
        }
        for (ColumnDTO2 column : columnDTO2List) {
            //text类型的字段都加上分词器
            if ("logsource".equals(column.getFieldName()) || "message".equals(column.getFieldName()) ||
                    "text".equals(column.getType().getType())) {
                column.setType(new FieldType2().type("text"));
                Map<String, Object> objectNode = new HashMap<>();
                objectNode.put("source_excludes", false);
                objectNode.put("index", true);
                objectNode.put("analyzer", "ik_max_word");
                column.setExtra(objectNode);
            }
            //数据工场时间类型字段勾选按时间筛选，创建的索引模式才能带时间筛选框
            if ("timestamp".equals(column.getFieldName())) {
                //String jsonString = "{\"source_excludes\":false,\"index\":true,\"format\":\"epoch_millis\"}";
                Map<String, Object> objectNode = new HashMap<>();
                objectNode.put("source_excludes", false);
                objectNode.put("index", true);
                objectNode.put("format", "epoch_millis");
                column.setExtra(objectNode);
                column.setIsKey(true);
            }
        }
    }

    /**
     * 伪造一个 talos topic 字段，创建 topic 表时字段必填，但与工场同学沟通得知实际只用于工场内部统计
     *
     * @return
     */
    private List<ColumnDTO2> generateExampleColumnDTOs() {
        List<ColumnDTO2> columnDTO2List = new ArrayList<>();
        ColumnDTO2 columnDTO2 = new ColumnDTO2();
        columnDTO2.fieldName("message").type(new FieldType2().type("string")).isKey(false);
        columnDTO2List.add(columnDTO2);
        return columnDTO2List;
    }
}
