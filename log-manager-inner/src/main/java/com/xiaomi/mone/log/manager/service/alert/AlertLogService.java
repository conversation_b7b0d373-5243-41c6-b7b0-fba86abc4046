package com.xiaomi.mone.log.manager.service.alert;

import cn.hutool.core.thread.ThreadUtil;
import com.gliwka.hyperscan.util.PatternFilter;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.gson.Gson;
import com.xiaomi.mone.log.manager.dao.alert.AlertLogDao;
import com.xiaomi.mone.log.manager.model.alert.Alert;
import com.xiaomi.mone.log.manager.model.alert.AlertCondition;
import com.xiaomi.mone.log.manager.model.alert.AlertRule;
import com.xiaomi.mone.log.manager.model.bo.RegexMatchRes;
import com.xiaomi.mone.log.manager.model.bo.alert.AlertLogBo;
import com.xiaomi.mone.log.manager.model.bo.alert.AlertMsgPattern;
import com.xiaomi.mone.log.manager.model.bo.alert.Pagination;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.manager.common.exception.MilogManageException;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class AlertLogService {

    @Resource
    private AlertLogDao alertLogDao;

    @Value(value = "$trace.url")
    private String traceUrl;

    @Resource
    private Gson gson;


    //key=alert id +":"+ip+":"+ruleId, value = alert timestamp in milliseconds
    private Cache<String, ActiveAlertData> activeAlerts = CacheBuilder.newBuilder()
            .maximumSize(500) // 设置缓存最大容量
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();
    private static final String WINDOW_SIZE_DEFAULT = "60";

    public long insertAlertLog(long alertId, String appName, long alertTime, String ip, String alertLevel, String logPath, String content) {
        return alertLogDao.insert(alertId, appName, alertTime, ip, alertLevel, logPath, content);
    }

    public Pagination getAllAlertLogs(String appName, String ip, long startTimeMin, long startTimeMax, int page, int pageSize) {
        page--;
        return alertLogDao.getAllAlertLogs(appName, ip, startTimeMin, startTimeMax, page * pageSize, pageSize);
    }


    @PostConstruct
    public void init() {
        Executors.newScheduledThreadPool(5, ThreadUtil.newNamedThreadFactory("log-updateActiveAlerts", false)).scheduleAtFixedRate(() -> {
            try {
                updateActiveAlerts();
            } catch (Exception e) {

            }
        }, 0, 60, TimeUnit.SECONDS);
    }

    private void updateActiveAlerts() {
        ConcurrentMap<String, ActiveAlertData> activeAlertsMap = activeAlerts.asMap();
        Iterator<Map.Entry<String, ActiveAlertData>> it = activeAlertsMap.entrySet().iterator();

        while (it.hasNext()) {
            Map.Entry<String, ActiveAlertData> current = it.next();
            long duration = System.currentTimeMillis() - current.getValue().lastLogTime;
            if (duration > ((current.getValue().windowSize + 60 * 5) * 1000)) {
                endAlertLog(current.getKey(), current.getValue());
            }
        }
    }

    public void endAlertLog(String key, ActiveAlertData data) {
        activeAlerts.invalidate(key);
        updateLog(data.alertLogId, data.value.get());
    }

    public void processAlertLog(Alert alert, String ip, AlertCondition matchedCondition, String regex, AlertRule alertRule, String traceId) {
        if (alert == null || matchedCondition == null) {
            return;
        }

        String windowSizeString = alert.getArgument(Constants.WINDOW_SIZE);
        if (StringUtils.isEmpty(windowSizeString)) {
            windowSizeString = WINDOW_SIZE_DEFAULT;
        }
        //in seconds
        int windowSize = Integer.parseInt(windowSizeString);

        String key = getLogKey(alert.getId(), ip, alertRule.getId());
        ActiveAlertData data = activeAlerts.getIfPresent(key);
        if (data == null) {
            long alertTime = System.currentTimeMillis();
            String content = (StringUtils.isNotEmpty(alertRule.getName()) ? (alertRule.getName() + " ") : "") + regex + matchedCondition.getOperation() + matchedCondition.getValue();
            long alertLogId = insertAlertLog(alert.getId(), alert.getAppName(), alertTime, ip, matchedCondition.getAlertLevel(), alert.getLogPath(), content);
            data = new ActiveAlertData(alertTime, windowSize, alertLogId, alert.getAppName(), matchedCondition.getAlertLevel(), ip, content, alert.getLogPath(), traceId);
            activeAlerts.put(key, data);
        } else {
            data.lastLogTime = System.currentTimeMillis();
        }
        data.value.incrementAndGet();
    }

    private String getLogKey(long alertId, String ip, long ruleId) {
        return alertId + ":" + ip + ":" + ruleId;
    }

    private void updateLog(long alertLogId, int count) {
        alertLogDao.update(alertLogId, count);
    }

    public boolean isAlertActive(int id) {
        ActiveAlertData data = activeAlerts.getIfPresent(id);
        if (data == null) {
            return false;
        }

        long duration = System.currentTimeMillis() - data.lastLogTime;

        return duration <= (data.windowSize + 60) * 1000;
    }

    public Pagination getActiveAlerts(int page, int rowCount) {
        Pagination result = new Pagination();
        result.setPage(page);
        result.setPageSize(rowCount);

        page--;
        if (page < 0 || rowCount == 0) {
            return result;
        }
        ArrayList<ActiveAlertData> alertLogs = new ArrayList<>(activeAlerts.asMap().values());
        ArrayList<AlertLogBo> alertLogBos = new ArrayList<>();
        long currentTime = System.currentTimeMillis();
        for (ActiveAlertData data : alertLogs) {
            AlertLogBo alertLogBo = new AlertLogBo();
            alertLogBo.setStartTime(data.startTime);
            alertLogBo.setEndTime(-1);
            alertLogBo.setAlertDuration(currentTime - data.startTime);
            alertLogBo.setAppName(data.appName);
            alertLogBo.setAlertLevel(data.alertLevel);
            alertLogBo.setIp(data.ip);
            alertLogBo.setAlertContent(data.alertContent);
            alertLogBo.setLogPath(data.logPath);
            alertLogBo.setActiveAlarm(true);
            alertLogBos.add(alertLogBo);

            if (StringUtils.isNotEmpty(data.traceId)) {
                alertLogBo.setTraceLink(traceUrl + "/trace/" + data.traceId);
            }
        }
        Collections.sort(alertLogBos, (P, Q) -> {
            if (P.getStartTime() > Q.getStartTime()) {
                return 1;
            } else if (P.getStartTime() < Q.getStartTime()) {
                return -1;
            } else {
                return 0;
            }
        });
        int index = page * rowCount;
        int end = index + rowCount;
        if (index < alertLogBos.size()) {
            result.setList(alertLogBos.subList(index, Math.min(alertLogBos.size(), end)));
        }

        result.setTotal(alertLogBos.size());
        return result;
    }

    public RegexMatchRes checkRegexMatch(AlertMsgPattern msgPattern) {
        validateInput(msgPattern);

        Pattern pattern = Pattern.compile(msgPattern.getRule(), Pattern.MULTILINE);

        List<Matcher> matchers = getMatchers(pattern, msgPattern.getMessage());

        List<RegexMatchRes.RegexRes> resList = new ArrayList<>();
        int count = countMatches(matchers, resList);

        return RegexMatchRes.builder()
                .count(count)
                .isMatch(count > 0)
                .resList(resList)
                .build();
    }

    private void validateInput(AlertMsgPattern msgPattern) {
        if (msgPattern == null || StringUtils.isAnyEmpty(msgPattern.getRule(), msgPattern.getMessage())) {
            throw new MilogManageException("参数不能为空");
        }
    }

    private List<Matcher> getMatchers(Pattern pattern, String message) {
        try {
            PatternFilter filter = new PatternFilter(Collections.singletonList(pattern));
            return filter.filter(message);
        } catch (Exception e) {
            log.error("hyper-scan pattern alarm message error, params: {}", pattern.pattern(), e);
            return Collections.singletonList(pattern.matcher(message));
        }
    }

    private int countMatches(List<Matcher> matchers, List<RegexMatchRes.RegexRes> resList) {
        int count = 0;
        for (Matcher matcher : matchers) {
            while (matcher.find()) {
                count++;
                resList.add(RegexMatchRes.RegexRes.builder()
                        .message(matcher.group())
                        .start(matcher.start())
                        .end(matcher.end())
                        .build());
            }
        }
        return count;
    }

    private class ActiveAlertData {
        long startTime;
        long lastLogTime;
        int windowSize;
        long alertLogId;
        AtomicInteger value;
        String appName;
        String alertLevel;
        String ip;
        String alertContent;
        String logPath;
        String traceId;

        public ActiveAlertData(long startTime, int windowSize, long alertLogId, String appName, String alertLevel, String ip, String alertContent, String logPath, String traceId) {
            this.startTime = startTime;
            this.lastLogTime = startTime;
            this.windowSize = windowSize;
            this.alertLogId = alertLogId;
            value = new AtomicInteger();
            value.set(1);
            this.appName = appName;
            this.alertLevel = alertLevel;
            this.ip = ip;
            this.alertContent = alertContent;
            this.logPath = logPath;
            this.traceId = traceId;
        }
    }
}
