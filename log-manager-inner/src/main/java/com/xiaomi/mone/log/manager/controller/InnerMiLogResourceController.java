package com.xiaomi.mone.log.manager.controller;

import com.xiaomi.mone.log.manager.service.InnerMilogMiddlewareConfigService;
import com.xiaomi.youpin.docean.anno.Controller;
import com.xiaomi.youpin.docean.anno.RequestMapping;
import com.xiaomi.youpin.docean.anno.RequestParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.api.model.bo.MiLogResource;
import org.apache.ozhera.log.common.Result;

import java.util.List;
import javax.annotation.Resource;

/**
 * @author: songyutong1
 * @date: 2024/08/22/10:54
 */
@Slf4j
@Controller
public class InnerMiLogResourceController {

    @Resource
    private InnerMilogMiddlewareConfigService innerMilogMiddlewareConfigService;

    @RequestMapping(path = "/log/esIndex/health", method = "POST")
    public Result<List> esIndexHealth(@RequestParam(value = "resource") MiLogResource miLogResource){
        return innerMilogMiddlewareConfigService.esIndexHealth(miLogResource);
    }

}
