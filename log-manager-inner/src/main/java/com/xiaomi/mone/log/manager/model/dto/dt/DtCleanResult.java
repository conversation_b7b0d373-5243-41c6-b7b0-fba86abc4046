package com.xiaomi.mone.log.manager.model.dto.dt;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 清理工场资源结果
 *
 * @author: songyutong1
 * @date: 2024/09/03/18:58
 */
@Data
public class DtCleanResult implements Serializable {
    private List<String> esIndexAddInDb;
    private List<String> esIndexDeleteInDb;
    private List<String> topicAddInDb;
    private List<String> topicDeleteInDb;
    private List<String> esIndexAddInDt;
    private List<String> esIndexDeleteInDt;
    private List<String> topicAddInDt;
    private List<String> topicDeleteInDt;
    private List<String> flinkJobAddInDt;
    private List<String> flinkJobStartInDt;
    private List<String> flinkJobStopInDt;
    private List<String> flinkJobDeleteInDt;
}
