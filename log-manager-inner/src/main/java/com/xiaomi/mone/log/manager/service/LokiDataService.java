package com.xiaomi.mone.log.manager.service;

import com.google.gson.JsonObject;
import com.xiaomi.mone.log.manager.model.dto.MergeLogQuery;
import org.apache.ozhera.log.common.Result;

/**
 * <AUTHOR>
 * @version 1.0
 * @description loki日志查询
 * @date 2024/10/28/11:31
 */
public interface LokiDataService {

    Result<JsonObject> logQuery(MergeLogQuery logQuery);

    Result<JsonObject> logStatistic(MergeLogQuery logQuery);

}
