package com.xiaomi.mone.log.manager.service;


import org.apache.ozhera.log.manager.model.pojo.MilogAppMiddlewareRel;
import org.apache.ozhera.log.manager.model.pojo.MilogMiddlewareConfig;
import org.apache.ozhera.log.manager.service.MqConfigService;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/4/10 17:47
 */
public interface InnerMqConfigService extends MqConfigService {
    /**
     * 创建使用平台资源的 logstore 下的 logtail 对应的 topic
     *
     * @param storeId
     * @param tailId
     * @param milogMiddlewareConfig
     * @return
     */
    MilogAppMiddlewareRel.Config generatePlatformConfig(Long spaceId, Long storeId, Long tailId, MilogMiddlewareConfig milogMiddlewareConfig);

    /**
     * 删除平台创建的 topic
     *
     * @param topicName
     * @param milogMiddlewareConfig
     */
    void deletePlatformTopic(String topicName, MilogMiddlewareConfig milogMiddlewareConfig);

    /**
     * 构造平台资源的 logstore 下的 logtail 对应的topic不进行创建
     *
     * @param spaceId
     * @param storeId
     * @param tailId
     * @param milogMiddlewareConfig
     * @return
     */
    MilogAppMiddlewareRel.Config generatePlatformConfigWithoutCreate(Long spaceId, Long storeId, Long tailId, MilogMiddlewareConfig milogMiddlewareConfig);
}
