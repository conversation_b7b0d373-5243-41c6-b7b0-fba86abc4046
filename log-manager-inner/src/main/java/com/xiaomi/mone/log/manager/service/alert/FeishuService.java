package com.xiaomi.mone.log.manager.service.alert;

import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.xiaomi.youpin.docean.Ioc;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.common.StringUtils;
import com.xiaomi.youpin.feishu.FeiShu;
import com.xiaomi.youpin.feishu.bo.GroupDetail;
import com.xiaomi.youpin.feishu.bo.GroupPageData;
import com.xiaomi.youpin.feishu.bo.Result;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.common.Config;

import javax.annotation.PostConstruct;
import java.util.List;

@Service
@Slf4j
public class FeishuService {

    @Getter
    private FeiShu feiShu;

    private final Gson gson = new Gson();

    @PostConstruct
    public void init() {
        String appId = Config.ins().get("alert_app_id","");
        String appSecret = Config.ins().get("alert_app_secret","");
        feiShu = new FeiShu(appId, appSecret);
    }

    public Result<GroupPageData> getFeishuGroups() {
        return feiShu.getGroupList();
    }

    public Result<GroupPageData> getFeishuGroups(String pageSize, String pageToken) {
        return feiShu.getGroupListByPage(pageSize, pageToken);
    }

    public Result<GroupPageData> getAllFeiShuGroups() {
        List<GroupDetail> groups = Lists.newArrayList();
        com.xiaomi.youpin.feishu.bo.Result<GroupPageData> feiShuGroups = getFeishuGroups("", "");
        groups.addAll(feiShuGroups.getData().getGroups());
        while (feiShuGroups.getData().isHas_more()) {
            feiShuGroups = getFeishuGroups("", feiShuGroups.getData().getPage_token());
            groups.addAll(feiShuGroups.getData().getGroups());
        }
        feiShuGroups.getData().setGroups(groups);
        return feiShuGroups;
    }

    private String feishuGroupsAtTags(String[] receivers) {
        StringBuilder builder = new StringBuilder();
        String atTag = "<at user_id=\"%s\"></at>";
        for (String userId : receivers) {
            builder.append(String.format(atTag, userId));
        }
        return builder.toString();
    }

    public boolean sendFeishu(String content, String[] receivers, String[] feishuGroups) {
        return sendFeishu(content, receivers, feishuGroups, false);
    }

    public boolean sendFeishu(String content, String[] receivers, String[] feishuGroups, boolean sendCard) {
        boolean isTrue = false;
        if (StringUtils.isEmpty(content)) {
            return isTrue;
        }

        try {
            if (receivers != null) {
                for (String receiver : receivers) {
                    if (receiver.contains("@xiaomi.com") == false) {
                        receiver = receiver + "@xiaomi.com";
                    }
                    if (sendCard) {
                        isTrue = feiShu.sendCardByEmail(receiver, content);
                    } else {
                        isTrue = feiShu.sendMsgByEmail(receiver, content);
                    }
                }
            }
            if (feishuGroups != null) {
                //content += feishuGroupsAtTags(receivers);
                for (String feishuGroup : feishuGroups) {
                    if (sendCard) {
                        isTrue = feiShu.sendCardByChatId(feishuGroup, content);
                    } else {
                        isTrue = feiShu.sendMsgByChatId(feishuGroup, content);
                    }
                }
            }

        } catch (Exception e) {
            log.error("send feishu alert message error,content:{}", content, e);
        }
        return isTrue;
    }

    public String getUserIdByEmail(String email) {
        return feiShu.getUserIdByEmail(email);
    }
}
