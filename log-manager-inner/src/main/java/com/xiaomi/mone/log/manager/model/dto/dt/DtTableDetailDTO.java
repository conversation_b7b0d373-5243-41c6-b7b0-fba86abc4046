package com.xiaomi.mone.log.manager.model.dto.dt;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 工场表详情
 *
 * @author: songyutong1
 * @date: 2024/09/02/11:36
 */
@Data
public class DtTableDetailDTO implements Serializable {
    private String service;
    private String name;
    private String catalog;
    private String dbName;
    private String owner;
    private Long createTime;
    private String description;
    private String hdfsUri;
    private List<DtFieldDTO> fieldList;
    private List<DtFieldDTO> partitionFieldList;
    private DtPartitionTypeDTO partitionType;
    private Integer preserveTime;
    private String serializationLib;
    private String formatVersion;
    private List<DtIcebergPartitionDTO> icebergPartitionDTOList;
    private String esClusterDomain;
    private String inputFormat;
    private String outputFormat;
}
