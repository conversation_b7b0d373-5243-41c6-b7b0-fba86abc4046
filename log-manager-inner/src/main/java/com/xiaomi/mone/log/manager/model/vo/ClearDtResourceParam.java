package com.xiaomi.mone.log.manager.model.vo;

import com.xiaomi.mone.log.manager.common.utils.DtUtils;
import com.xiaomi.mone.log.manager.enums.DtHeraLogSpaceEnum;
import com.xiaomi.mone.log.manager.model.dto.dt.DtTokenDetail;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;

/**
 * @author: songyutong1
 * @date: 2024/09/02/14:20
 */
@Data
public class ClearDtResourceParam {
    private String machineRoom;
    private List<Integer> stageList;
    private Boolean clearFlag;
    private String token;
    private String flinkJobToken;
    private String flinkCluster;

    public String validate() {
        StringBuilder errInfo = new StringBuilder();
        if (StringUtils.isEmpty(machineRoom)) {
            errInfo.append("region is empty;");
        }
        if (CollectionUtils.isEmpty(stageList)) {
            errInfo.append("stateList need have at least one state;");
        }
        if (ObjectUtils.isEmpty(clearFlag)) {
            errInfo.append("clearFlag is empty;");
        }
        if (StringUtils.isEmpty(token)) {
            errInfo.append("token is empty;");
        }
        if (StringUtils.isEmpty(flinkJobToken)) {
            errInfo.append("flinkJobToken is empty;");
        }
        if (StringUtils.isEmpty(flinkCluster)) {
            errInfo.append("flinkCluster is empty;");
        }

        DtTokenDetail tableTokenDetail = DtUtils.get(DtUtils.URL_TOKEN_DETAIL, token, DtTokenDetail.class);
        Boolean valid = DtHeraLogSpaceEnum.verifyTokenDetail(tableTokenDetail, machineRoom);
        if (!valid) {
            errInfo.append("table token is invalid;");
        }

        DtTokenDetail flinkTokenDetail = DtUtils.get(DtUtils.URL_TOKEN_DETAIL, flinkJobToken, DtTokenDetail.class);
        valid = DtHeraLogSpaceEnum.verifyTokenDetail(flinkTokenDetail, flinkCluster);
        if (!valid) {
            errInfo.append("flink job token is invalid;");
        }

        Collections.sort(stageList);
        return errInfo.toString();
    }
}
