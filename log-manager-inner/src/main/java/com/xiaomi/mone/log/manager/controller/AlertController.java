package com.xiaomi.mone.log.manager.controller;

import com.xiaomi.infra.galaxy.talos.producer.ProducerNotActiveException;
import com.xiaomi.mone.log.manager.common.context.MoneUserContext;
import com.xiaomi.mone.log.manager.model.alert.Alert;
import com.xiaomi.mone.log.manager.model.alert.AlertCondition;
import com.xiaomi.mone.log.manager.model.alert.AlertQuery;
import com.xiaomi.mone.log.manager.model.alert.AlertRule;
import com.xiaomi.mone.log.manager.model.bo.RegexMatchRes;
import com.xiaomi.mone.log.manager.model.bo.alert.*;
import com.xiaomi.mone.log.manager.service.alert.AlertLogService;
import com.xiaomi.mone.log.manager.service.alert.AlertService;
import com.xiaomi.mone.log.manager.service.alert.FeishuService;
import com.xiaomi.mone.log.manager.service.alert.FlinkAlphaService;
import com.xiaomi.mone.log.manager.service.impl.InnerMilogAppMiddlewareRelServiceImpl;
import com.xiaomi.mone.log.manager.user.MoneUser;
import com.xiaomi.youpin.docean.anno.Controller;
import com.xiaomi.youpin.docean.anno.RequestMapping;
import com.xiaomi.youpin.docean.anno.RequestParam;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import com.xiaomi.youpin.feishu.bo.GroupPageData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.manager.common.ErrorCode;
import org.apache.ozhera.log.manager.common.ExceptionCode;
import org.apache.ozhera.log.manager.common.Result;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static com.xiaomi.mone.log.manager.common.ManagerConfig.EXECUTOR_COMMON;
import static com.xiaomi.mone.log.manager.user.MoneUserDetailService.GSON;


@Slf4j
@Controller
public class AlertController {

    private static final String HTTP_PREFIX = "http";

    @Resource
    private AlertService alertService;

    @Resource
    private AlertLogService alertLogService;

    @Resource
    private FeishuService feishuService;

    @Resource
    private FlinkAlphaService flinkAlphaService;

    @Resource
    private InnerMilogAppMiddlewareRelServiceImpl middlewareRelService;

    @Value(value = "$flink_cluster")
    private String flinkCluster;

    @RequestMapping(path = "/alert/create", method = "post")
    public Result<Boolean> createAlert(@RequestParam("param") AlertParam param) {
        // 获取当前用户
        MoneUser currentUser = MoneUserContext.getCurrentUser();

        // 参数校验
        if (isInvalidCallbackUrl(param.getCallbackUrl())) {
            return Result.fail(new ExceptionCode(ErrorCode.FAIL_PARAM.getCode(), "回调地址必须以http开始"));
        }

        try {
            // 创建警报
            Alert alert = createAlertAndSubmitJob(param, currentUser.getUser());
            if (alert == null) {
                return Result.fail(ErrorCode.CREATE_ALERT_FAILURE);
            }

            // 更新警报信息
            updateAlertInfo(alert);

            // 更新Flink规则
            alertService.updateFlinkRules(alert.getId());

            return Result.success(true);
        } catch (Exception e) {
            log.error("Error while creating alert: {}", e.getMessage(), e);
            return Result.fail(new ExceptionCode(ErrorCode.CREATE_ALERT_FAILURE.getCode(), "创建日志告警信息失败，请稍后重试"));
        }
    }

    // 参数校验：回调地址是否有效
    private boolean isInvalidCallbackUrl(String callbackUrl) {
        return StringUtils.isNotEmpty(callbackUrl) && !callbackUrl.startsWith(HTTP_PREFIX);
    }

    // 创建警报并提交Flink作业
    private Alert createAlertAndSubmitJob(AlertParam param, String currentUser) {
        // 创建警报
        Alert alert = alertService.createAlert(param, currentUser);
        if (alert == null) {
            return null;
        }

        // 提交Flink作业
        Long jobId = alertService.submitFlinkJob(alert, param);
        if (jobId == null) {
            // 提交Flink作业失败，回滚创建的警报
            alertService.deleteAlert(alert.getId());
            return null;
        }

        // 设置Flink作业ID
        alert.setJobId(jobId);
        return alert;
    }

    // 更新警报相关信息
    private void updateAlertInfo(Alert alert) {
        alert.setFlinkJobName(flinkAlphaService.buildFlinkJobName(alert.getId()));
        alert.setFlinkCluster(flinkCluster);
        alert.setStatus(AlertStatus.ON.getStatus());
        alertService.updateAlert(alert);
        log.info("Updated alert with jobId: {}", alert.getJobId());
    }

    @RequestMapping(path = "/alert/get", method = "get")
    public Result<Alert> getAlert(@RequestParam("alertId") long alertId) {
        return Result.success(alertService.getAlert(alertId));
    }

    @RequestMapping(path = "/alert/feishu", method = "post")
    public Result<Boolean> sendFeishu(@RequestParam("param") AlertParam param) {
        feishuService.sendFeishu(param.getContent(), splitString(param.getContacts()), splitString(param.getFeishuGroups()), true);
        return Result.success(true);
    }

    @RequestMapping(path = "/alert/feishuGroups", method = "get")
    public com.xiaomi.youpin.feishu.bo.Result<GroupPageData> getFeishuGroups() {
        return feishuService.getAllFeiShuGroups();
    }

    private String[] splitString(String S) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(S)) {
            return null;
        }
        return S.split("[,]");
    }

    @RequestMapping(path = "/alert/getAll")
    public Result<AlertQueryResult> getAllAlert(@RequestParam("param") AlertQuery alertQuery) {
        AlertQueryResult alerts = alertService.getAllAlerts(alertQuery);

        return Result.success(alerts);
    }

    @RequestMapping(path = "/alert/detail", method = "get")
    public Result<AlertBo> getAlertDetail(@RequestParam("alertId") Long alertId) {
        Assert.notNull(alertId, "alertId不能为空");
        return alertService.getAlertDetail(alertId);
    }

    @RequestMapping(path = "/alert/start", method = "get")
    public Result<Boolean> startAlert(@RequestParam("alertId") long alertId) {
        Alert alert = alertService.getAlert(alertId);
        if (alert == null) {
            return Result.fail(ErrorCode.ALERT_NOT_FOUND);
        }
        boolean success = alertService.startAlert(alert);
        if (success) {
            log.info("started success,alertId:{}", alertId);
            alert.setStatus(AlertStatus.ON.getStatus());
            alertService.updateAlert(alert);
            log.info("startAlert alertId:{},status:{}", alertId, alert.getStatus());
            alertService.updateFlinkRules(alertId);
            return Result.success(true);
        } else {
            return Result.fail(ErrorCode.unknownError);
        }
    }

    @RequestMapping(path = "/alert/restartAll", method = "get")
    public Result<Boolean> restartAllAlerts() {
        List<Alert> alerts = alertService.getAllAlerts();
        alertService.restartAllAlerts(alerts);
        return Result.success(true);
    }


    @RequestMapping(path = "/alert/stop", method = "get")
    public Result<Boolean> stopAlert(@RequestParam("alertId") long alertId) {
        Alert alert = alertService.getAlert(alertId);
        if (alert == null) {
            return Result.fail(ErrorCode.ALERT_NOT_FOUND);
        }
        boolean success = alertService.stopAlert(alert);
        if (success) {
            alert.setStatus(AlertStatus.OFF.getStatus());
            alertService.updateAlert(alert);
            return Result.success(true);
        } else {
            return Result.fail(ErrorCode.unknownError);
        }

    }

    @RequestMapping(path = "/alert/remove", method = "get")
    public Result<Boolean> removeAlert(@RequestParam("alertId") long alertId) {
        Alert alert = alertService.getAlert(alertId);
        if (alert == null) {
            return Result.fail(ErrorCode.ALERT_NOT_FOUND);
        }
        removeJonAsync(alert);
        alertService.deleteAlert(alertId);
        return Result.success(true);
    }

    private void removeJonAsync(Alert alert) {
        CompletableFuture.runAsync(() -> {
            try {
                alertService.stopAlert(alert);
            } catch (Exception e) {
                log.error(String.format("stopAlert error,alertId:%s", alert), e);
            }
            try {
                alertService.removeFlinkJob(alert);
            } catch (Exception e) {
                log.error(String.format("removeAlert error,alertId:%s", alert), e);
            }
        }, EXECUTOR_COMMON);
    }

    @RequestMapping(path = "/alert/removeAll", method = "get")
    public Result<Boolean> removeAllAlert() {
        List<Alert> alerts = alertService.getAllAlerts();
        for (Alert alert : alerts) {
            alertService.removeFlinkJob(alert);
            alertService.deleteAlert(alert.getId());
        }
        return Result.success(true);
    }


    @RequestMapping(path = "/alert/update/yarn", method = "get")
    public Result<Boolean> updateYarnQueue() {
        alertService.updateYarnQueue();
        return Result.success(true);
    }

    //String consumerAccessKey, String consumerSecretKey, String consumerServer,
//                                           String consumerTopic
    @RequestMapping(path = "/alert/update/arguments", method = "post")
    public Result<Boolean> updateArguments(@RequestParam("param") AlertParam param) {
        long alertId = param.getAlertId();
        Alert alert = alertService.getAlert(alertId);
        if (alert == null) {
            return Result.fail(ErrorCode.ALERT_NOT_FOUND);
        }

        alertService.updateAlertArguments(alert, param);
        boolean success = alertService.updateFlinkJobArguments(alert, param);
        if (success) {
            return Result.success(true);
        }
        return Result.fail(ErrorCode.unknownError);
    }

    @RequestMapping(path = "/alert/update", method = "post")
    public Result<Boolean> update(@RequestParam("param") AlertUpdateParam param) {
        try {
            MoneUser currentUser = MoneUserContext.getCurrentUser();
            if (currentUser == null) {
                return Result.fail(ErrorCode.ALERT_NOT_FOUND);
            }

            long alertId = param.getAlertId();
            Alert alert = alertService.getAlert(alertId);
            if (alert == null) {
                return Result.fail(ErrorCode.ALERT_NOT_FOUND);
            }

            alertService.updateAlertProperties(alert, param);

            if (CollectionUtils.isNotEmpty(param.getRules())) {
                updateFlinkRules(alertId);
            }

            return Result.success(true);
        } catch (Exception e) {
            log.error("update error", e);
            return Result.fail(ErrorCode.unknownError);
        }
    }

    @RequestMapping(path = "/alert/produce-logs", method = "post")
    public Result<Boolean> produceLogs(@RequestParam("param") AlertLogParam param) {
        return Result.success(alertService.produceLogs(param.getAccessKey(), param.getSecretKey(), param.getTopicName(),
                param.getMqServer(), param.getLogBody(), param.getNumOfLogs()));
    }

    @RequestMapping(path = "/alert/updateFlinkRules", method = "get")
    public void updateFlinkRules(@RequestParam("alertId") long alertId) throws ProducerNotActiveException {
        alertService.updateFlinkRules(alertId);
    }

    @RequestMapping(path = "/alert/createRule", method = "post")
    public Result<Boolean> createRule(@RequestParam("param") AlertRuleParam param) {
        MoneUser currentUser = MoneUserContext.getCurrentUser();
        alertService.createRule(param.getAlertConditionList(), param.getAlertId(), param.getRegex(), currentUser.getUser(), param.getName());
        alertService.updateFlinkRules(param.getAlertId());
        return Result.success(true);
    }

    @RequestMapping(path = "/alert/getAllRules", method = "get")
    public Result<List<AlertRuleBo>> getAllRules(@RequestParam("alertId") long alertId) {
        return Result.success(alertService.getAllRulesBo(alertId));
    }

    @RequestMapping(path = "/alert/getAllConditions", method = "get")
    public Result<List<AlertCondition>> getAllConditions(@RequestParam("alertRuleId") long alertRuleId) {
        return Result.success(alertService.getAllConditions(alertRuleId));
    }

    @RequestMapping(path = "/alert/createAlertCondition", method = "post")
    public Result<Boolean> createAlertCondition(
            @RequestParam("conditionParam") AlertConditionParam conditionParam) {
        MoneUser currentUser = MoneUserContext.getCurrentUser();

        alertService.createAlertCondition(conditionParam.getAlertRuleId(), conditionParam, currentUser.getUser());
        return Result.success(true);
    }

    @RequestMapping(path = "/alert/deleteRule", method = "get")
    public Result<Boolean> deleteRule(@RequestParam("alertRuleId") long alertRuleId) throws ProducerNotActiveException {

        AlertRule alertRule = alertService.getRule(alertRuleId);
        if (alertRule == null) {
            return Result.success(true);
        }

        alertService.deleteRule(alertRuleId);
        alertService.updateFlinkRules(alertRule.getAlertId());
        return Result.success(true);
    }

    @RequestMapping(path = "/alert/deleteAlertCondition", method = "get")
    public Result<Boolean> deleteAlertCondition(@RequestParam("alertConditionId") long alertConditionId) {
        alertService.deleteAlertCondition(alertConditionId);
        return Result.success(true);
    }

    @RequestMapping(path = "/alert/alertLogs", method = "get")
    public Result<Pagination> getAllAlertLogs(@RequestParam("appName") String appName,
                                              @RequestParam("ip") String ip,
                                              @RequestParam("startTimeMin") Long startTimeMin,
                                              @RequestParam("startTimeMax") Long startTimeMax,
                                              @RequestParam("page") Integer page,
                                              @RequestParam("pageSize") Integer pageSize) {
        //String appName, String ip, long startTimeMin, long startTimeMax, int offset, int rowCount

        return Result.success(alertLogService.getAllAlertLogs(appName, ip, startTimeMin, startTimeMax, page, pageSize));
    }

    @RequestMapping(path = "/alert/active-alerts", method = "get")
    public Result<Pagination> getActiveAlerts(@RequestParam("page") int page,
                                              @RequestParam("pageSize") int pageSize) {
        return Result.success(alertLogService.getActiveAlerts(page, pageSize));
    }

    /**
     * 正则表达式校验
     *
     * @param alertMsgPattern
     * @return
     */
    @RequestMapping(path = "/alert/check/message/match")
    public Result<RegexMatchRes> checkRegexMatch(AlertMsgPattern alertMsgPattern) {
        return Result.success(alertLogService.checkRegexMatch(alertMsgPattern));
    }

    /**
     * 日志告警回调
     *
     * @param callBackParam
     * @return
     */
    @RequestMapping(path = "/alert/receive/alert/callback", method = "POST")
    public Result<String> receiveAlert(AlertCallBackParam callBackParam) {
        log.info("receive receiveAlert,param:{}", GSON.toJson(callBackParam));
        return Result.success("success");
    }

    /**
     * 查询日志告警中的操作符列表
     *
     * @return
     */
    @RequestMapping(path = "/alert/match/operator", method = "GET")
    public Result<List<AlertMatchOperator>> getAlertMatchOperators(@RequestParam("type") String type) {
        if (StringUtils.isBlank(type)) {
            return Result.fail(new ExceptionCode(ErrorCode.FAIL_PARAM.getCode(), "参数不能为空"));
        }
        return Result.success(alertService.getAlertMatchOperators(type));
    }

    /**
     * 根据选择的tail查询解析脚本的类别
     *
     * @param param
     * @return
     */
    @RequestMapping(path = "/alert/tail/rel/ids", method = "POST")
    public Result<List<AlarmTailRelationship>> alarmWithTailIds(AlarmWithTailReq param) {
        if (null == param || CollectionUtils.isEmpty(param.getTailIds())) {
            return Result.success(new ArrayList<>());
        }
        return Result.success(alertService.alarmWithTailIds(param.getTailIds(), param.getType()));
    }
}

