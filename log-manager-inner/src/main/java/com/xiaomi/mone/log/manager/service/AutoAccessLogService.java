package com.xiaomi.mone.log.manager.service;

import com.xiaomi.mone.log.manager.model.po.AutoAccessLogParam;
import com.xiaomi.mone.log.manager.model.po.LogEnabledParam;
import com.xiaomi.mone.log.manager.model.vo.LogEnabledTreeVo;
import com.xiaomi.mone.log.manager.model.vo.LogEnabledVo;

import java.util.List;

/**
 *
 * @description
 * @version 1.0
 * <AUTHOR>
 * @date 2024/7/8 16:13
 *
 */
public interface AutoAccessLogService {
    LogEnabledVo queryLogEnabled(LogEnabledParam logEnabledParam);

    /**
     * 邮箱前缀
     * @param userName
     * @return
     */
    List<LogEnabledTreeVo> queryLogBindTree(String spaceName, String userName);

    String autoAccessToLog(AutoAccessLogParam logParam);
}
