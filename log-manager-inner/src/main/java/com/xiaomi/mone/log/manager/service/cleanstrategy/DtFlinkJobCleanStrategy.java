package com.xiaomi.mone.log.manager.service.cleanstrategy;

import com.xiaomi.mone.enums.InnerMiddlewareEnum;
import com.xiaomi.mone.log.manager.dao.InnerLogMiddlewareConfigDao;
import com.xiaomi.mone.log.manager.dao.alert.AlertDao;
import com.xiaomi.mone.log.manager.model.alert.Alert;
import com.xiaomi.mone.log.manager.model.alert.AlertJobDetail;
import com.xiaomi.mone.log.manager.model.bo.alert.AlertParam;
import com.xiaomi.mone.log.manager.model.bo.alert.AlertStatus;
import com.xiaomi.mone.log.manager.model.dto.dt.DtJobDTO;
import com.xiaomi.mone.log.manager.model.dto.dt.DtJobListResponse;
import com.xiaomi.mone.log.manager.model.vo.ClearDtResourceParam;
import com.xiaomi.mone.log.manager.service.alert.AlertService;
import com.xiaomi.mone.log.manager.service.alert.FlinkAlphaService;
import com.xiaomi.mone.log.manager.service.remoting.DtRemoteService;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.manager.common.exception.MilogManageException;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;
import org.apache.ozhera.log.manager.model.pojo.MilogMiddlewareConfig;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import static org.apache.ozhera.log.common.Constant.SYMBOL_COMMA;
import static org.apache.ozhera.log.common.Constant.TAILID_KEY;

/**
 * 清理工场flink作业资源和数据库不一致
 *
 * @author: songyutong1
 * @date: 2024/09/13/16:51
 */
@Service
@Slf4j
public class DtFlinkJobCleanStrategy extends AbstractCleanStrategy {

    @Resource
    private InnerLogMiddlewareConfigDao innerLogMiddlewareConfigDao;

    @Resource
    private AlertDao alertDao;

    @Resource
    private DtRemoteService dtRemoteService;

    @Resource
    private AlertService alertService;

    @Resource
    private FlinkAlphaService flinkAlphaService;

    @Resource
    private MilogLogTailDao milogLogTailDao;

    @Value(value = "$server.type")
    private String serverType;

    @Override
    public void clean(ClearDtResourceParam param, String uuid) {
        log.info("uuid:{}, start clean up flinkJob diff in dt and db", uuid);
        MilogMiddlewareConfig milogMiddlewareConfig = innerLogMiddlewareConfigDao.queryPlatformTalosConfigByRegion(param.getMachineRoom());
        if (ObjectUtils.isEmpty(milogMiddlewareConfig)) {
            log.info("uuid:{}, clean up flinkJob diff in dt and db failed, talosConfig not exist, please check the machineRoom:{}", uuid, param.getMachineRoom());
            throw new MilogManageException("clean up flinkJob diff in dt and db failed, talosConfig not exist, please check the machineRoom:" + param.getMachineRoom());
        }

        // 对比flink作业，修复不一致的flinkJob
        createFlinkJobInDt(param, uuid);
        updateFlinkJobInDb(param, uuid);
        deleteFlinkJobInDt(param, uuid);
    }

    public void createFlinkJobInDt(ClearDtResourceParam param, String uuid) {
        // 获取数据库中的flinkJob
        List<Alert> alertList = alertDao.queryAlertByIdAndFlinkClusterFuzzy(null, param.getFlinkCluster());
        // 获取工厂中的flinkJob
        DtJobListResponse response = dtRemoteService.queryJobList(1L, 300L, null, false, "", "FLINKJAR_STREAMING", param.getFlinkJobToken());
        List<DtJobDTO> dtJobs = new ArrayList<>(response.getJobList());
        while (dtJobs.size() != response.getPaging().getTotal()) {
            response = dtRemoteService.queryJobList(response.getPaging().getPage() + 1L, 300L, null, false, "", "FLINKJAR_STREAMING", param.getFlinkJobToken());
            dtJobs.addAll(response.getJobList());
        }

        List<String> flinkJobAddInDt = new ArrayList<>();
        HashSet<Long> dtSet = dtJobs.stream().map(DtJobDTO::getJobId).collect(Collectors.toCollection(HashSet::new));
        alertList.forEach(alert -> {
            if (dtSet.contains(alert.getJobId())) {
                return;
            }
            flinkJobAddInDt.add(flinkAlphaService.buildFlinkJobName(alert.getId()));
            if (Boolean.FALSE.equals(param.getClearFlag())) {
                return;
            }
            String tailIdStr = alert.getArgument(TAILID_KEY);
            List<Long> tailId = new ArrayList<>(Arrays.stream(tailIdStr.split(SYMBOL_COMMA)).map(Long::valueOf).toList());
            if (CollectionUtils.isEmpty(tailId)) {
                log.info("uuid:{}, alert:{} has no binding tail", uuid, alert);
            }
            // 过滤不存在的tail
            String finalTailIdStr = getFinalTailList(tailId);
            // 更新数据库
            if (!StringUtils.equals(tailIdStr, finalTailIdStr)) {
                alert.addArgument(TAILID_KEY, finalTailIdStr);
                alertDao.update(alert);
            }
            AlertParam alertParam = new AlertParam();
            alertParam.setMqType(InnerMiddlewareEnum.TALOS.getName());
            alertParam.setTailIds(tailId);
            alertParam.setRegionEn(param.getMachineRoom());
            try {
                Long jobId = alertService.submitFlinkJob(alert, alertParam);
                AlertJobDetail jobDetail = dtRemoteService.queryJobDetail(jobId, param.getFlinkJobToken());
                if (ObjectUtils.isEmpty(jobId) || ObjectUtils.isEmpty(jobDetail)) {
                    log.error("uuid:{}, create flink job failed, alert:{}", uuid, alert.getFlinkJobName());
                    throw new MilogManageException("create flink job failed, alert:" + alert.getFlinkJobName());
                }
                alert.setJobId(jobId);
                alert.setFlinkJobName(jobDetail.getJobName());
                alert.setFlinkCluster(jobDetail.getFlinkCluster());
                alert.setStatus(AlertStatus.ON.getStatus());
                alertService.updateAlert(alert);
            } catch (Exception e) {
                log.error("uuid:{}, create flink job failed, alert:{}, e:{}", uuid, alert.getFlinkJobName(), e.getMessage(), e);
                throw new MilogManageException(e);
            }
        });
        log.info("uuid:{}, create flink job into dt:{}", uuid, flinkJobAddInDt);
    }

    public void updateFlinkJobInDb(ClearDtResourceParam param, String uuid) {
        // 获取数据库中的flinkJob
        List<Alert> alertList = alertDao.queryAlertByIdAndFlinkClusterFuzzy(null, param.getFlinkCluster());

        List<String> startList = new ArrayList<>();
        List<String> stopList = new ArrayList<>();
        alertList.forEach(alert -> {
            AlertJobDetail alertJobDetail = dtRemoteService.queryJobDetail(alert.getJobId(), param.getFlinkJobToken());

            // 重启flink作业
            if (alert.getStatus() == AlertStatus.ON.getStatus() && !"STARTED".equals(alertJobDetail.getJobStatus())) {
                String tailIdStr = alert.getArgument(TAILID_KEY);
                List<Long> tailId = new ArrayList<>(Arrays.stream(tailIdStr.split(SYMBOL_COMMA)).map(Long::valueOf).toList());
                startList.add(alert.getFlinkJobName());
                if (Boolean.FALSE.equals(param.getClearFlag())) {
                    return;
                }
                if (CollectionUtils.isEmpty(tailId)) {
                    log.info("uuid:{}, alert:{} has no binding tail", uuid, alert);
                }
                // 过滤不存在的tail重新构建flink作业
                String finalTailIdStr = getFinalTailList(tailId);
                if (!StringUtils.equals(tailIdStr, finalTailIdStr)) {
                    alert.addArgument(TAILID_KEY, finalTailIdStr);
                }
                // 更新数据库和flink作业并返回
                if (!StringUtils.equals(tailIdStr, finalTailIdStr)) {
                    alert.addArgument(TAILID_KEY, finalTailIdStr);
                    alertDao.update(alert);
                    AlertParam alertParam = new AlertParam();
                    alertParam.setAlertId(alert.getId());
                    alertParam.setTailIds(tailId);
                    alertParam.setMqType(InnerMiddlewareEnum.TALOS.getName());
                    alertParam.setRegionEn(param.getMachineRoom());
                    try {
                        boolean success = alertService.updateFlinkJobArguments(alert, alertParam);
                        if (!success) {
                            log.error("uuid:{}, update flink job failed, alert:{}", uuid, alert.getFlinkJobName());
                            throw new MilogManageException("update flink job failed, alert:" + alert.getFlinkJobName());
                        }
                    } catch (Exception e) {
                        log.error("uuid:{}, update flink job failed, alert:{}, e:{}", uuid, alert.getFlinkJobName(), e.getMessage(), e);
                        throw new MilogManageException(e);
                    }
                    return;
                }
                // tail一致的情况重新启动flink作业
                try {
                    Integer count = dtRemoteService.startJob(alert.getJobId(), param.getFlinkJobToken());
                    if (count != 0) {
                        log.error("uuid:{}, start flink job failed, alert:{}", uuid, alert.getFlinkJobName());
                        throw new MilogManageException("start flink job failed, alert:" + alert.getFlinkJobName());
                    }
                } catch (Exception e) {
                    log.error("uuid:{}, start flink job failed, alert:{}, error:{}", uuid, alert.getFlinkJobName(), e.getMessage(), e);
                    throw new MilogManageException("start flink job failed, alert:" + alert.getFlinkJobName());
                }
                return;
            }

            // 停止flink作业
            if (alert.getStatus() == AlertStatus.OFF.getStatus() && !"STOPPED".equals(alertJobDetail.getJobStatus())) {
                stopList.add(alert.getFlinkJobName());
                if (Boolean.FALSE.equals(param.getClearFlag())) {
                    return;
                }
                try {
                    Boolean success = dtRemoteService.stopJob(alert.getJobId(), param.getFlinkJobToken());
                    if (!success) {
                        log.error("uuid:{}, stop flink job failed, alert:{}", uuid, alert.getFlinkJobName());
                        throw new MilogManageException("stop flink job failed, alert:" + alert.getFlinkJobName());
                    }
                } catch (Exception e) {
                    log.error("uuid:{}, stop flink job failed, alert:{}, error:{}", uuid, alert.getFlinkJobName(), e.getMessage(), e);
                    throw new MilogManageException("stop flink job failed, alert:" + alert.getFlinkJobName());
                }
            }
        });
        log.info("uuid:{}, flink job start in dt:{}", uuid, startList);
        log.info("uuid:{}, flink job stop in dt:{}", uuid, stopList);
    }

    public void deleteFlinkJobInDt(ClearDtResourceParam param, String uuid) {
        // 获取数据库中的flinkJob
        List<Alert> alertList = alertDao.queryAlertByIdAndFlinkClusterFuzzy(null, param.getFlinkCluster());
        // 获取工厂中的flinkJob
        DtJobListResponse response = dtRemoteService.queryJobList(1L, 300L, null, false, "", "FLINKJAR_STREAMING", param.getFlinkJobToken());
        List<DtJobDTO> dtJobs = new ArrayList<>(response.getJobList());
        while (dtJobs.size() != response.getPaging().getTotal()) {
            response = dtRemoteService.queryJobList(response.getPaging().getPage() + 1L, 300L, null, false, "", "FLINKJAR_STREAMING", param.getFlinkJobToken());
            dtJobs.addAll(response.getJobList());
        }

        List<String> flinkJobDeleteInDt = new ArrayList<>();
        HashSet<Long> alertSet = alertList.stream().map(Alert::getJobId).collect(Collectors.toCollection(HashSet::new));
        dtJobs.forEach(dtJob -> {
            String prefix = buildJobPrefix();
            if (alertSet.contains(dtJob.getJobId()) || !dtJob.getJobName().contains(prefix)) {
                return;
            }
            flinkJobDeleteInDt.add(dtJob.getJobName());
            if (Boolean.FALSE.equals(param.getClearFlag())) {
                return;
            }
            try {
                dtRemoteService.deleteJobForce(dtJob.getJobId(), param.getFlinkJobToken());
            } catch (Exception e) {
                log.error("uuid:{}, delete flink job from dt failed, job:{}, error:{}", uuid, dtJob.getJobName(), e.getMessage(), e);
            }
        });
        log.info("uuid:{}, delete flink job from dt:{}", uuid, flinkJobDeleteInDt);
    }

    private String buildJobPrefix() {
        return String.format("%s_%s", "Mione", serverType);
    }

    private String getFinalTailList(List<Long> tailId) {
        Iterator<Long> iterator = tailId.iterator();
        while (iterator.hasNext()) {
            Long id = iterator.next();
            MilogLogTailDo milogLogTailDo = milogLogTailDao.queryById(id);
            if (ObjectUtils.isEmpty(milogLogTailDo)) {
                iterator.remove();
            }
        }
        return tailId.stream().map(String::valueOf).collect(Collectors.joining(SYMBOL_COMMA));
    }

}
