package com.xiaomi.mone.log.manager.service;

import com.xiaomi.youpin.docean.anno.Service;
import org.apache.ozhera.log.api.model.meta.LogCollectMeta;

import javax.annotation.Resource;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.INNER_LOG_AGENT_SERVICE;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/6/7 17:31
 */
@Service
public class DockerAgentConfigProcessor implements AgentConfigProcessor {

    @Resource(name = INNER_LOG_AGENT_SERVICE)
    private InnerLogAgentService milogAgentService;

    @Override
    public LogCollectMeta queryLogCollectMeta(String agentIp) {
        return milogAgentService.queryMilogAgentConfig("", agentIp, "");
    }

}
