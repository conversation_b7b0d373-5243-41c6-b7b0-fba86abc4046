package com.xiaomi.mone.log.manager.service.alert;

import com.xiaomi.mone.log.manager.model.alert.Alert;
import com.xiaomi.mone.log.manager.model.alert.AlertCondition;
import com.xiaomi.mone.log.manager.model.alert.AlertRule;
import com.xiaomi.mone.log.manager.model.bo.LogServiceMeta;
import com.xiaomi.mone.log.manager.model.bo.alert.AlertCallBackParam;
import com.xiaomi.mone.log.manager.model.bo.alert.CallBackArgument;
import com.xiaomi.mone.log.manager.model.dto.TraceAppInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.api.model.bo.AlertInfo;
import org.jetbrains.annotations.NotNull;

import static com.xiaomi.mone.log.manager.service.alert.Constants.WINDOW_SIZE;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/5/20 15:16
 */
@Slf4j
public abstract class SendAlertCardService {

    public void sendAlertCard(AlertInfo alertInfo, Alert alert, AlertCondition matchedCondition,
                              AlertRule alertRule, String traceId, String timeStamp, String logParam,
                              String message, String logPath, String deploySpace,
                              TraceAppInfoDTO traceInfo, Long tailId) {
        String value = alertInfo.getInfo("count");
        int count = StringUtils.isEmpty(value) ? -1 : Integer.parseInt(value);
        String ruleName = getAlertRuleName(alert, alertRule);
        String ruleRegex = getAlertRule(alert, alertRule, tailId);

        RemoteSendAlertLink.Meta meta = getMeta(count, alert, alertRule, alertRule.getId(), traceId, getHeraUrl(), matchedCondition, getServerType(), ruleRegex.replaceAll("\\\\", "\\\\\\\\\\\\\\\\").replaceAll("\"", "\\\\\""), ruleName, timeStamp, logParam, message);
        String[] receivers = getReceiver(alert);
        String[] feishuGroups = getFeishuGroups(alert);
        String[] notifyGroups = getNotifyGroups(alert);

        String ip = alertInfo.getInfo("ip");
        CallBackArgument callBackArgument = getCallBackArgument(alert, matchedCondition, traceId, message, logPath, meta, ruleName, ruleRegex, count, ip, deploySpace, traceInfo);

        AlertCallBackParam callBackParam = getCallBackParam(callBackArgument);
        if (ArrayUtils.isEmpty(receivers) && ArrayUtils.isEmpty(feishuGroups) && ArrayUtils.isEmpty(notifyGroups)) {
            log.warn("empty receivers、feishu groups and notify groups, alert id:{}", alertInfo.getAlertId());
            return;
        }

        sendMessage(alert, matchedCondition, meta, receivers, feishuGroups, notifyGroups, callBackParam);

        // feishuService.sendFeishu(content, splitString(alert.getContacts()),
        // splitString(alert.getFeishuGroups()), true);

    }

    @NotNull
    private static CallBackArgument getCallBackArgument(Alert alert, AlertCondition matchedCondition, String traceId, String message, String logPath, RemoteSendAlertLink.Meta meta, String ruleName, String ruleRegex, int count, String ip, String deploySpace, TraceAppInfoDTO traceInfo) {
        CallBackArgument callBackArgument = new CallBackArgument();
        callBackArgument.setAlert(alert);
        callBackArgument.setMeta(meta);
        callBackArgument.setTraceId(traceId);
        callBackArgument.setMessage(message);
        callBackArgument.setRuleName(ruleName);
        callBackArgument.setRuleRegex(ruleRegex);
        callBackArgument.setLogPath(logPath);

        callBackArgument.setLevel(matchedCondition.getAlertLevel());
        callBackArgument.setAlarmValue(count);
        String windowSizeValue = alert.getArguments().get(WINDOW_SIZE);
        String alarmDesc = getAlarmDesc(matchedCondition, windowSizeValue, ruleRegex);
        callBackArgument.setAlarmDesc(alarmDesc);

        LogServiceMeta logServiceMeta = new LogServiceMeta();
        if (ObjectUtils.isNotEmpty(traceInfo)) {
            logServiceMeta.setIamTreeId(traceInfo.getIamTreeId());
        }
        logServiceMeta.setDeploySpace(deploySpace);
        logServiceMeta.setLogIp(ip);
        callBackArgument.setServiceInfo(logServiceMeta);
        return callBackArgument;
    }

    private static String getAlarmDesc(AlertCondition matchedCondition, String windowSizeValue, String ruleRegex) {
        int windowSize;
        if (windowSizeValue != null) {
            windowSize = Integer.parseInt(windowSizeValue);
        } else {
            windowSize = 60;
        }
        String windowSizeString = (windowSize / 60) + "分钟内";
        // {{.FirstEvent.Meta.window_size}} **{{.FirstEvent.Meta.rule_regex}}** 匹配次数 **{{.FirstEvent.Meta.operation}} {{.FirstEvent.Meta.threshold}}*
        String alarmDesc = String.format("%s%s 匹配次数 %s%s", windowSizeString, ruleRegex, matchedCondition.getOperation(), matchedCondition.getValue());
        return alarmDesc;
    }

    protected abstract String getServerType();

    protected abstract String getHeraUrl();

    protected abstract void sendMessage(Alert alert, AlertCondition matchedCondition, RemoteSendAlertLink.Meta meta, String[] receivers, String[] feishuGroups, String[] notifyGroups, AlertCallBackParam callBackParam);

    protected abstract AlertCallBackParam getCallBackParam(CallBackArgument callBackArgument);

    protected abstract String[] getNotifyGroups(Alert alert);

    protected abstract String[] getFeishuGroups(Alert alert);

    protected abstract String[] getReceiver(Alert alert);

    protected abstract String getAlertRule(Alert alert, AlertRule alertRule, Long tailId);

    protected abstract String getAlertRuleName(Alert alert, AlertRule alertRule);

    protected abstract RemoteSendAlertLink.Meta getMeta(int count, Alert alert, AlertRule alertRule, long ruleId, String traceId,
                                                        String heraUrl, AlertCondition matchedCondition, String env,
                                                        String ruleRegex, String ruleName, String timeStamp,
                                                        String logParam, String message);

}
