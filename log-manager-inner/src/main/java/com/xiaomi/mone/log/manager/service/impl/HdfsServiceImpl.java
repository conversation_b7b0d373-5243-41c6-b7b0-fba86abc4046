package com.xiaomi.mone.log.manager.service.impl;

import com.alibaba.fastjson.JSON;
import com.xiaomi.mone.log.manager.dao.HdfsDao;
import com.xiaomi.mone.log.manager.model.dto.HdfsDTO;
import com.xiaomi.mone.log.manager.service.HdfsService;
import com.xiaomi.youpin.docean.anno.Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.common.Result;
import org.apache.ozhera.log.exception.CommonError;

import javax.annotation.Resource;

@Slf4j
@Service
public class HdfsServiceImpl implements HdfsService {

    @Resource
    HdfsDao hdfsDao;


    @Override
    public Result<String> queryHive(String date, String traceId)  {

        HdfsDTO result = hdfsDao.queryHiveData(date,traceId);
        return new Result<String>(CommonError.Success.getCode(), CommonError.Success.getMessage(), JSON.toJSONString(result));
    }

}



















