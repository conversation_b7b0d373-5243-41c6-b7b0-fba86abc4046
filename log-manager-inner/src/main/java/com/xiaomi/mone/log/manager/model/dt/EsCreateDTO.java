package com.xiaomi.mone.log.manager.model.dt;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class EsCreateDTO {
    @JsonProperty("autoMapping")
    private Boolean autoMapping = null;
    @JsonProperty("name")
    private String name = null;
    @JsonProperty("catalog")
    private String catalog = null;
    @JsonProperty("dataSize")
    private Integer dataSize = null;
    @JsonProperty("dbName")
    private String dbName = null;
    @JsonProperty("partitionUnit")
    private String partitionUnit = null;
    @JsonProperty("owner")
    private String owner = null;
    @JsonProperty("preserveTime")
    private Integer preserveTime = null;
    @JsonProperty("description")
    private String description = null;
    @JsonProperty("partitionType")
    private Integer partitionType = null;
    @JsonProperty("columnDtos")
    private List<ColumnDTO2> columnDtos = null;
    @JsonProperty("storageType")
    private String storageType = null;

    public EsCreateDTO() {
    }

    public EsCreateDTO autoMapping(Boolean autoMapping) {
        this.autoMapping = autoMapping;
        return this;
    }

    @ApiModelProperty(
            example = "true",
            value = "是否开启自动映射"
    )
    public Boolean isAutoMapping() {
        return this.autoMapping;
    }

    public void setAutoMapping(Boolean autoMapping) {
        this.autoMapping = autoMapping;
    }

    public EsCreateDTO name(String name) {
        this.name = name;
        return this;
    }

    @ApiModelProperty(
            example = "table_name_en",
            value = "表英文名,为小写英文字母数字或下划线"
    )
    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public EsCreateDTO catalog(String catalog) {
        this.catalog = catalog;
        return this;
    }

    @ApiModelProperty(
            example = "catalog",
            value = "catalog"
    )
    public String getCatalog() {
        return this.catalog;
    }

    public void setCatalog(String catalog) {
        this.catalog = catalog;
    }

    public EsCreateDTO dataSize(Integer dataSize) {
        this.dataSize = dataSize;
        return this;
    }

    @ApiModelProperty("索引数据日增量，单位为GB")
    public Integer getDataSize() {
        return this.dataSize;
    }

    public void setDataSize(Integer dataSize) {
        this.dataSize = dataSize;
    }

    public EsCreateDTO dbName(String dbName) {
        this.dbName = dbName;
        return this;
    }

    @ApiModelProperty(
            example = "dbName",
            value = "库名"
    )
    public String getDbName() {
        return this.dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public EsCreateDTO partitionUnit(String partitionUnit) {
        this.partitionUnit = partitionUnit;
        return this;
    }

    @ApiModelProperty("分区规则")
    public String getPartitionUnit() {
        return this.partitionUnit;
    }

    public void setPartitionUnit(String partitionUnit) {
        this.partitionUnit = partitionUnit;
    }

    public EsCreateDTO owner(String owner) {
        this.owner = owner;
        return this;
    }

    @ApiModelProperty(
            example = "zhangsan",
            value = "所属者"
    )
    public String getOwner() {
        return this.owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public EsCreateDTO preserveTime(Integer preserveTime) {
        this.preserveTime = preserveTime;
        return this;
    }

    @ApiModelProperty("生命周期")
    public Integer getPreserveTime() {
        return this.preserveTime;
    }

    public void setPreserveTime(Integer preserveTime) {
        this.preserveTime = preserveTime;
    }

    public EsCreateDTO description(String description) {
        this.description = description;
        return this;
    }

    @ApiModelProperty(
            example = "这是一张表的描述信息",
            value = "表描述"
    )
    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public EsCreateDTO partitionType(Integer partitionType) {
        this.partitionType = partitionType;
        return this;
    }

    @ApiModelProperty(
            example = "1",
            value = "分区类型"
    )
    public Integer getPartitionType() {
        return this.partitionType;
    }

    public void setPartitionType(Integer partitionType) {
        this.partitionType = partitionType;
    }

    public EsCreateDTO columnDtos(List<ColumnDTO2> columnDtos) {
        this.columnDtos = columnDtos;
        return this;
    }

    public EsCreateDTO addColumnDtosItem(ColumnDTO2 columnDtosItem) {
        if (this.columnDtos == null) {
            this.columnDtos = new ArrayList();
        }

        this.columnDtos.add(columnDtosItem);
        return this;
    }

    @ApiModelProperty("表字段信息")
    public List<ColumnDTO2> getColumnDtos() {
        return this.columnDtos;
    }

    public void setColumnDtos(List<ColumnDTO2> columnDtos) {
        this.columnDtos = columnDtos;
    }

    public EsCreateDTO storageType(String storageType) {
        this.storageType = storageType;
        return this;
    }

    @ApiModelProperty("存储类型")
    public String getStorageType() {
        return this.storageType;
    }

    public void setStorageType(String storageType) {
        this.storageType = storageType;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } else if (o != null && this.getClass() == o.getClass()) {
            EsCreateDTO esCreateDTO = (EsCreateDTO)o;
            return Objects.equals(this.autoMapping, esCreateDTO.autoMapping) && Objects.equals(this.name, esCreateDTO.name) && Objects.equals(this.catalog, esCreateDTO.catalog) && Objects.equals(this.dataSize, esCreateDTO.dataSize) && Objects.equals(this.dbName, esCreateDTO.dbName) && Objects.equals(this.partitionUnit, esCreateDTO.partitionUnit) && Objects.equals(this.owner, esCreateDTO.owner) && Objects.equals(this.preserveTime, esCreateDTO.preserveTime) && Objects.equals(this.description, esCreateDTO.description) && Objects.equals(this.partitionType, esCreateDTO.partitionType) && Objects.equals(this.columnDtos, esCreateDTO.columnDtos) && Objects.equals(this.storageType, esCreateDTO.storageType);
        } else {
            return false;
        }
    }

    public int hashCode() {
        return Objects.hash(new Object[]{this.autoMapping, this.name, this.catalog, this.dataSize, this.dbName, this.partitionUnit, this.owner, this.preserveTime, this.description, this.partitionType, this.columnDtos, this.storageType});
    }

    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class EsCreateDTO {\n");
        sb.append("    autoMapping: ").append(this.toIndentedString(this.autoMapping)).append("\n");
        sb.append("    name: ").append(this.toIndentedString(this.name)).append("\n");
        sb.append("    catalog: ").append(this.toIndentedString(this.catalog)).append("\n");
        sb.append("    dataSize: ").append(this.toIndentedString(this.dataSize)).append("\n");
        sb.append("    dbName: ").append(this.toIndentedString(this.dbName)).append("\n");
        sb.append("    partitionUnit: ").append(this.toIndentedString(this.partitionUnit)).append("\n");
        sb.append("    owner: ").append(this.toIndentedString(this.owner)).append("\n");
        sb.append("    preserveTime: ").append(this.toIndentedString(this.preserveTime)).append("\n");
        sb.append("    description: ").append(this.toIndentedString(this.description)).append("\n");
        sb.append("    partitionType: ").append(this.toIndentedString(this.partitionType)).append("\n");
        sb.append("    columnDtos: ").append(this.toIndentedString(this.columnDtos)).append("\n");
        sb.append("    storageType: ").append(this.toIndentedString(this.storageType)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    private String toIndentedString(Object o) {
        return o == null ? "null" : o.toString().replace("\n", "\n    ");
    }
}
