package com.xiaomi.mone.log.manager.service.impl;

import com.google.gson.reflect.TypeToken;
import com.xiaomi.data.push.client.HttpClientV6;
import com.xiaomi.mone.log.manager.common.utils.HeraTraceUtils;
import com.xiaomi.mone.log.manager.model.dto.DeploySystemAppsDTO;
import com.xiaomi.mone.log.manager.model.dto.DeploySystemAppsDTO.DeploySystemAppData;
import com.xiaomi.mone.log.manager.model.dto.DeploySystemAppsDTO.DeploySystemCluster;
import com.xiaomi.mone.log.manager.model.dto.TraceAppInfoDTO;
import com.xiaomi.mone.log.manager.service.DeploySystemLogService;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.common.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.http.client.utils.URIBuilder;

import java.net.URI;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import static org.apache.ozhera.log.common.Constant.GSON;

/**
 * 融合云部署系统应用日志服务
 */
@Slf4j
@Service
public class DeploySystemLogServiceImpl implements DeploySystemLogService {

  private final static String URI_GET_INSTANCES = "/tracing/v1/service/instances";

  @Resource
  private MatrixLogServiceImpl matrixLogService;

  public DeploySystemCluster getDeploySystemIPs(Long appId, String appName,
      String cluster) {
    DeploySystemAppData deploySystemApp = getDeploySystemApp(appId, null);
    if (deploySystemApp == null || CollectionUtils.isEmpty(deploySystemApp.getClusters())) {
      return null;
    }

    // choose cluster
    DeploySystemCluster targetCluster = null;
    List<DeploySystemCluster> clusters = deploySystemApp.getClusters();
    for (DeploySystemCluster current : clusters) {
      if (current.getName().equals(cluster)) {
        targetCluster = current;
        break;
      }
    }

    if (targetCluster == null) {
      return null;
    }

    // get ips
    List<String> ips = getIPs(targetCluster.getTracingCluster(), targetCluster.getTracingService());
    targetCluster.setIps(ips);
    return targetCluster;
  }

  private List<String> getIPs(String tracingCluster, String tracingService) {
    List<String> ips = new ArrayList<>();
    String mioapDomain = HeraTraceUtils.getHeraTraceDomainByTraceRegion(tracingCluster);
    if (null == mioapDomain) {
      log.warn("Failed to get mioapDomain, tracingCluster:{}, tracingService:{}", tracingCluster,
          tracingService);
      return ips;
    }
    try {
      String schema = HeraTraceUtils.MIOAP_DOMAIN_STAGING.equals(mioapDomain) ? "http" : "https";
      long endTime = TimeUnit.MILLISECONDS.toMicros(System.currentTimeMillis());
      long startTime = endTime - TimeUnit.MINUTES.toMicros(70);
      URI uri;
      if (tracingService.contains("@")) {
        // tracingService represents region(such as chn-beijing) not idc(such as c3/c4)
        String[] tracingServiceParts = tracingService.split("@");
        uri = new URIBuilder()
            .setScheme(schema).setHost(mioapDomain).setPath(URI_GET_INSTANCES)
            .addParameter("service", tracingServiceParts[1])
            .addParameter("tracingCluster", tracingServiceParts[0])
            .addParameter("startTime", String.valueOf(startTime))
            .addParameter("endTime", String.valueOf(endTime))
            .build();
      } else {
        uri = new URIBuilder()
            .setScheme(schema).setHost(mioapDomain).setPath(URI_GET_INSTANCES)
            .addParameter("service", tracingService)
            .addParameter("startTime", String.valueOf(startTime))
            .addParameter("endTime", String.valueOf(endTime))
            .build();
      }
      String returnGet = HttpClientV6.get(uri.toString(), null, 10000);
      TypeToken<List<DeploySystemAppsDTO.ServiceInstance>> token = new TypeToken<>() {
      };
      List<DeploySystemAppsDTO.ServiceInstance> serviceInstances = GSON.fromJson(returnGet, token);
      if (null == serviceInstances || serviceInstances.isEmpty()) {
        return ips;
      }

      // filter blank name
      return serviceInstances.stream()
          .map(DeploySystemAppsDTO.ServiceInstance::getName)
          .collect(Collectors.toList());
    } catch (Exception e) {
      log.error("Failed to getIPs, tracingCluster:{}, tracingService:{}", tracingCluster,
          tracingService, e);
    }
    return ips;
  }

  public DeploySystemAppData getDeploySystemApp(Long appId) {
    return getDeploySystemApp(appId, null);
  }

  public DeploySystemAppData getDeploySystemApp(Long appId,
      String targetTracingCluster) {
    DeploySystemAppData result = new DeploySystemAppData();
    List<TraceAppInfoDTO> traceAppInfoDTOList = matrixLogService.queryTraceAppInfos(
        new Long[]{appId});
    Set<DeploySystemCluster> clusters = getDeploySystemClusters(
        traceAppInfoDTOList, targetTracingCluster);
    if (CollectionUtils.isEmpty(traceAppInfoDTOList)) {
      return null;
    }

    result.setClusters(new ArrayList<>(clusters));
    result.setId(traceAppInfoDTOList.get(0).getAppId());
    result.setName(traceAppInfoDTOList.get(0).getAppName());
    result.setTreeId(traceAppInfoDTOList.get(0).getIamTreeId());
    return result;
  }

  private Set<DeploySystemCluster> getDeploySystemClusters(
      List<TraceAppInfoDTO> traceAppInfoDTOList, String targetTracingCluster) {
    Set<DeploySystemCluster> deploySystemClusters = new LinkedHashSet<>();
    if (CollectionUtils.isEmpty(traceAppInfoDTOList)) {
      return deploySystemClusters;
    }
    traceAppInfoDTOList.forEach((current) -> {
      if (StringUtils.isNotEmpty(current.getServiceCluster())) {
        DeploySystemCluster deploySystemCluster = toDeploySystemCluster(
            current);
        if (targetTracingCluster != null && !current.getTracingCluster().equals(targetTracingCluster)) {
          return;
        }

        deploySystemClusters.add(deploySystemCluster);
      }
    });
    return deploySystemClusters;
  }


  private DeploySystemCluster toDeploySystemCluster(
      TraceAppInfoDTO current) {
    DeploySystemCluster deploySystemCluster = new DeploySystemCluster();
    deploySystemCluster.setId(current.getServiceClusterId());
    deploySystemCluster.setName(current.getServiceCluster());
    deploySystemCluster.setTracingCluster(current.getTracingCluster());
    deploySystemCluster.setTracingService(current.getTracingService());
    return deploySystemCluster;
  }
}
