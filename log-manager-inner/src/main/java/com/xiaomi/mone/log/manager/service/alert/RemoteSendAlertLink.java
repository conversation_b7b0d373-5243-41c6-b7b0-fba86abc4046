package com.xiaomi.mone.log.manager.service.alert;

import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;

import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.ozhera.log.common.Config;
import org.apache.ozhera.log.common.Constant;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description p0 打电话—>发短信->飞书
 * @date 2022/9/2 15:40
 */
@Slf4j
public class RemoteSendAlertLink extends SendAlertLink {

    private static String P0_LEVEL = "P0";
    private static String P1_LEVEL = "P1";
    private static String P2_LEVEL = "P2";

    private static Integer P0_LEVEL_NOX = 100;
    private static Integer P1_LEVEL_NOX = 101;

    private static Integer P2_LEVEL_NOX = 102;

    public static Map<String, Integer> LEVEL2NOX = new HashMap<>();

    static {
        LEVEL2NOX.put(P0_LEVEL, P0_LEVEL_NOX);
        LEVEL2NOX.put(P1_LEVEL, P1_LEVEL_NOX);
        LEVEL2NOX.put(P2_LEVEL, P2_LEVEL_NOX);
    }
    private String url = Config.ins().get("alert.url", "http://api.falcon.srv/nox/api/v1/event/batch");
    private String token = Config.ins().get("alert.token", "257b2134-a7e6-57e2-9386-e8dfe8551c0d");
    private OkHttpClient httpClient;
    private Gson gson = Constant.GSON;

    private List<Target> targets;
    private Integer level;
    private Meta meta;

    public RemoteSendAlertLink(List<Target> targets, Integer level, Meta meta, OkHttpClient httpClient) {
        this.targets = targets;
        this.level = level;
        this.meta = meta;
        this.httpClient = httpClient;
    }

    @Override
    public boolean doSend() {
        Event event = Event.of(id, targets, level, meta);
        log.info("send alert, params:{}", gson.toJson(event));
        try {
            RequestBody requestBody = RequestBody.create(gson.toJson(event),
                    MediaType.parse("application/json; charset=utf-8"));
            Request request = new Request.Builder()
                    .url(url)
                    .addHeader("Authorization", String.format("Bearer %s", token))
                    .post(requestBody)
                    .build();
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                String rstJson = response.body().string();
                log.info("send alert succeed, alertId:{}, result:{}", meta.getAlertId(), rstJson);
            } else {
                log.info("send alert failed, alertId:{}, targets:{}", meta.getAlertId(), targets);
            }
        } catch (Exception e) {
            log.error(String.format("send alert exception, alertId:%d, targets:%s, url:%s, event:%s", meta.getAlertId(),
                    targets, url, gson.toJson(event)), e);
        }
        SendAlertLink next = super.next();
        if (null == next) {
            return true;
        }
        return next.doSend();
    }

    @Data
    private static class Event {
        @SerializedName("source_id")
        private Integer sourceId;
        /**
         * 告警级别，100=P0、101=P1、102=P2、103=P3、104=P4、105=P5
         */
        private Integer level = 100;
        private List<Target> targets;
        private Integer type = 0;
        private Meta meta;

        public static Event of(Integer sourceId, List<Target> targets, Integer level, Meta meta) {
            Event event = new Event();
            event.setSourceId(sourceId);
            event.setTargets(targets);
            event.setLevel(level);
            event.setMeta(meta);
            return event;
        }
    }

    @Data
    @Builder
    public static class Target {
        /**
         * * 告警接收对象，支持
         * * 1:通用告警组、
         * * 2: oncall 组、
         * * 3:个人、
         * * 4:飞书群聊、
         * * 5: iam 用户组、
         * * 6:iam tree 用户
         */
        private Integer type;
        /**
         * * 告警接收对象名称，个人=邮箱前缀；
         * * oncall组=oncall组名称；
         * * 通用告警组=通用告警组名称；
         * * 飞书群聊=飞书群聊ID（chatID）；
         * * iam用户组 = iam 用户组id；
         * * iam tree 用户=iam tree id
         */
        private String name;
    }

    @Data
    @Builder
    public static class Meta {
        @SerializedName("log-alert")
        private String logAlert = "日志告警";
        @SerializedName("alert_id")
        private String alertId;
        @SerializedName("rule_id")
        private String ruleId;
        @SerializedName("rule_name")
        private String ruleName;
        @SerializedName("app_name")
        private String appName;
        @SerializedName("alert_time")
        private String alertTime;
        @SerializedName("window_size")
        private String windowSize;
        @SerializedName("rule_regex")
        private String ruleRegex;
        private String operation;
        private int threshold;
        private int count;
        private String message;
        private String env;
        @SerializedName("trace_id")
        private String traceId;
        @SerializedName("message_prefix")
        private String messagePrefix;
        @SerializedName("trace_url")
        private String traceUrl;
        @SerializedName("log_url")
        private String logUrl;
        @SerializedName("at_members")
        private String atMembers;
    }

    @Data
    private static class AlertResult<T> {
        private String code;
        private String message;
        private T data;
    }
}
