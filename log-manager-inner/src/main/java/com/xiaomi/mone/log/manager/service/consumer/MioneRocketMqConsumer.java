package com.xiaomi.mone.log.manager.service.consumer;

import com.xiaomi.mone.log.manager.service.RocketMqService;
import com.xiaomi.mone.log.manager.service.impl.InnerMqConsumerServiceImpl;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.api.enums.ProjectSourceEnum;
import org.apache.ozhera.log.manager.model.dto.DockerScaleBo;
import org.apache.ozhera.log.manager.model.dto.ProjectInfo;
import org.apache.ozhera.log.manager.service.consumer.RocketMqConsumer;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.message.MessageExt;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.xiaomi.mone.log.manager.user.MoneUserDetailService.GSON;


/**
 * <AUTHOR>
 * @version 1.0
 * @description mq消费
 * @date 2021/7/14 20:15
 */
@Slf4j
@Service
public class MioneRocketMqConsumer extends RocketMqConsumer {

    @Value("$rocketmq_consumer_topic")
    private String consumeTopic;

    @Value("$rocketmq_consumer_tag")
    private String consumeTag;

    @Value("$rocketmq_consumer_scale_tag")
    private String consumeDockerScaleTag;

    @Resource
    private DefaultMQPushConsumer consumer;

    @Resource
    private RocketMqService rocketMqService;

    @Resource
    private InnerMqConsumerServiceImpl milogLogtailService;

    public void init() {
        log.info("consumer mq service init");
        List<String> projectTags = Arrays.stream(consumeTag.split(",")).collect(Collectors.toList());
        List<String> dockerScaleTags = Arrays.stream(consumeDockerScaleTag.split(",")).collect(Collectors.toList());
        String projectTag = projectTags.stream().collect(Collectors.joining("||"));
        String dockerScaleTag = dockerScaleTags.stream().collect(Collectors.joining("||"));
        try {
            consumer.subscribe(consumeTopic, projectTag + "||" + dockerScaleTag);
        } catch (MQClientException e) {
            log.error("订阅创建项目时的RocketMq消费异常", e);
        }
        consumer.registerMessageListener((MessageListenerOrderly) (list, consumeOrderlyContext) -> {
            list.stream().forEach(ele -> {
                // 创建项目发送的mq消息
                if (projectTags.contains(ele.getTags())) {
                    createProjectConsumeMessage(ele);
                }
                // 动态扩缩容
                if (dockerScaleTags.contains(ele.getTags())) {
                    dockerScaleConsumeMessage(ele);
                }
            });
            return ConsumeOrderlyStatus.SUCCESS;
        });

        try {
            consumer.start();
        } catch (MQClientException e) {
            log.error("订阅创建项目时的RocketMq客户端启动异常", e);
        }
    }

    private void createProjectConsumeMessage(MessageExt message) {
        try {
            byte[] body = message.getBody();
            ProjectInfo projectInfo = GSON.fromJson(new String(body), ProjectInfo.class);
            log.info("【创建项目】RocketMq消费的消息数据转化为对象: {}", projectInfo.toString());
            ProjectSourceEnum sourceEnum = ProjectSourceEnum.queryForSource(projectInfo.getMioneEnv());
//            rocketMqService.handleTopicAppRel(projectInfo, null, DEFAULT_OPERATOR, sourceEnum.getSource());
            log.info("【创建项目】RocketMq消费的消息消费结束");
        } catch (Exception ex) {
            log.error(String.format("【创建项目】RocketMq消费的消息消费异常:%s", message), ex);
        }
    }

    private void dockerScaleConsumeMessage(MessageExt message) {
        try {
            byte[] body = message.getBody();
            DockerScaleBo projectInfo = GSON.fromJson(new String(body), DockerScaleBo.class);
            log.info("【动态扩缩容】RocketMq消费的消息数据转化为对象: {}", projectInfo.toString());
            milogLogtailService.dockerScaleDynamic(projectInfo);
            log.info("【动态扩缩容】RocketMq消费的消息消费结束");
        } catch (Exception ex) {
            log.error("【动态扩缩容】RocketMq消费的消息消费异常:" + ex.getMessage(), ex);
        }
    }

}
