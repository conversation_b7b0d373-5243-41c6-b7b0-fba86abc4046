package com.xiaomi.mone.log.manager.common;

import com.xiaomi.youpin.docean.anno.Component;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.manager.bootstrap.LogStoragePlugin;
import org.apache.ozhera.log.manager.mapper.MilogEsClusterMapper;
import org.apache.ozhera.log.manager.model.pojo.MilogEsClusterDO;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/9/18 19:14
 */
@Slf4j
@Component
public class EsAddressReplaceService {

    private static final String ES_ADDRESS_KEY = "zjynewretail.api.es.srv:80";
    private static final String NEW_ES_ADDRESS_KEY = "aknewretail.api.es.srv:80";
    private static final String ES_USER_KEY = "zgq_xiaoneng_7788";

    @Resource
    private MilogEsClusterMapper milogEsClusterMapper;

    @Resource
    private LogStoragePlugin logStoragePlugin;

    public void replaceEsAddress() {
        List<MilogEsClusterDO> esClusterList = milogEsClusterMapper.selectAll();
        if (esClusterList == null || esClusterList.isEmpty()) {
            log.warn("no Log storage type");
            return;
        }
        for (MilogEsClusterDO cluster : esClusterList) {
            if (StringUtils.equals(ES_ADDRESS_KEY, cluster.getAddr()) &&
                    StringUtils.equalsIgnoreCase(ES_USER_KEY, cluster.getUser())) {
                log.info("replace es address,esId:{},address:{}", cluster.getId(), cluster.getAddr());
                cluster.setAddr(NEW_ES_ADDRESS_KEY);
                logStoragePlugin.initializeLogStorage(cluster);
            }
        }
    }


}
