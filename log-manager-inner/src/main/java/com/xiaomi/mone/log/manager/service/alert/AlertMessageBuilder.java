package com.xiaomi.mone.log.manager.service.alert;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.xiaomi.mone.log.manager.model.alert.Alert;
import com.xiaomi.mone.log.manager.model.alert.AlertCondition;
import com.xiaomi.mone.log.manager.model.alert.LogNumAlertMsg;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.utils.DateUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.DEFAULT_ALERT_WINDOW_SIZE;

public class AlertMessageBuilder {

    private static final String COUNT_LIMIT = "countLimit";
    private static final String FILTER_REGEX = "filterRegex";
    private static final String WINDOW_SIZE = "windowSize";
    private static final String OPERATION = "operation";
    private static final String dateFormat = "yyyy-MM-dd HH:mm:ss";


    private static DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);


    public static RemoteSendAlertLink.Meta buildEventMeta(int count, Alert alert, long ruleId, String traceId,
                                                          String heraUrl, AlertCondition matchedCondition, String env,
                                                          String ruleRegex, String ruleName, String timeStamp,
                                                          String logParam, String message) {
        message = StringUtils.replaceAll(message, "\"", "'");
        message = StringUtils.replaceAll(message, "\\{", "[");
        message = StringUtils.replaceAll(message, "\\}", "]");
        String messagePrefix = message;
        if (message.length() > 600) {
            message = message.substring(0, 600);
        }
        if (messagePrefix.length() > 400) {
            messagePrefix = messagePrefix.substring(0, 400).replaceAll("\\\\", "\\\\\\\\\\\\\\\\");
        }
        Integer windowSize = null == alert.getWindowSize() ? DEFAULT_ALERT_WINDOW_SIZE : alert.getWindowSize();
        message = JSONUtil.quote(message, false);
        messagePrefix = JSONUtil.quote(messagePrefix, false);
        String date;
        if (StringUtils.isNotEmpty(timeStamp)) {
            date = DateUtils.timeStamp2Date(timeStamp, dateFormat);
        } else {
            date = LocalDateTime.now().format(formatter);
        }
        String windowSizeString = STR."\{divideAndFormat(windowSize, 60)}分钟内";

        if (StringUtils.isEmpty(traceId)) {
            traceId = "";
        }
        return RemoteSendAlertLink.Meta.builder()
                .alertId(Long.toString(alert.getId()))
                .ruleId(Long.toString(ruleId))
                .ruleName(ruleName)
                .appName(alert.getArguments().get("appName") == null ? alert.getAppName() : alert.getArguments().get("appName"))
                .alertTime(date)
                .windowSize(windowSizeString)
                .ruleRegex(ruleRegex)
                .operation(matchedCondition.getOperation())
                .threshold(matchedCondition.getValue())
                .count(count)
                .message(message)
                .env(env)
                .traceId(traceId.trim())
                .messagePrefix(messagePrefix)
                .traceUrl(String.format("%s/project-hera-tracing/traceid/%s", heraUrl, traceId.trim()))
                .logUrl(String.format("%s/project-milog/user/space-tree?%s", heraUrl, logParam))
                .atMembers(alert.getAtMembers())
                .build();
    }

    public static String buildLogNumAlertMsg(LogNumAlertMsg msg) {
        if (msg == null) {
            return null;
        }
        return String.format(LOG_NUMBER_ALARM_TEMPLATE, msg.getDay(), msg.getAppName(), msg.getNumber(), msg.getEnv());
    }

    public static String divideAndFormat(Integer dividend, Integer divisor) {
        if (divisor == 0) {
            throw new IllegalArgumentException("除数不能为零");
        }

        double result = NumberUtil.div(dividend, divisor, 2).doubleValue();

        if (result == Math.floor(result) && !Double.isInfinite(result)) {
            return String.valueOf((int) result);
        } else {
            return String.format("%.2f", result);
        }
    }

    private static final String LOG_NUMBER_ALARM_TEMPLATE =
            "{\n" +
                    "  \"config\": {\n" +
                    "    \"wide_screen_mode\": true\n" +
                    "  },\n" +
                    "  \"elements\": [\n" +
                    "    {\n" +
                    "      \"tag\": \"div\",\n" +
                    "      \"text\": {\n" +
                    "        \"content\": \"\uD83D\uDCC5️ **日志时间**\\n%s\\n\\n \uD83D\uDCCB **应用名称**\\n%s \\n\\n \uD83D\uDD22 **日志量**\\n%s条！\\n\\n您的日志量过多，请减少日志量\",\n" +
                    "        \"tag\": \"lark_md\"\n" +
                    "      }\n" +
                    "    }\n" +
                    "  ],\n" +
                    "  \"header\": {\n" +
                    "    \"template\": \"red\",\n" +
                    "    \"title\": {\n" +
                    "      \"content\": \"【日志量大于40000000】 -  %s环境\",\n" +
                    "      \"tag\": \"plain_text\"\n" +
                    "    }\n" +
                    "  }\n" +
                    "}";
}
