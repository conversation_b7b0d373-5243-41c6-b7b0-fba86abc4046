package com.xiaomi.mone.log.manager.service;

import com.xiaomi.mone.log.manager.model.dto.BillingAccount;
import com.xiaomi.mone.log.manager.model.po.ResourceBillAccountRelDO;
import org.apache.ozhera.log.manager.model.pojo.MilogEsIndexDO;
import org.apache.ozhera.log.manager.model.pojo.MilogLogStoreDO;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface BillingManagementService {

    // return true if collect work done
    boolean collectLogStorage(List<MilogEsIndexDO> esIndexs, String day);

    boolean isLogStorageCollectDone(String day);

    boolean clearLogStorageByDay(String day);

    boolean clearLogStorageBeforeDay(String day);

    long queryESStorageBytes(MilogLogStoreDO store, boolean byDay, String day);

    double queryESStorageAvgBytes(MilogLogStoreDO store, boolean byDay, String day);

    BillingAccount queryMilogAppAccount(Long milogAppId);

    BillingAccount queryMilogSpaceAccount(Long spaceId);

    BillingAccount queryMilogTailAccount(Long tailId);

    /**
     * buildBaseRel itemName->relatedLogTails，生成一条账号关联关系
     */
    List<ResourceBillAccountRelDO> buildBaseRel(Map<String, List<MilogLogTailDo>> keyTailsMap, ResourceBillAccountRelDO.ItemTypeEnum itemTypeEnum);


}
