package com.xiaomi.mone.log.manager.service.impl;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.xiaomi.mone.log.manager.model.dto.MergeLogQuery;
import com.xiaomi.mone.log.manager.service.MergeLogDataService;
import com.xiaomi.mone.log.manager.service.remoting.loki.LokiDataServiceImpl;
import com.xiaomi.youpin.docean.anno.Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.ConnectionClosedException;
import org.apache.ozhera.log.common.Result;
import org.apache.ozhera.log.exception.CommonError;
import org.apache.ozhera.log.manager.bootstrap.LogStoragePlugin;
import org.apache.ozhera.log.manager.common.context.MoneUserContext;
import org.apache.ozhera.log.manager.dao.MilogLogstoreDao;
import org.apache.ozhera.log.manager.mapper.MilogEsClusterMapper;
import org.apache.ozhera.log.manager.model.dto.EsStatisticResult;
import org.apache.ozhera.log.manager.model.dto.LogDTO;
import org.apache.ozhera.log.manager.model.pojo.MilogLogStoreDO;
import org.apache.ozhera.log.manager.service.impl.EsDataServiceImpl;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

import static com.xiaomi.mone.enums.InnerLogTypeEnum.LOKI_APP_LOG;
import static com.xiaomi.mone.log.manager.common.utils.EsUtils.DISABLE_LIFECYCLE;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 合并日志查询
 * @date 2024/10/28/11:10
 */
@Slf4j
@Service
public class MergeLogDataServiceImpl implements MergeLogDataService {

    @Resource
    private EsDataServiceImpl esDataService;

    @Resource
    private LokiDataServiceImpl lokiDataService;

    @Resource
    private MilogLogstoreDao logstoreDao;

    @Resource
    private InnerEsDataServiceImpl innerEsDataService;

    @Resource
    private MilogEsClusterMapper milogEsClusterMapper;

    @Resource
    private LogStoragePlugin esPlugin;

    private static final String ES = "Es";
    private static final String LOKI = "Loki";

    public static Gson gson = new Gson();

    @Override
    public Result<JsonObject> logQuery(MergeLogQuery logQuery) throws IOException {
        logQuery.validate();
        try {
            // 判断当前查询数据源
            String logType = getLogQuerySource(logQuery);
            if (ES.equals(logType)) {
                //如果开始时间是三天前的，打印出来日志方便es出问题的时候排查
                if (logQuery.getStartTime() < System.currentTimeMillis() - TimeUnit.DAYS.toMillis(3)) {
                    log.warn("Log query warn, log search error,logQuery:[{}],user:[{}]", gson.toJson(logQuery), MoneUserContext.getCurrentUser());
                }
                // 查询es
                Result<LogDTO> logDtoResult = esDataService.logQuery(logQuery);
                JsonObject logData = gson.toJsonTree(logDtoResult.getData()).getAsJsonObject();
                logData.addProperty("storageType", ES);
                return Result.success(logData);
            }
        } catch (Throwable e) {
            log.error("Log query error, log search error,logQuery:[{}],user:[{}]", logQuery, MoneUserContext.getCurrentUser(), e);
//            handleException(logQuery, e);
            return Result.failParam(e.getMessage());
        }
        // 查询Loki
        return lokiDataService.logQuery(logQuery);
    }

    @Override
    public Result<JsonObject> logStatistic(MergeLogQuery logQuery) throws IOException {
        logQuery.validate();
        try {
            // 判断当前查询数据源
            String logType = getLogQuerySource(logQuery);
            if (ES.equals(logType)) {
                // 查询es
                Result<EsStatisticResult> esStatistic = esDataService.EsStatistic(logQuery);
                if (esStatistic.getCode() == CommonError.Success.getCode()) {
                    JsonObject logData = gson.toJsonTree(esStatistic.getData()).getAsJsonObject();
                    logData.addProperty("logType", ES);
                    return Result.success(logData);
                }
                return Result.failParam(esStatistic.getMessage());
            }
        } catch (Throwable e) {
            log.error("Log query errors while query log bar chart statistics report errors[{}],logQuery:[{}],user:[{}]", e, logQuery, MoneUserContext.getCurrentUser(), e);
//            handleException(logQuery, e);
            return Result.failParam(e.getMessage());
        }
        // 查询Loki
        return lokiDataService.logStatistic(logQuery);
    }

    private String getLogQuerySource(MergeLogQuery logQuery) throws IOException {
        MilogLogStoreDO milogLogStoreDO = logstoreDao.queryById(logQuery.getStoreId());
        if (LOKI_APP_LOG.getType().equals(milogLogStoreDO.getLogType())) {
            return LOKI;
        }
        Long lifeCycle = innerEsDataService.queryIndexLifeCycle(logQuery.getStoreId());
        if (DISABLE_LIFECYCLE.equals(lifeCycle)) {
            return ES;
        }
        Long nowTimeMillis = System.currentTimeMillis();
        if (nowTimeMillis - logQuery.getStartTime() > TimeUnit.DAYS.toMillis(lifeCycle)) {
            return LOKI;
        }
        return ES;
    }

    private void handleException(MergeLogQuery logQuery, Throwable e) {
        if (e instanceof ConnectionClosedException) {
            log.warn("ConnectionClosedException, log search error,logQuery:[{}],user:[{}]", logQuery, MoneUserContext.getCurrentUser());
            MilogLogStoreDO logStore = logstoreDao.queryById(logQuery.getStoreId());
            esPlugin.initializeLogStorage(milogEsClusterMapper.selectById(logStore.getEsClusterId()));
        }
    }

}
