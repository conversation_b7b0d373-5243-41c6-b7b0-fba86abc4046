package com.xiaomi.mone.log.manager.model.dto;

import com.google.gson.annotations.SerializedName;
import com.xiaomi.mone.enums.AccountTypeEnum;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @Description app 关联账户信息
 * @date 2023-10-24
 */
@Data
@Builder
public class BillingAccount {
    @SerializedName("accountType")
    private String accountTypeEnum;
    private String accountName;

    public BillingAccount() {
        this.accountTypeEnum = AccountTypeEnum.IAMTREE_TYPE.toString();
    }

    public BillingAccount(String accountTypeEnum, String accountName) {
        this.accountTypeEnum = accountTypeEnum;
        this.accountName = accountName;
    }
}
