package com.xiaomi.mone.log.manager.service.impl;

import com.google.common.collect.Lists;
import com.xiaomi.mone.enums.InnerMiddlewareEnum;
import com.xiaomi.mone.log.manager.common.context.MoneUserContext;
import com.xiaomi.mone.log.manager.common.utils.DtUtils;
import com.xiaomi.mone.log.manager.model.bo.alert.MqInfoBo;
import com.xiaomi.mone.log.manager.service.InnerMilogAppMiddlewareRelService;
import com.xiaomi.mone.log.manager.service.InnerMqConfigService;
import com.xiaomi.mone.log.manager.service.TalosMqConfigService;
import com.xiaomi.youpin.docean.Ioc;
import com.xiaomi.youpin.docean.anno.Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.app.api.response.AppBaseInfo;
import org.apache.ozhera.log.manager.common.Utils;
import org.apache.ozhera.log.manager.common.exception.MilogManageException;
import org.apache.ozhera.log.manager.dao.MilogAppMiddlewareRelDao;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.dao.MilogLogstoreDao;
import org.apache.ozhera.log.manager.dao.MilogMiddlewareConfigDao;
import org.apache.ozhera.log.manager.model.pojo.MilogAppMiddlewareRel;
import org.apache.ozhera.log.manager.model.pojo.MilogLogStoreDO;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;
import org.apache.ozhera.log.manager.model.pojo.MilogMiddlewareConfig;
import org.apache.ozhera.log.manager.service.impl.HeraAppServiceImpl;

import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import static org.apache.ozhera.log.common.Constant.DEFAULT_CONSUMER_GROUP;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/4/10 17:39
 */
@Service
@Slf4j
public class InnerMilogAppMiddlewareRelServiceImpl implements InnerMilogAppMiddlewareRelService {


    @Resource
    private MilogAppMiddlewareRelDao milogAppMiddlewareRelDao;
    @Resource
    private MilogMiddlewareConfigDao milogMiddlewareConfigDao;
    @Resource
    private MilogLogTailDao milogLogtailDao;
    @Resource
    private MilogLogstoreDao milogLogstoreDao;
    @Resource
    private HeraAppServiceImpl heraAppService;

    private InnerMqConfigService mqConfigService;

    @Override
    public void bindingMisAppTailConfigRel(Long tailId, Long milogAppId, String motorRoomEn) {
        // mis应用一个app对应多个配置文件
        AppBaseInfo appBaseInfo = heraAppService.queryById(milogAppId);
        MilogMiddlewareConfig wareConfig = milogMiddlewareConfigDao.queryDefaultMqMiddlewareConfigMotorRoom(motorRoomEn);
        if (null != wareConfig) {
            instantiateMqConfigSubClass(InnerMiddlewareEnum.queryByCode(wareConfig.getType()));
            MilogAppMiddlewareRel.Config config = mqConfigService.generateConfig(wareConfig.getAk(),
                    wareConfig.getSk(), wareConfig.getNameServer(), wareConfig.getServiceUrl(),
                    wareConfig.getAuthorization(), wareConfig.getOrgId(), wareConfig.getTeamId(),
                    Long.valueOf(appBaseInfo.getBindId()), appBaseInfo.getAppName(), appBaseInfo.getPlatformName(), tailId);
            handleTailMqRel(tailId, milogAppId, wareConfig, config);
        } else {
            log.error("当前机房没有配置MQ配置信息，请配置机房下消息配置信息,机房：{}", motorRoomEn);
        }
    }

    @Override
    public void bindingPlatformTailConfigRel(Long spaceId, Long storeId, Long tailId, Long milogAppId, Long storeMqResourceId) {
        // 创建 topic
        MilogMiddlewareConfig milogMiddlewareConfig = milogMiddlewareConfigDao.queryById(storeMqResourceId);

        // 去创建 topic
        instantiateMqConfigSubClass(InnerMiddlewareEnum.queryByCode(milogMiddlewareConfig.getType()));
        MilogAppMiddlewareRel.Config config = mqConfigService.generatePlatformConfig(spaceId, storeId, tailId, milogMiddlewareConfig);

        // 写 milog_app_middleware_rel
        MilogAppMiddlewareRel milogAppMiddlewareRel = generateMiddlewareRel(tailId, milogAppId, storeMqResourceId, config);
        milogAppMiddlewareRelDao.insertUpdate(milogAppMiddlewareRel);
    }

    @Override
    public void unBindingPlatformTailConfigRel(Long storeId, Long tailId, Long milogAppId, Long storeMqResourceId) {
        // 删除 topic
        String topicName = DtUtils.buildTalosTopic(storeId, tailId);
        MilogMiddlewareConfig milogMiddlewareConfig = milogMiddlewareConfigDao.queryById(storeMqResourceId);
        instantiateMqConfigSubClass(InnerMiddlewareEnum.queryByCode(milogMiddlewareConfig.getType()));
        mqConfigService.deletePlatformTopic(topicName, milogMiddlewareConfig);

        // 删除绑定关系
        milogAppMiddlewareRelDao.deleteRel(milogAppId, storeMqResourceId);
    }

    @Override
    public MqInfoBo getMqInfoBo(List<Long> tailIds) {
        MqInfoBo mqInfoBo = new MqInfoBo();
        List<String> topics = Lists.newArrayList();
        List<String> consumerGroups = Lists.newArrayList();
        List<String> consumerTags = Lists.newArrayList();
        extractConsumerInfo(tailIds, mqInfoBo, topics, consumerGroups, consumerTags);
        mqInfoBo.setTopics(topics.stream().distinct().collect(Collectors.toList()));
        mqInfoBo.setConsumerGroups(consumerGroups);
        mqInfoBo.setConsumerTags(consumerTags);
        return mqInfoBo;
    }

    @Override
    public void createMilogAppMiddlewareRel(Long spaceId, Long storeId, Long tailId, Long milogAppId, MilogMiddlewareConfig milogMiddlewareConfig) {
        instantiateMqConfigSubClass(InnerMiddlewareEnum.queryByCode(milogMiddlewareConfig.getType()));
        MilogAppMiddlewareRel.Config config = mqConfigService.generatePlatformConfigWithoutCreate(spaceId, storeId, tailId, milogMiddlewareConfig);
        MilogAppMiddlewareRel milogAppMiddlewareRel = generateMiddlewareRel(tailId, milogAppId, milogMiddlewareConfig.getId(), config);
        milogAppMiddlewareRelDao.insertUpdate(milogAppMiddlewareRel);
    }

    @Override
    public void bindingTailConfigRel(Long tailId, Long milogAppId, Long middlewareConfigId, String topicName) {
//1.查询配置的配型组装config信息
        //2.入库
        MilogMiddlewareConfig wareConfig = queryMiddlewareConfig(middlewareConfigId);
        if (null != wareConfig) {
            middlewareConfigId = wareConfig.getId();
            instantiateMqConfigSubClass(InnerMiddlewareEnum.queryByCode(wareConfig.getType()));
            AppBaseInfo appBaseInfo = heraAppService.queryById(milogAppId);
            MilogAppMiddlewareRel.Config config = new MilogAppMiddlewareRel.Config();
            if (StringUtils.isEmpty(topicName)) {
                config = mqConfigService.generateConfig(wareConfig.getAk(),
                        wareConfig.getSk(), wareConfig.getNameServer(), wareConfig.getServiceUrl(),
                        wareConfig.getAuthorization(), wareConfig.getOrgId(), wareConfig.getTeamId(),
                        Long.valueOf(appBaseInfo.getBindId()), appBaseInfo.getAppName(), appBaseInfo.getPlatformName(), tailId);
            } else {
                config.setTopic(topicName);
                config.setPartitionCnt(1);
            }
            MilogLogTailDo milogLogtailDo = milogLogtailDao.queryById(tailId);
            String tag = Utils.createTag(milogLogtailDo.getSpaceId(), milogLogtailDo.getStoreId(), milogLogtailDo.getId());
            config.setTag(tag);
            config.setConsumerGroup(DEFAULT_CONSUMER_GROUP + tag);
            milogAppMiddlewareRelDao.insertUpdate(generateMiddlewareRel(tailId, milogAppId, middlewareConfigId, config));
        } else {
            log.error("当前环境的中间件配置为空,tailId:{},milogAppId:{},middlewareConfigId:{}", tailId, milogAppId, middlewareConfigId);
        }
    }

    @Override
    public void defaultBindingAppTailConfigRel(Long tailId, Long milogAppId, Long middleWareId, String topicName, Integer batchSendSize) {
        AppBaseInfo appBaseInfo = heraAppService.queryById(milogAppId);
        MilogMiddlewareConfig wareConfig;
        if (null == middleWareId) {
            wareConfig = milogMiddlewareConfigDao.queryDefaultMiddlewareConfig();
        } else {
            wareConfig = milogMiddlewareConfigDao.queryById(middleWareId);
        }
        if (null != wareConfig) {
            MilogAppMiddlewareRel.Config config = new MilogAppMiddlewareRel.Config();
            if (StringUtils.isEmpty(topicName)) {
                instantiateMqConfigSubClass(InnerMiddlewareEnum.queryByCode(wareConfig.getType()));
                config = mqConfigService.generateConfig(wareConfig.getAk(),
                        wareConfig.getSk(), wareConfig.getNameServer(), wareConfig.getServiceUrl(),
                        wareConfig.getAuthorization(), wareConfig.getOrgId().trim(),
                        wareConfig.getTeamId(), Long.valueOf(appBaseInfo.getBindId()),
                        appBaseInfo.getAppName(), appBaseInfo.getPlatformName(), tailId);
            } else {
                config.setTopic(topicName);
                config.setPartitionCnt(1);
            }
            if (null != batchSendSize) {
                config.setBatchSendSize(batchSendSize);
            }
            handleTailMqRel(tailId, milogAppId, wareConfig, config);
        } else {
            log.error("当前部门没有配置MQ配置信息，请配置当前部门的消息配置信息,tailId：{},storeId:{},middleWareId:{}",
                    tailId, milogAppId, middleWareId);
            throw new MilogManageException("当前部门没有配置MQ配置信息，请配置当前部门的资源配置信息");
        }
    }

    private void extractConsumerInfo(List<Long> tailIds, MqInfoBo mqInfoBo, List<String> topics, List<String> consumerGroups, List<String> consumerTags) {
        for (Long tailId : tailIds) {
            MilogLogTailDo logTailDo = milogLogtailDao.queryById(tailId);
            if (null == logTailDo) {
                continue;
            }
            List<MilogAppMiddlewareRel> middlewareRels = milogAppMiddlewareRelDao.queryByCondition(null, null, tailId);
            if (CollectionUtils.isNotEmpty(middlewareRels)) {
                MilogMiddlewareConfig middlewareConfig = milogMiddlewareConfigDao.queryById(middlewareRels.get(0).getMiddlewareId());
                mqInfoBo.setConsumerAccessKey(middlewareConfig.getAk());
                mqInfoBo.setConsumerSecretKey(middlewareConfig.getSk());
                mqInfoBo.setConsumerServer(middlewareConfig.getServiceUrl());
                topics.add(middlewareRels.get(middlewareRels.size() - 1).getConfig().getTopic());
                consumerGroups.add(middlewareRels.get(middlewareRels.size() - 1).getConfig().getConsumerGroup());
                consumerTags.add(middlewareRels.get(middlewareRels.size() - 1).getConfig().getTag());
            }
        }
    }

    private Long getMqResourceId(MilogLogTailDo logTailDo) {
        MilogLogStoreDO logStoreDO = milogLogstoreDao.queryById(logTailDo.getStoreId());
        Long mqResourceId = logStoreDO.getMqResourceId();
        if (null == mqResourceId) {
            List<MilogAppMiddlewareRel> appMiddlewareRels = milogAppMiddlewareRelDao.queryByCondition(logTailDo.getMilogAppId(), null, logTailDo.getId());
            if (CollectionUtils.isEmpty(appMiddlewareRels)) {
                throw new MilogManageException("mq config not exist");
            }
            mqResourceId = appMiddlewareRels.get(appMiddlewareRels.size() - 1).getMiddlewareId();
        }
        return mqResourceId;
    }

    private void handleTailMqRel(Long tailId, Long milogAppId, MilogMiddlewareConfig wareConfig, MilogAppMiddlewareRel.Config config) {
        MilogLogTailDo milogLogtailDo = milogLogtailDao.queryById(tailId);
        String tag = Utils.createTag(milogLogtailDo.getSpaceId(), milogLogtailDo.getStoreId(), milogLogtailDo.getId());
        config.setTag(tag);
        config.setConsumerGroup(DEFAULT_CONSUMER_GROUP + tag);
        MilogAppMiddlewareRel milogAppMiddlewareRel = generateMiddlewareRel(tailId, milogAppId, wareConfig.getId(), config);
        milogAppMiddlewareRelDao.insertUpdate(milogAppMiddlewareRel);
    }

    private MilogAppMiddlewareRel generateMiddlewareRel(Long tailId, Long milogAppId, Long configId, MilogAppMiddlewareRel.Config config) {
        MilogAppMiddlewareRel milogAppMiddlewareRel = new MilogAppMiddlewareRel();
        milogAppMiddlewareRel.setMilogAppId(milogAppId);
        milogAppMiddlewareRel.setMiddlewareId(configId);
        milogAppMiddlewareRel.setTailId(tailId);
        milogAppMiddlewareRel.setConfig(config);
        milogAppMiddlewareRel.setCtime(Instant.now().toEpochMilli());
        milogAppMiddlewareRel.setUtime(Instant.now().toEpochMilli());
        if (null == MoneUserContext.getCurrentUser() ||
                StringUtils.isEmpty(MoneUserContext.getCurrentUser().getUser())) {
            MilogLogTailDo milogLogTailDo = milogLogtailDao.queryById(tailId);
            milogAppMiddlewareRel.setCreator(milogLogTailDo.getCreator());
            milogAppMiddlewareRel.setUpdater(milogLogTailDo.getUpdater());
        } else {
            milogAppMiddlewareRel.setCreator(MoneUserContext.getCurrentUser().getUser());
            milogAppMiddlewareRel.setUpdater(MoneUserContext.getCurrentUser().getUser());
        }
        return milogAppMiddlewareRel;
    }

    /**
     * middlewareConfigId 不为空找寻自己对应的 否则选择默认的
     *
     * @param middlewareConfigId
     * @return
     */
    public MilogMiddlewareConfig queryMiddlewareConfig(Long middlewareConfigId) {
        MilogMiddlewareConfig wareConfig;
        if (null != middlewareConfigId) {
            wareConfig = milogMiddlewareConfigDao.queryById(middlewareConfigId);
        } else {
            wareConfig = milogMiddlewareConfigDao.queryDefaultMiddlewareConfig();
        }
        return wareConfig;
    }

    private void instantiateMqConfigSubClass(InnerMiddlewareEnum middlewareEnum) {
        switch (middlewareEnum) {
            case ROCKETMQ:
                mqConfigService = Ioc.ins().getBean(InnerRocketMqConfigService.class);
                break;
            case TALOS:
            case PLATFORM_DEFAULT_TALOS:
                mqConfigService = Ioc.ins().getBean(TalosMqConfigService.class);
        }
    }
}
