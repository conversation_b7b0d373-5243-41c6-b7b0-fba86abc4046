package com.xiaomi.mone.log.manager.service.impl;

import com.google.common.collect.Lists;
import com.xiaomi.mone.log.manager.service.BaseMilogRpcConsumerService;
import com.xiaomi.mone.miline.api.bo.Result;
import com.xiaomi.mone.miline.api.dto.PipelineDeployDto;
import com.xiaomi.mone.miline.api.dto.milog.ProjectInstanceDto;
import com.xiaomi.mone.miline.api.dto.milog.ProjectInstanceQry;
import com.xiaomi.mone.miline.api.service.MilogProviderService;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import com.xiaomi.youpin.docean.plugin.dubbo.anno.Reference;
import com.xiaomi.youpin.gwdash.bo.*;
import com.xiaomi.youpin.gwdash.bo.openApi.ProjectDeployInfoQuery;
import com.xiaomi.youpin.gwdash.service.OpenApiService;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MilineRpcConsumerServiceImpl extends BaseMilogRpcConsumerService {

    @Reference(interfaceClass = MilogProviderService.class, group = "$dubbo.miline.group", check = false, version = "1.0", timeout = 15000)
    private MilogProviderService milogProviderService;

    @Reference(interfaceClass = MilogProviderService.class, group = "staging", check = false, version = "1.0", timeout = 15000)
    private MilogProviderService stagingLogProviderService;

    @Reference(interfaceClass = OpenApiService.class, group = "$dubbo.group", check = false, timeout = 15000)
    private OpenApiService openApiService;

    @Value("$dubbo.miline.rpc.env")
    private String dubboMilineRpcEnv;

    @Value(value = "$server.type")
    private String serverType;

    @Override
    public MiLogMachineBo queryMachineInfoByProject(String projectName, String name) {
        return milogProviderService.queryMachineInfoByProject(projectName, name, dubboMilineRpcEnv);
    }

    @Override
    public Page<ProjectDeployInfoDTO> queryProjectDeployInfoList(ProjectDeployInfoQuery query) {
        return null;
    }

    @Override
    public List<String> queryAppsByIp(String ip) {
        return null;
    }

    @Override
    public List<MachineBo> queryIpsByAppId(Long projectId, Long projectEnvId, String envName) {
        return null;
    }

    @Override
    public List<SimplePipleEnvBo> querySimplePipleEnvBoByProjectId(Long projectId) {
        return null;
    }

    @Override
    public List<SimplePipleEnvBo> querySimplePipleEnvBoByProjectId(Long projectId, String env) {
        return milogProviderService.querySimplePipleEnvBoByProjectId(projectId, env);
    }

    @Override
    public List<ProjectMemberDTO> queryProjectIdsByUserName(String userName) {
        return null;
    }

    @Override
    public List<Long> queryDeleteProjectId() {
        return null;
    }

    @Override
    public List<SimplePipleEnvBo> queryStagingSimplePipleEnvBoByProjectId(Long projectId, String env) {
        return stagingLogProviderService.querySimplePipleEnvBoByProjectId(projectId, env);
    }

    @Override
    public PipelineDeployDto qryDeployInfo(long projectId, long pipelineId) {
        Result<PipelineDeployDto> res = milogProviderService.qryDeployInfo(projectId, pipelineId);
        return res.getData();
    }

    @Override
    protected List<String> queryLiveMachines() {
        return Lists.newArrayList();
    }

    @Override
    public ProjectInstanceDto queryMachineInfoByProjectNew(Long projectId, List<Long> pipelineIds) {
        ProjectInstanceQry params = new ProjectInstanceQry();
        params.setProjectId(projectId);
        params.setPipelineIds(pipelineIds);
        params.setEnv(dubboMilineRpcEnv);
        return milogProviderService.queryMachineInfoByProjectNew(params);
    }
}
