/*
 *  Copyright 2020 Xiaomi
 *
 *    Licensed under the Apache License, Version 2.0 (the "License");
 *    you may not use this file except in compliance with the License.
 *    You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *    Unless required by applicable law or agreed to in writing, software
 *    distributed under the License is distributed on an "AS IS" BASIS,
 *    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *    See the License for the specific language governing permissions and
 *    limitations under the License.
 */

package com.xiaomi.mone.log.manager.controller.interceptor;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xiaomi.hera.trace.context.TraceIdUtil;
import com.xiaomi.mone.log.manager.common.context.MoneUserContext;
import com.xiaomi.mone.log.manager.common.context.OperationOaContext;
import com.xiaomi.mone.log.manager.user.InnerMoneUtil;
import com.xiaomi.mone.log.manager.user.MoneUser;
import com.xiaomi.mone.tpc.login.vo.AuthUserVo;
import com.xiaomi.youpin.docean.Ioc;
import com.xiaomi.youpin.docean.anno.Component;
import com.xiaomi.youpin.docean.aop.AopContext;
import com.xiaomi.youpin.docean.aop.EnhanceInterceptor;
import com.xiaomi.youpin.docean.mvc.ContextHolder;
import com.xiaomi.youpin.docean.mvc.HttpResponseUtils;
import com.xiaomi.youpin.docean.mvc.MvcContext;
import com.xiaomi.youpin.docean.mvc.common.MvcConst;
import com.xiaomi.youpin.docean.mvc.session.HttpSession;
import com.xiaomi.youpin.docean.plugin.dubbo.anno.Reference;
import com.xiaomi.youpin.gwdash.bo.openApi.OperationLogRequest;
import com.xiaomi.youpin.gwdash.service.OperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.ozhera.log.annotation.UnifiedResponse;
import org.apache.ozhera.log.common.Config;
import org.apache.ozhera.log.common.Result;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.xiaomi.mone.log.manager.user.MoneUserDetailService.GSON;
import static org.apache.ozhera.log.common.Constant.SYMBOL_COMMA;
import static org.apache.ozhera.log.manager.common.ManagerConstant.SPACE_PAGE_URL;
import static org.apache.ozhera.log.manager.common.ManagerConstant.TPC_HOME_URL_HEAD;


/**
 * <AUTHOR>
 * @Date 2021/9/2 15:18
 */
@Slf4j
@Component
public class CasHttpRequestInterceptor extends EnhanceInterceptor {

    @Reference(interfaceClass = OperationLogService.class, group = "$dubbo.group", check = false, timeout = 5000)
    private OperationLogService operationLogService;

    private static final Integer MAX_LENGTH = 3000;

    private static final AtomicLong REQUEST_COUNT = new AtomicLong();

    private String filterUrls = Config.ins().get("filter_urls", Strings.EMPTY);


    private static final Set<String> TPC_HEADERS_URLS = Sets.newHashSet();

    private List<String> filterUrlList;

    {
        filterUrlList = Arrays.stream(filterUrls.split(SYMBOL_COMMA)).distinct().collect(Collectors.toList());
        TPC_HEADERS_URLS.add(SPACE_PAGE_URL);
    }

    @Override
    public void before(AopContext aopContext, Method method, Object[] args) {
        REQUEST_COUNT.incrementAndGet();
        /**
         * User information will not be available in the context
         */
        if (filterUrlList.contains(method.getName())) {
            return;
        }
        MvcContext mvcContext = ContextHolder.getContext().get();
        saveUserInfoThreadLocal(mvcContext);
        saveRequestThreadLocal(args);
    }

    @Override
    public Object after(AopContext context, Method method, Object res) {
        saveOperationLog(method, res);
        solveResHeaders();
        clearThreadLocal();
        setResponseEncoding();
        // java动态代理改变不了返回值类型，所以这个用这种方式实现不了
//        Object unifiedResponse = setUnifiedResponse(method, res);
        return super.after(context, method, res);
    }

    private Object setUnifiedResponse(Method method, Object res) {
        if (res instanceof Result<?>) {
            return res;
        }
        if (!method.getDeclaringClass().isAnnotationPresent(UnifiedResponse.class) ||
                !method.isAnnotationPresent(UnifiedResponse.class)) {
            res = GSON.toJson(Result.success(res));
        }
        return res;
    }

    private static void setResponseEncoding() {
        MvcContext mvcContext = MvcConst.MVC_CONTEXT.get();
        mvcContext.setContentType(HttpResponseUtils.ContentTypeJson);
    }

    private void solveResHeaders() {
        MvcContext mvcContext = ContextHolder.getContext().get();
        mvcContext.getResHeaders().put("traceId", TraceIdUtil.traceId() == null ? StringUtils.EMPTY : TraceIdUtil.traceId());
        if (TPC_HEADERS_URLS.contains(mvcContext.getPath())) {
            mvcContext.getResHeaders().put(TPC_HOME_URL_HEAD, Config.ins().get(TPC_HOME_URL_HEAD, "https://127.0.0.1"));
        }
    }


    @Override
    public void exception(AopContext context, Method method, Throwable ex) {
        log.error("exception,method:{}", method, ex);
        clearThreadLocal();
        super.exception(context, method, ex);
    }

    private void clearThreadLocal() {
        OperationOaContext.clear();
        MoneUserContext.clear();
        org.apache.ozhera.log.manager.common.context.MoneUserContext.clear();
    }

    private void saveUserInfoThreadLocal(MvcContext mvcContext) {
        HttpSession session = mvcContext.session();
        MoneUser userExam = (MoneUser) session.getAttribute(InnerMoneUtil.MONE_USER_INFO);
        MoneUser moneUser = null != userExam ? userExam : (MoneUser) session.getAttribute(InnerMoneUtil.MONE_USER_INFO_EXAM);
        if (null == moneUser || StringUtils.isEmpty(moneUser.getUID())) {
            String headerData = mvcContext.getHeaders().get(InnerMoneUtil.HEADER_KEY_SIGN_AND_USER_DATA);
            String headerDataExam = mvcContext.getHeaders().get(InnerMoneUtil.HEADER_KEY_SIGN_AND_USER_DATA_EXAM);
            if (StringUtils.isEmpty(headerData) && StringUtils.isEmpty(headerDataExam)) {
                printLogFrequency(Lists.newArrayList(mvcContext.getPath(), GSON.toJson(mvcContext.getParams())), results -> log.info("请求头信息为空，path:{},params:{}", results.get(0), results.get(1)));
            } else {
                printLogFrequency(Lists.newArrayList(StringUtils.isEmpty(headerData) ? headerDataExam : headerData), results -> log.info("请求头信息：{}", results.get(0)));
            }
            moneUser = InnerMoneUtil.getUserInfo(StringUtils.isEmpty(headerData) ? headerDataExam : headerData);
            if (null != moneUser) {
                session.setAttribute(InnerMoneUtil.MONE_USER_INFO, moneUser);
            } else {
                log.error("用户不存在，联系研发排查问题");
            }
            printLogFrequency(Lists.newArrayList(moneUser), results -> log.info("登陆人的信息：{}", GSON.toJson(results.get(0))));
        }

        populateUserContext(moneUser);
    }

    private static void populateUserContext(MoneUser moneUser) {
        MoneUserContext.setCurrentUser(moneUser);
        AuthUserVo userVo = new AuthUserVo();
        userVo.setUserId(moneUser.getUID());
        userVo.setEnName(moneUser.getUser());
        userVo.setName(moneUser.getDisplayName());
        userVo.setAccount(moneUser.getUser());
        userVo.setEmail(moneUser.getEmail());
        userVo.setCasUid(moneUser.getUID());
        userVo.setUserType(moneUser.getUserType());

        org.apache.ozhera.log.manager.common.context.MoneUserContext.setCurrentUser(userVo, moneUser.getIsAdmin());
    }

    private void printLogFrequency(List<Object> results, Consumer<List<Object>> consumer) {
        if (REQUEST_COUNT.get() == 1 | REQUEST_COUNT.get() % 100 == 0) {
            consumer.accept(results);
        }
    }

    private void saveRequestThreadLocal(Object[] args) {
        if (args.length > 0) {
            OperationLogRequest operationLogRequest = new OperationLogRequest();
            StringBuffer sb = new StringBuffer();
            for (Object o : args) {
                sb.append(o);
            }
            if (sb.length() < MAX_LENGTH) {
                operationLogRequest.setDataBefore(sb.toString());
            }
            OperationOaContext.setCurrentOperationOa(operationLogRequest);
        }
    }

    private void saveOperationLog(Method method, Object res) {
        final String METHOD_GET = "get";
        final String METHOD_QUERY = "query";
        final String METHOD_LIST = "List";
        final String METHOD_FILTER = "operation.log.filtration";
        final String APP_NAME = "milog";
        //流水表记录操作 && 过滤掉查询类的请求
        if (!method.getName().contains(METHOD_GET) &&
                !method.getName().contains(METHOD_QUERY) &&
                !method.getName().contains(METHOD_LIST) &&
                !Config.ins().get(METHOD_FILTER, "").contains(method.getName())) {
            try {
                OperationLogService operationLogService = Ioc.ins().getBean(OperationLogService.class);
                MoneUser currentUser = MoneUserContext.getCurrentUser();
                OperationLogRequest request = OperationOaContext.getCurrentOperationOa();
                if (null != request && null != res) {
                    request.setAppName(APP_NAME);
                    request.setUserName(currentUser.getUser());
                    request.setDataId(method.getName());
                    if (String.valueOf(res).length() > MAX_LENGTH) {
                        request.setDataAfter(String.valueOf(res).substring(0, MAX_LENGTH));
                    } else {
                        request.setDataAfter(String.valueOf(res));
                    }
                    request.setRemark("");

                    operationLogService.saveOperationLog(request);

                }
            } catch (Exception e) {
                log.error("save to mione log request response by dubbo error", e);
            }
        }
    }


}
