package com.xiaomi.mone.log.manager.model.alert;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/6/19 15:57
 */
@Data
public class AlertJobDetail {
    private int id;

    private int workflowId;

    private int version;

    private int lastVersion;

    private String region;

    private String regionName;

    private String jobName;

    private String description;

    private String jobType;

    private String jobTypeDesc;

    private String creator;

    private String owner;

    private String status;

    private boolean pause;

    private boolean jobDeleted;

    private boolean jobVersionDeleted;

    private boolean inLastWorkflowVersion;

    private Long createTime;

    private Long updateTime;

    private Long lastScheduleTime;

    private int retryTimes;

    private List<Notice> noticeList;

    private String jobStatus;

    private int jobOnlineVersion;

    private String flinkSQLVersion;

    private String jobManagerMemory;

    private String taskManagerMemory;

    private int parallelism;

    private List<String> frameworkParams;

    private List<String> arguments;

    private String jarPath;

    private String jarName;

    private String flinkVersion;

    private String mainClass;

    private String flinkCluster;

    private boolean autoScale;

    private String stateBackend;

    private String updateMode;

    @Data
    public static class Notice {
        private List<String> notifyIf;
        private Integer timeout;
        private String notifyProvider;
        private String notifyLevel;
        private Integer customConditionThreshold;
        private String retryTriggerCondition;
        private List<NotifyingReceiver> notifyingReceiver;
    }

    @Data
    public static class NotifyingReceiver {
        private String notifyObjectType;
        private List<Receiver> receivers;
    }

    @Data
    public static class Receiver {
        private String id;
        private String value;
    }

}
