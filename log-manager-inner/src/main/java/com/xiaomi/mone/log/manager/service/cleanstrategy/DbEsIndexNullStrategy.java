package com.xiaomi.mone.log.manager.service.cleanstrategy;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xiaomi.mone.enums.InnerLogTypeEnum;
import com.xiaomi.mone.log.manager.dao.InnerMilogLogStoreDao;
import com.xiaomi.mone.log.manager.model.po.InnerMilogLogStoreDO;
import com.xiaomi.mone.log.manager.model.vo.ClearDtResourceParam;
import com.xiaomi.youpin.docean.anno.Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.manager.common.exception.MilogManageException;
import org.apache.ozhera.log.manager.mapper.MilogEsIndexMapper;
import org.apache.ozhera.log.manager.model.pojo.MilogEsIndexDO;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 清理数据库es为空的情况
 *
 * @author: songyutong1
 * @date: 2024/09/13/16:22
 */
@Slf4j
@Service
public class DbEsIndexNullStrategy extends AbstractCleanStrategy {

    @Resource
    private InnerMilogLogStoreDao innerMilogLogStoreDao;

    @Resource
    private MilogEsIndexMapper esIndexMapper;

    @Override
    public void clean(ClearDtResourceParam param, String uuid) {
        log.info("uuid:{}, start fix esIndex not exist", uuid);
        List<InnerMilogLogStoreDO> doList = innerMilogLogStoreDao.queryPlatformResourceLogStoreByMachineRoom(param.getMachineRoom(), true);
        List<String> esAddInDb = new ArrayList<>();
        doList.forEach(logStoreDO -> {
            if (InnerLogTypeEnum.OPENTELEMETRY.getType().equals(logStoreDO.getLogType()) ||
                    InnerLogTypeEnum.MATRIX_ES_LOG.getType().equals(logStoreDO.getLogType()) ||
                    InnerLogTypeEnum.ORIGIN_LOG.getType().equals(logStoreDO.getLogType()) ||
                    InnerLogTypeEnum.LOKI_APP_LOG.getType().equals(logStoreDO.getLogType())) {
                return;
            }
            if (StringUtils.isEmpty(logStoreDO.getEsIndex())) {
                log.info("uuid:{}, logStore:{} esIndex is empty", uuid, logStoreDO.getId());
                return;
            }
            if (!esIndexNotExist(logStoreDO)) {
                return;
            }
            esAddInDb.add(logStoreDO.getEsIndex());
            if (Boolean.FALSE.equals(param.getClearFlag())) {
                return;
            }
            int count = insertEsIndex(logStoreDO);
            if (count < 1) {
                log.info("uuid:{}, fix esIndex not exist failed, create EsIndex failed, storeId:{}", uuid, logStoreDO.getId());
                throw new MilogManageException("fix esIndex not exist failed, create EsIndex failed, storeId:" + logStoreDO.getId());
            }
        });
        log.info("uuid:{}, fix esIndex not exist finished, esAddInDb:{}", uuid, esAddInDb);
    }


    private int insertEsIndex(InnerMilogLogStoreDO logStoreDO) {
        MilogEsIndexDO milogEsIndexDO = new MilogEsIndexDO();
        milogEsIndexDO.setClusterId(logStoreDO.getEsClusterId());
        milogEsIndexDO.setLogType(logStoreDO.getLogType());
        milogEsIndexDO.setIndexName(logStoreDO.getEsIndex());
        return esIndexMapper.insert(milogEsIndexDO);
    }

    private boolean esIndexNotExist(InnerMilogLogStoreDO logStoreDO) {
        QueryWrapper<MilogEsIndexDO> wrapper = new QueryWrapper<MilogEsIndexDO>().eq("index_name", logStoreDO.getEsIndex());
        List<MilogEsIndexDO> list = esIndexMapper.selectList(wrapper);
        return CollectionUtils.isEmpty(list);
    }

}
