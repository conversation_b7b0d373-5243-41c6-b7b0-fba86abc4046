package com.xiaomi.mone.log.manager.controller;

import com.xiaomi.mone.log.manager.model.po.AutoAccessLogParam;
import com.xiaomi.mone.log.manager.model.po.LogEnabledParam;
import com.xiaomi.mone.log.manager.model.vo.LogEnabledTreeVo;
import com.xiaomi.mone.log.manager.model.vo.LogEnabledVo;
import com.xiaomi.mone.log.manager.service.AutoAccessLogService;
import com.xiaomi.youpin.docean.anno.Controller;
import com.xiaomi.youpin.docean.anno.RequestMapping;
import com.xiaomi.youpin.docean.anno.RequestParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.common.Result;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

import static com.xiaomi.mone.log.manager.user.MoneUserDetailService.GSON;

/**
 *
 * @description 自动接入日志
 * @version 1.0
 * <AUTHOR>
 * @date 2024/7/8 16:12
 *
 */
@Controller
@Slf4j
public class AutoAccessLogController {

    @Resource
    private AutoAccessLogService autoAccessLogService;

    /**
     * 是否已经接入日志了
     * @param logEnabledParam
     * @return
     */
    @RequestMapping(path = "/access/log/enable")
    public Result<LogEnabledVo> queryLogEnabled(LogEnabledParam logEnabledParam) {
        Assert.notNull(logEnabledParam.getAppId(), "appId不能为空");
        Assert.notNull(logEnabledParam.getEnvId(), "流水线Id不能为空");
        return Result.success(autoAccessLogService.queryLogEnabled(logEnabledParam));
    }

    /**
     * 查询选择树
     * @param userName
     * @return
     */
    @RequestMapping(path = "/access/log/bind/tree", method = "get")
    public Result<List<LogEnabledTreeVo>> queryLogBindTree(@RequestParam(value = "spaceName") String spaceName, @RequestParam(value = "userName") String userName) {
        Assert.hasLength(userName, "用户名不能为空");
        return Result.success(autoAccessLogService.queryLogBindTree(spaceName, userName));
    }

    /**
     * 接入日志系统
     * @param logParam
     * @return
     */
    @RequestMapping(path = "/access/log/auto")
    public Result<String> autoAccessToLog(AutoAccessLogParam logParam) {
        Assert.notNull(logParam.getAppId(), "appId不能为空");
        Assert.notNull(logParam.getEnvId(), "流水线Id不能为空");
        Assert.hasLength(logParam.getUserName(), "用户名不能为空");
        log.info("autoAccessToLog param:{}", GSON.toJson(logParam));
        return Result.success(autoAccessLogService.autoAccessToLog(logParam));
    }

}
