package com.xiaomi.mone.log.manager.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class MigrateDtResourceConfig {
    //工场空间 token
    String alphaToken;
    String talosServiceUrl;
    String talosAk;
    String talosSk;
    String talosCatalog;
    String esCatalog;
    String esAddr;
    String esDatabase;
    String suffix;

    // 待迁移的 store 所属的 space
    Long spaceId;
    // 待迁移的 store
    Long storeId;
    // 待迁移的 store 所在的集群
    String machineRoom;
    // 待迁移的 es 集群 id
    Long esClusterId;

    // 资源复制开关: 具有一致性。不会重复创建资源。
    Boolean enableEs;
    Boolean enableTalos;
    // 用来提醒的开关
    /* MySQL 工单格式：
     * UPDATE milog_middleware_config
     * SET name_server = '',
     * service_url = '',
     * ak = '',
     * sk = '',
     * token='',
     * dt_catalog=''
     * WHERE id = 90017;
     *
     * #更新 es 集群地址
     * update `milog_es_cluster`
     * set cluster_name='',
     * addr = '',
     * user='',
     * pwd='',
     * token='',
     * dt_catalog=''
     * where id=90014;
     *
     * #更新 logstore 表中的索引名为 prod_hera_index_92647_后缀
     * UPDATE `milog_logstore` SET es_index = CONCAT(es_index, '_1') WHERE
     * machine_room="shgs1-nc4-303";
     */
    Boolean doneMySQLOrder;
    // 切流开关：具有一致性。如果 MySQL 工单未执行，则操作不生效。
    Boolean enableNacosRefresh;
}
