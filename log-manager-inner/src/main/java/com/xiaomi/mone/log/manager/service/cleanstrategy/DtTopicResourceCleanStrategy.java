package com.xiaomi.mone.log.manager.service.cleanstrategy;

import com.xiaomi.mone.log.manager.dao.InnerLogMiddlewareConfigDao;
import com.xiaomi.mone.log.manager.dao.InnerMilogLogStoreDao;
import com.xiaomi.mone.log.manager.model.dto.dt.DtTableInfoDTO;
import com.xiaomi.mone.log.manager.model.po.InnerMilogLogStoreDO;
import com.xiaomi.mone.log.manager.model.vo.ClearDtResourceParam;
import com.xiaomi.mone.log.manager.service.InnerTailExtensionService;
import com.xiaomi.mone.log.manager.service.remoting.DtRemoteService;
import com.xiaomi.youpin.docean.anno.Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.common.Config;
import org.apache.ozhera.log.manager.common.exception.MilogManageException;
import org.apache.ozhera.log.manager.dao.MilogAppMiddlewareRelDao;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.model.bo.LogTailParam;
import org.apache.ozhera.log.manager.model.pojo.MilogAppMiddlewareRel;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;
import org.apache.ozhera.log.manager.model.pojo.MilogMiddlewareConfig;
import org.apache.ozhera.log.manager.service.bind.LogTypeProcessor;
import org.apache.ozhera.log.manager.service.bind.LogTypeProcessorFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.INNER_TAIL_SERVICE;

/**
 * 清理工场topic资源与数据不一致
 *
 * @author: songyutong1
 * @date: 2024/09/13/16:48
 */
@Service
@Slf4j
public class DtTopicResourceCleanStrategy extends AbstractCleanStrategy {

    @Resource
    private InnerLogMiddlewareConfigDao innerLogMiddlewareConfigDao;

    @Resource
    private MilogAppMiddlewareRelDao milogAppMiddlewareRelDao;

    @Resource
    private InnerMilogLogStoreDao innerMilogLogStoreDao;

    @Resource
    private DtRemoteService dtRemoteService;

    @Resource
    private MilogLogTailDao milogLogtailDao;

    @Resource
    private LogTypeProcessorFactory logTypeProcessorFactory;
    private LogTypeProcessor logTypeProcessor;

    public void init() {
        logTypeProcessor = logTypeProcessorFactory.getLogTypeProcessor();
    }

    @Resource(name = INNER_TAIL_SERVICE)
    private InnerTailExtensionService tailExtensionService;


    @Override
    public void clean(ClearDtResourceParam param, String uuid) {
        log.info("uuid:{}, start clean up topic diff in dt and db", uuid);
        MilogMiddlewareConfig milogMiddlewareConfig = innerLogMiddlewareConfigDao.queryPlatformTalosConfigByRegion(param.getMachineRoom());
        if (ObjectUtils.isEmpty(milogMiddlewareConfig)) {
            log.info("uuid:{}, clean up topic diff in dt and db failed, talosConfig not exist, please check the machineRoom:{}", uuid, param.getMachineRoom());
            throw new MilogManageException("clean up topic diff in dt and db failed, talosConfig not exist, please check the machineRoom:" + param.getMachineRoom());
        }
        addTopicInDt(milogMiddlewareConfig, param, uuid);
        deleteTopicInDt(milogMiddlewareConfig, param, uuid);
    }

    /**
     * 补齐工场中缺少的topic
     */
    public void addTopicInDt(
            MilogMiddlewareConfig milogMiddlewareConfig,
            ClearDtResourceParam param,
            String uuid) {
        List<MilogAppMiddlewareRel> middlewareRelList = milogAppMiddlewareRelDao.queryByCondition(null, milogMiddlewareConfig.getId(), null);
        List<DtTableInfoDTO> dtTableList = dtRemoteService.queryDtTableList(milogMiddlewareConfig.getDtCatalog(), milogMiddlewareConfig.getDtDatabase(), false, true, "", "TALOS", param.getToken());

        HashSet<String> topicSet = dtTableList.stream().map(DtTableInfoDTO::getTableNameEn).collect(Collectors.toCollection(HashSet::new));
        List<String> topicAddInDt = new ArrayList<>();
        middlewareRelList.forEach(middlewareRel -> {
            if (topicSet.contains(middlewareRel.getConfig().getTopic())) {
                return;
            }
            if (StringUtils.isBlank(middlewareRel.getConfig().getTopic())) {
                log.error("uuid:{}, topic is empty, please check the middlewareRel:{}, continue clean", uuid, middlewareRel.getId());
                return;
            }
            topicAddInDt.add(middlewareRel.getConfig().getTopic());
            if (Boolean.FALSE.equals(param.getClearFlag())) {
                return;
            }
            try {
                int maxRetries = 3; // 最大重试次数
                int retryCount = 0; // 当前重试次数
                boolean success = false;

                while (!success && retryCount < maxRetries) {
                    success = dtRemoteService.createTalosTable(
                            milogMiddlewareConfig.getDtCatalog(),
                            milogMiddlewareConfig.getDtDatabase(),
                            middlewareRel.getConfig().getTopic(),
                            milogMiddlewareConfig.getToken(),
                            "talos topic for hera logtail: " + middlewareRel.getTailId());
                    if (!success) {
                        retryCount++;
                        log.warn("uuid:{}, create talos topic into dt failed, topic:{}, retrying {}/{}", uuid, middlewareRel.getConfig().getTopic(), retryCount, maxRetries);
                        try {
                            Thread.sleep(10000); // 等待10秒后重试
                        } catch (InterruptedException e) {
                            log.error("Thread sleep interrupted", e);
                            break;
                        }
                    }
                }

                if (!success) {
                    log.error("uuid:{}, create talos topic into dt failed after {} retries, topic:{}", uuid, maxRetries, middlewareRel.getConfig().getTopic());
                    //throw new MilogManageException("create talos topic into dt failed after " + maxRetries + " retries, topic:" + middlewareRel.getConfig().getTopic());
                    return;
                }

                topicSet.add(middlewareRel.getConfig().getTopic());

                // 获取logTail
                List<MilogLogTailDo> tailList = milogLogtailDao.getMilogLogtail(Collections.singletonList(middlewareRel.getMilogAppId()));
                if (ObjectUtils.isEmpty(tailList)) {
                    return;
                }
                MilogLogTailDo logTailDo = tailList.getFirst();

                LogTailParam logTailparam = LogTailParam.builder()
                        .spaceId(logTailDo.getSpaceId())
                        .storeId(logTailDo.getId())
                        .middlewareConfigId(milogMiddlewareConfig.getId())
                        .collectionReady(logTailDo.getCollectionReady())
                        .build();

                // 获取logStore
                InnerMilogLogStoreDO logStoreDO = innerMilogLogStoreDao.queryById(logTailDo.getStoreId());

                boolean supportedConsume = logTypeProcessor.supportedConsume(logStoreDO.getLogType());
                tailExtensionService.sendMessageOnCreate(logTailparam, logTailDo, logTailDo.getMilogAppId(), supportedConsume);
                log.info("uuid:{}, send config to agent finished, tail id:{}", uuid, logTailDo.getId());
            } catch (Exception e) {
                log.error("uuid:{}, create talos topic into dt failed, topic:{}, err:{}", uuid, middlewareRel.getConfig().getTopic(), e.getMessage(), e);
            }
        });
        log.info("uuid:{}, add talos topic into dt:{}", uuid, topicAddInDt);
    }

    /**
     * 删除工场中多余的topic
     */
    public void deleteTopicInDt(
            MilogMiddlewareConfig milogMiddlewareConfig,
            ClearDtResourceParam param,
            String uuid) {
        List<MilogAppMiddlewareRel> middlewareRelList = milogAppMiddlewareRelDao.queryByCondition(null, milogMiddlewareConfig.getId(), null);
        List<DtTableInfoDTO> dtTableList = dtRemoteService.queryDtTableList(milogMiddlewareConfig.getDtCatalog(), milogMiddlewareConfig.getDtDatabase(), false, true, "", "TALOS", param.getToken());

        HashSet<String> middlewareSet = middlewareRelList.stream()
                .map(MilogAppMiddlewareRel::getConfig)
                .map(MilogAppMiddlewareRel.Config::getTopic)
                .collect(Collectors.toCollection(HashSet::new));
        List<String> topicDeleteInDt = new ArrayList<>();
        dtTableList.forEach(dtTable -> {
            String prefix = Config.ins().get("hera_topic_prefix", "hera_topic_");
            if (middlewareSet.contains(dtTable.getTableNameEn()) || !(dtTable.getTableNameEn().contains(prefix) || dtTable.getTableNameEn().contains("prod_matrix_hera_"))) {
                return;
            }
            topicDeleteInDt.add(dtTable.getTableNameEn());
            if (Boolean.FALSE.equals(param.getClearFlag())) {
                return;
            }
            try {
                boolean success = dtRemoteService.deleteDtTable(
                        milogMiddlewareConfig.getDtCatalog(),
                        milogMiddlewareConfig.getDtDatabase(),
                        dtTable.getTableNameEn(),
                        milogMiddlewareConfig.getToken()
                );
                if (!success) {
                    log.info("uuid:{}, delete talos topic from dt failed, topic:{}", uuid, dtTable.getTableNameEn());
                    //throw new MilogManageException("delete talos topic from dt failed, topic:" + dtTable.getTableNameEn());
                }
            } catch (Exception e) {
                log.error("uuid:{}, delete talos topic from dt failed, topic:{}, err:{}", uuid, dtTable.getTableNameEn(), e.getMessage(), e);
                //throw new MilogManageException(e);
            }
        });
        log.info("uuid:{}, delete talos topic from dt:{}", uuid, topicDeleteInDt);
    }

}
