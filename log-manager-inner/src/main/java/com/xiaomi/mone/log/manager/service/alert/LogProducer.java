package com.xiaomi.mone.log.manager.service.alert;


import com.google.gson.Gson;
import com.xiaomi.infra.galaxy.rpc.thrift.Credential;
import com.xiaomi.infra.galaxy.rpc.thrift.UserType;
import com.xiaomi.infra.galaxy.talos.client.SimpleTopicAbnormalCallback;
import com.xiaomi.infra.galaxy.talos.producer.*;
import com.xiaomi.infra.galaxy.talos.thrift.Message;
import com.xiaomi.youpin.docean.anno.Service;
import libthrift091.TException;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.api.model.msg.LineMessage;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Properties;

/**
 * <AUTHOR>
 */

@Slf4j
@Service
public class LogProducer {

    private Gson gson = new Gson();

    public boolean produceLogs(String accessKey, String secretKey, String topicName, String mqServer, String logBody, int numOfLogs) {
        try {
            Properties pros = new Properties();
            pros.setProperty("galaxy.talos.service.endpoint", mqServer);
            //disable falcon client
            pros.setProperty("galaxy.talos.client.falcon.monitor.switch", "false");

            TalosProducerConfig producerConfig = new TalosProducerConfig(pros);
            Credential credential = new Credential();
            credential.setSecretKeyId(accessKey)
                .setSecretKey(secretKey)
                .setType(UserType.DEV_XIAOMI);


            TalosProducer producer = new TalosProducer(producerConfig, credential,
                topicName, new SimpleTopicAbnormalCallback(), new MyMessageCallback());


            List<Message> messageList = new ArrayList<>(numOfLogs);
            for (int i = 0; i < numOfLogs; ++i) {
                long time = System.currentTimeMillis();

                LineMessage line = new LineMessage();
                line.setExtMap(new HashMap<>());
                line.setTimeStamp(time);
                line.setMsgBody(logBody);
                line.setMsgLength(logBody.length());


                Message message = new Message(ByteBuffer.wrap(gson.toJson(line).getBytes()));
                messageList.add(message);
            }
            producer.addUserMessage(messageList);

            producer.shutdown();
            return true;
        } catch (ProducerNotActiveException e) {
            log.error(e.toString());
        } catch (TException e) {
            log.error(e.toString());
        } catch (Exception e) {
            log.error(e.toString());
        }
        return false;
    }

    private class MyMessageCallback implements UserMessageCallback {
        // count when success
        @Override
        public void onSuccess(UserMessageResult userMessageResult) {

        }

        // retry when failed
        @Override
        public void onError(UserMessageResult userMessageResult) {

        }
    }
}
