package com.xiaomi.mone.log.manager.service;

import cn.hutool.core.util.StrUtil;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.xiaomi.mone.enums.InnerMachineRegionEnum;
import com.xiaomi.mone.enums.InnerMiddlewareEnum;
import com.xiaomi.mone.enums.InnerProjectTypeEnum;
import com.xiaomi.mone.log.manager.common.ManagerConfig;
import com.xiaomi.mone.log.manager.dao.InnerLogTailDao;
import com.xiaomi.youpin.docean.anno.Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.dao.MilogLogstoreDao;
import org.apache.ozhera.log.manager.model.pojo.MilogLogStoreDO;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;
import org.apache.ozhera.log.manager.model.vo.LogQuery;
import org.apache.ozhera.log.manager.service.extension.common.CommonExtensionService;
import org.apache.ozhera.log.manager.service.extension.common.DefaultCommonExtensionService;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermQueryBuilder;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.INNER_COMMON_SERVICE;
import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.SPACE_DATA_ID_MAP;
import static org.apache.ozhera.log.common.Constant.DEFAULT_STREAM_SERVER_NAME;
import static org.apache.ozhera.log.common.Constant.LOG_MANAGE_PREFIX;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/4/27 16:51
 */
@Service(name = INNER_COMMON_SERVICE)
@Slf4j
public class InnerCommonExtensionService implements CommonExtensionService {

    private static final String INNER_PREFIX = "inner_";

    @Resource
    private MilogLogTailDao milogLogtailDao;

    @Resource
    private InnerLogTailDao innerLogTailDao;

    @Resource
    private MilogLogstoreDao milogLogstoreDao;

    @Resource
    private ManagerConfig managerConfig;

    private static final Cache<Long, Boolean> MIFE_CACHE_LOCAL = CacheBuilder.newBuilder()
            .maximumSize(200)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build();

    @Override
    public String getLogManagePrefix() {
        return String.format("%s", LOG_MANAGE_PREFIX);
    }

    @Override
    public String getHeraLogStreamServerName() {
        return String.format("%s%s", INNER_PREFIX, DEFAULT_STREAM_SERVER_NAME);
    }

    @Override
    public String getMachineRoomName(String machineRoomEn) {
        return InnerMachineRegionEnum.queryCnByEn(machineRoomEn);
    }

    @Override
    public boolean middlewareEnumValid(Integer type) {
        return InnerMiddlewareEnum.ROCKETMQ.getCode().equals(type) ||
                InnerMiddlewareEnum.TALOS.getCode().equals(type) ||
                InnerMiddlewareEnum.PLATFORM_DEFAULT_TALOS.getCode().equals(type);
    }

    @Override
    public BoolQueryBuilder commonRangeQuery(LogQuery logQuery) {
        if (matchMifeSpace(logQuery.getStoreId())) {
            Pair<Object, Object> timeRangePair = checkAndBuildTimeRangeOfMiFELog(logQuery);
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("time").from(timeRangePair.getLeft()).to(timeRangePair.getRight()));
            return boolQueryBuilder;
        }
        return getCommonBoolQueryBuilder(logQuery);
    }

    @Override
    public String getSortedKey(LogQuery logQuery, String sortedKey) {
        if (matchMifeSpace(logQuery.getStoreId())) {
            return "time";
        }
        return sortedKey;
    }

    @Override
    public TermQueryBuilder multipleChooseBuilder(DefaultCommonExtensionService.QueryTypeEnum queryTypeEnum, Long storeId, String chooseVal) {
        if (matchMifeSpace(storeId)) {
            if (StrUtil.isNumeric(chooseVal)) {
                MilogLogTailDo milogLogTailDo = milogLogtailDao.queryById(Long.valueOf(chooseVal));
                return QueryBuilders.termQuery("http_host", milogLogTailDo.getTail());
            }
            return QueryBuilders.termQuery("http_host", chooseVal);
        }
        if (DefaultCommonExtensionService.QueryTypeEnum.ID == queryTypeEnum) {
            return QueryBuilders.termQuery("tailId", chooseVal);
        }
        return QueryBuilders.termQuery("tail", chooseVal);
    }

    @Override
    public String queryDateHistogramField(Long storeId) {
        if (matchMifeSpace(storeId)) {
            return "time";
        }
        return "timestamp";
    }

    @Override
    public String getSearchIndex(Long storeId, String esIndexName) {
        Boolean esIndexCache = MIFE_CACHE_LOCAL.getIfPresent(storeId);
        if (null == esIndexCache) {
            MIFE_CACHE_LOCAL.put(storeId, matchMifeSpace(storeId));
        }
        if (MIFE_CACHE_LOCAL.getIfPresent(storeId)) {
            return String.format("%s*", esIndexName);
        }
        return esIndexName;
    }

    @Override
    public String getSpaceDataId(Long spaceId) {
        if (managerConfig.isStaging()) {
            return SPACE_DATA_ID_MAP.get(InnerProjectTypeEnum.MIONE_TYPE.getType());
        }
        List<String> machines = queryMachinesBySpaceId(spaceId);
        if (CollectionUtils.isNotEmpty(machines)
                && machines.size() == 1
                && machines.getFirst().equals(InnerMachineRegionEnum.CN_MACHINE.getEn())) {
            if (matchMifeSpaceBySpaceId(spaceId)) {
                return SPACE_DATA_ID_MAP.get(InnerProjectTypeEnum.MIFE_TYPE.getType());
            }
            if (matchMatrixSpace(spaceId)) {
                return SPACE_DATA_ID_MAP.get(InnerProjectTypeEnum.MATRIX_TYPE.getType());
            }
        }
        return SPACE_DATA_ID_MAP.get(InnerProjectTypeEnum.MIONE_TYPE.getType());
    }

    @Override
    public List<String> queryMachineRegions() {
        return Arrays.stream(InnerMachineRegionEnum.values()).map(InnerMachineRegionEnum::getEn).collect(Collectors.toList());
    }

    private boolean matchMifeSpace(Long storeId) {
        List<MilogLogTailDo> logTailDos = milogLogtailDao.queryTailsByStoreId(storeId);
        if (CollectionUtils.isNotEmpty(logTailDos)) {
            return logTailDos.stream()
                    .allMatch(tail ->
                            Objects.equals(InnerProjectTypeEnum.MIFE_TYPE.getCode(), tail.getAppType()));
        }
        return false;
    }

    private boolean matchSpaceByQuery(Long spaceId, Predicate<MilogLogTailDo> predicate) {
        List<MilogLogTailDo> logTailDos = innerLogTailDao.queryTailsBySpace(spaceId);
        return CollectionUtils.isNotEmpty(logTailDos) && logTailDos.stream().anyMatch(predicate);
    }

    private boolean matchMifeSpaceBySpaceId(Long id) {
        return matchSpaceByQuery(id, tail -> Objects.equals(InnerProjectTypeEnum.MIFE_TYPE.getCode(), tail.getAppType()));
    }

    public boolean matchMatrixSpace(Long id) {
        return matchSpaceByQuery(id, tail -> Objects.equals(InnerProjectTypeEnum.MATRIX_TYPE.getCode(), tail.getAppType()));
    }


    private static BoolQueryBuilder getCommonBoolQueryBuilder(LogQuery logQuery) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("timestamp").from(logQuery.getStartTime()).to(logQuery.getEndTime()));
        boolQueryBuilder.filter(QueryBuilders.termQuery("storeId", logQuery.getStoreId()));
//        boolQueryBuilder.filter(QueryBuilders.termQuery("logstore", logQuery.getLogstore()));
        return boolQueryBuilder;
    }

    public List<String> queryMachinesBySpaceId(Long spaceId) {
        List<MilogLogStoreDO> logStoreDOS = milogLogstoreDao.getMilogLogstoreBySpaceId(spaceId);
        return logStoreDOS.stream().map(MilogLogStoreDO::getMachineRoom).collect(Collectors.toList());
    }


    private Pair<Object, Object> checkAndBuildTimeRangeOfMiFELog(LogQuery logQuery) {
        log.info("enter checkAndBuildTimeRangeOfMiFELog,startTime={},endTime={}", logQuery.getStartTime(), logQuery.getEndTime());
        // only search latest 3 days mife log
        if (LocalDate.now().atStartOfDay(ZoneId.systemDefault()).minusDays(2).toInstant().compareTo(Instant.ofEpochMilli(logQuery.getStartTime())) > 0) {
            throw new IllegalArgumentException("Wrong time range, cannot search MiFE log 3 days ago");
        }
        // using ISO time format in order to be compatible with az-pn search
        String startTimeStr = DateTimeFormatter.ISO_INSTANT.format(Instant.ofEpochMilli(logQuery.getStartTime()));
        String endTimeStr = DateTimeFormatter.ISO_INSTANT.format(Instant.ofEpochMilli(logQuery.getEndTime()));
        log.info("exit checkAndBuildTimeRangeOfMiFELog,startTime={},endTime={}", startTimeStr, endTimeStr);
        return Pair.of(startTimeStr, endTimeStr);
    }
}
