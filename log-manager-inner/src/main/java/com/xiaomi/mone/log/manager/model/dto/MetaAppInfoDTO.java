package com.xiaomi.mone.log.manager.model.dto;

import com.google.gson.annotations.SerializedName;
import com.xiaomi.mione.tesla.k8s.bo.LogAgentListBo;
import lombok.Data;
import org.apache.flink.util.CollectionUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 应用信息
 * @date 2024/8/16 20:00
 */
@Data
public class MetaAppInfoDTO {
    @SerializedName("app_id")
    private int appId;
    @SerializedName("app_name")
    private String appName;
    @SerializedName("source_app_type")
    private String sourceAppType;
    @SerializedName("source_app_node")
    private String sourceAppNode;
    @SerializedName("app_platform")
    private String appPlatform;
    @SerializedName("server_alive_ips")
    private List<ServerInstance> serverAliveIps;
    @SerializedName("server_dead_ips")
    private List<ServerInstance> serverDeadIps;
    @SerializedName("extra")
    private Extra extra;

    @Data
    public static class ServerInstance {
        @SerializedName("server_ip")
        private String serverIp;
        @SerializedName("server_host")
        private String serverHost;
        @SerializedName("instance_extra")
        private InstanceExtra instanceExtra;

        public LogAgentListBo toLogAgentListBo() {
            LogAgentListBo pod = new LogAgentListBo();
            pod.setPodName(serverHost);
            pod.setPodIP(serverIp);
            pod.setAgentIP(instanceExtra.nodeIp);
            pod.setAgentName(instanceExtra.nodeHost);
            return pod;
        }
    }

    @Data
    public static class Extra {
        @SerializedName("log_group_key")
        private String logGroupKey;
        @SerializedName("log_namespace")
        private String logNamespace;
    }

    @Data
    public static class InstanceExtra {
        @SerializedName("node_ip")
        private String nodeIp;
        @SerializedName("node_host")
        private String nodeHost;
        @SerializedName("miks_cluster")
        private String miksCluster;
    }

    public List<LogAgentListBo> getPodsForCluster(String cluster, boolean alive) {
        List<ServerInstance> filterList = serverAliveIps;
        if (!alive) {
            filterList = serverDeadIps;
        }
        if (CollectionUtil.isNullOrEmpty(filterList)) {
            return null;
        }
        return filterList.stream().filter(ip -> ip != null && ip.getInstanceExtra() != null &&
                        ip.getInstanceExtra().getMiksCluster() != null &&
                        ip.getInstanceExtra().getMiksCluster().equals(cluster)).
                map(ServerInstance::toLogAgentListBo).collect(Collectors.toList());
    }

    public List<LogAgentListBo> getPodsByPodIps(List<String> podIps) {
        List<ServerInstance> filterList = new ArrayList<>(serverAliveIps);
        filterList.addAll(serverDeadIps);
        return  filterList.stream()
                .filter(serverInstance -> podIps.contains(serverInstance.getServerIp()))
                .map(ServerInstance::toLogAgentListBo)
                .collect(Collectors.toList());
    }

    @Data
    public static class MiKSAppData {
        private Long id;
        private String name;
        private Long treeId;
        private List<Env> clusters;

        public MiKSAppData() {
            this.clusters = new ArrayList<>();
        }
    }

    @Data
    public static class Env {
        private Long id;
        private String name;
        private List<String> ips;
    }

    @Data
    public static class LogInstanceGroup {
        @SerializedName("name")
        private String name;
        @SerializedName("value")
        private String value;
        @SerializedName("tracing_area")
        private String tracingArea;
        @SerializedName("log_area")
        private String logArea;
        @SerializedName("ips")
        private List<String> ips;

        public MetaAppInfoDTO.Env toEnv() {
            MetaAppInfoDTO.Env env = new MetaAppInfoDTO.Env();
            env.setName(value);
            env.setIps(ips);
            return env;
        }
    }
}
