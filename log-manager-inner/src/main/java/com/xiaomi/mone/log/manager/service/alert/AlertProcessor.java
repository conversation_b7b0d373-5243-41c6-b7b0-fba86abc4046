package com.xiaomi.mone.log.manager.service.alert;

import cn.hutool.core.date.DateUtil;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.xiaomi.hera.trace.annotation.Trace;
import com.xiaomi.infra.galaxy.rpc.thrift.Credential;
import com.xiaomi.infra.galaxy.rpc.thrift.UserType;
import com.xiaomi.infra.galaxy.talos.client.SimpleTopicAbnormalCallback;
import com.xiaomi.infra.galaxy.talos.consumer.*;
import com.xiaomi.infra.galaxy.talos.thrift.MessageAndOffset;
import com.xiaomi.infra.galaxy.talos.thrift.TopicAndPartition;
import com.xiaomi.mone.cache.redis.RedisClientFactory;
import com.xiaomi.mone.log.manager.model.alert.Alert;
import com.xiaomi.mone.log.manager.model.alert.AlertCondition;
import com.xiaomi.mone.log.manager.model.alert.AlertRule;
import com.xiaomi.mone.log.manager.model.dto.TraceAppInfoDTO;
import com.xiaomi.mone.log.manager.service.impl.MatrixLogServiceImpl;
import com.xiaomi.mone.log.manager.service.impl.MilogAppTopicServiceImpl;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import libthrift091.TException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.api.model.bo.AlertInfo;
import org.apache.ozhera.log.api.model.msg.LineMessage;
import org.apache.ozhera.log.common.Config;
import org.apache.ozhera.log.manager.common.Utils;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.dao.MilogLogstoreDao;
import org.apache.ozhera.log.manager.dao.MilogSpaceDao;
import org.apache.ozhera.log.manager.model.pojo.MilogLogStoreDO;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;
import org.apache.ozhera.log.manager.model.pojo.MilogSpaceDO;
import org.apache.ozhera.log.manager.model.vo.LogPathTopicVo;
import org.apache.ozhera.log.manager.service.impl.LogTailServiceImpl;
import org.apache.ozhera.log.parse.AbstractLogParser;
import org.apache.ozhera.log.parse.LogParser;
import org.apache.ozhera.log.parse.LogParserFactory;
import redis.clients.jedis.JedisCluster;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Clock;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.xiaomi.mone.constant.RedisConstants.HERA_LOG_ALTER_KEY;
import static com.xiaomi.mone.constant.RedisConstants.SET_NX_SUCCESS;
import static org.apache.ozhera.log.common.Constant.*;
import static org.apache.ozhera.log.parse.LogParser.esKeyMap_timestamp;

/**
 * <AUTHOR>
 */

@Slf4j
@Service
public class AlertProcessor {
    @Value(value = "$team.access_key")
    private String accessKey;

    @Value(value = "$team.secret_key")
    private String secretKey;

    @Value(value = "$producer.topic")
    private String producerTopic;

    @Value(value = "$producer.server")
    private String producerServer;

    @Value(value = "$server.type")
    private String serverType;

    @Value(value = "$hera.url")
    private String heraUrl;

    @Resource
    private AlertService alertService;

    @Resource
    private FeishuService feishuService;

    @Resource
    private AlertLogService alertLogService;

    @Resource
    private MilogAppTopicServiceImpl milogAppTopicService;

    @Resource
    private MilogLogTailDao milogLogtailDao;

    @Resource
    private MilogLogstoreDao milogLogstoreDao;

    @Resource
    private MilogSpaceDao milogSpaceDao;

    @Resource
    private DefaultSendAlertCardService sendAlertCardService;

    @Resource
    private Gson gson;

    @Resource
    private LogTailServiceImpl logTailService;

    @Resource
    private MatrixLogServiceImpl matrixLogService;

    private static final String COUNT_MAX_LIMIT = "countMaxLimit";

    private static final String LOG_API = "/open/api/milog/config/log/path";

    private TalosConsumer talosConsumer;

    private final JedisCluster jedisCluster = RedisClientFactory.getJedisCluster();

    private static final int ALERT_QUEUE_CAPACITY = 5000;
    private LinkedBlockingQueue<byte[]> alertQueue = new LinkedBlockingQueue<>(ALERT_QUEUE_CAPACITY);

    private static final Cache<Long, Optional<AlertRule>> ALERT_RULE_CACHE_LOCAL = CacheBuilder.newBuilder()
            .maximumSize(200)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build();

    private static final Cache<Long, Alert> ALERT_CACHE_LOCAL = CacheBuilder.newBuilder()
            .maximumSize(200)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build();

    private static final Cache<Long, List<AlertCondition>> ALERT_CONDITIONS_LOCAL = CacheBuilder.newBuilder()
            .maximumSize(200)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build();

    private static final Cache<Long, LogParser> TAIL_PARSER_CACHE = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();

    private static final Cache<String, Pattern> SCRIPT_COMPILE_CACHE = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();

    @PostConstruct
    public void init() throws TException {
        if (serverType != null && serverType.equals("local") && System.getenv("debug") == null) {
            return;
        }

        Properties pros = new Properties();
        pros.setProperty("galaxy.talos.service.endpoint", producerServer);
        // disable falcon client
        pros.setProperty("galaxy.talos.client.falcon.monitor.switch", "true");

        // 从头开始消费，去除积压
        pros.setProperty("galaxy.talos.consumer.start.reset.offset.value", "-2");
        pros.setProperty("galaxy.talos.consumer.start.whether.reset.offset", "true");

        // pros.setProperty("galaxy.talos.consumer.max.fetch.records", "1600");
        // pros.setProperty("galaxy.talos.consumer.fetch.interval.ms", "200");
        TalosConsumerConfig consumerConfig = new TalosConsumerConfig(pros);

        // credential
        Credential credential = new Credential();
        credential.setSecretKeyId(accessKey).setSecretKey(secretKey).setType(UserType.DEV_XIAOMI);

        String consumerGroup = "milog_" + "alert_processor" + "_" + serverType;

        talosConsumer = new TalosConsumer(consumerGroup, consumerConfig, credential, producerTopic, new MyMessageProcessorFactory(), "mione-", new SimpleTopicAbnormalCallback());
        log.info("init talos consumer,topic:{},consumerGroup:{}", producerTopic, consumerGroup);
        Runtime.getRuntime().addShutdownHook(new Thread(talosConsumer::shutDown));
    }

    @Trace
    public void handleAlertMsg(String content) {
        try {
            log.debug("alert content：{}", content);
            AlertInfo alertInfo = gson.fromJson(content, AlertInfo.class);
//            Alert alert = alertService.getAlert(alertInfo.getAlertId());
            Alert alert = ALERT_CACHE_LOCAL.getIfPresent(alertInfo.getAlertId());
            if (null == alert) {
                alert = alertService.getAlert(alertInfo.getAlertId());
                if (null == alert) {
                    log.error("handleAlertMsg alertId:{} not found", alertInfo.getAlertId());
                    return;
                }
                ALERT_CACHE_LOCAL.put(alertInfo.getAlertId(), alert);
            }

            String value = alertInfo.getInfo("count");
            int count = StringUtils.isEmpty(value) ? -1 : Integer.parseInt(value);
            String regex = alertInfo.getInfo("regex");
            String timeStamp = alertInfo.getInfo(esKeyMap_timestamp);
            String tag = alertInfo.getInfo(LineMessage.KEY_MQ_TOPIC_TAG);
            String tailId = StringUtils.substringAfterLast(tag, "_");
            if (StringUtils.isEmpty(tailId)) {
                String tailIdStr = alert.getArgument(TAILID_KEY);
                List<Long> tailIds = Arrays.stream(tailIdStr.split(SYMBOL_COMMA)).map(Long::valueOf).toList();
                tailId = tailIds.get(0).toString();
            }

//            List<AlertCondition> alertConditions = alertService.getAllConditions(alertInfo.getRuleId());
            List<AlertCondition> alertConditions = ALERT_CONDITIONS_LOCAL.get(alertInfo.getRuleId(), () -> alertService.getAllConditions(alertInfo.getRuleId()));
            AlertCondition matchedCondition = checkCondition(alertConditions, count);
            Optional<AlertRule> alertRule = ALERT_RULE_CACHE_LOCAL.get(alertInfo.getRuleId(), () -> Optional.ofNullable(alertService.getRule(alertInfo.getRuleId())));
//            AlertRule alertRule = alertService.getRule(alertInfo.getRuleId());
            if (matchedCondition != null && alertRule.isPresent()) {
                long lastAlertTime = matchedCondition.getSendAlertTime();
                String[] logFormat = getLogFormat(alert);
                String traceId = parseTraceId(alertInfo.getInfo("log"), logFormat);

                MilogLogTailDo milogLogtailDo = milogLogtailDao.queryById(Long.valueOf(tailId));
                MilogLogStoreDO logStoreDO = milogLogstoreDao.queryById(milogLogtailDo.getStoreId());
                MilogSpaceDO spaceDO = milogSpaceDao.queryById(milogLogtailDo.getSpaceId());

                if (canSendAlert(alertInfo, lastAlertTime, matchedCondition.getPeriod(), logStoreDO, milogLogtailDo)) {

                    Map<String, Object> analysisLogMap = analysisLog(alertInfo, milogLogtailDo, logStoreDO);

                    TraceAppInfoDTO traceInfo = matrixLogService.getTraceBaseInfoByAppId(Long.valueOf(alert.getApp()));
                    if (ObjectUtils.isEmpty(traceInfo)) {
                        log.info("alert:{} app info is empty", alertInfo.getAlertId());
                    }

                    String logParam = "";
                    if (null != analysisLogMap) {
                        if (analysisLogMap.containsKey(TRACE_ID_KEY)) {
                            traceId = analysisLogMap.get(TRACE_ID_KEY).toString();
                        } else if (analysisLogMap.containsKey("trace_id")) {
                            traceId = analysisLogMap.get("trace_id").toString();
                        }
                        logParam = generateLogParam(spaceDO.getId(), logStoreDO, milogLogtailDo.getTail(), analysisLogMap, alertInfo);
                    }

                    log.info("log href param:{},content:{}", logParam, content);
                    log.info("send feishu alert,alertRule:{},matchedCondition:{}", gson.toJson(alertRule), gson.toJson(matchedCondition));
                    alertService.updateSendAlertTime(matchedCondition);
                    sendAlertCardService.sendAlertCard(alertInfo, alert, matchedCondition, alertRule.get(), traceId, timeStamp, logParam, alertInfo.getInfo("log"), milogLogtailDo.getLogPath(), milogLogtailDo.getDeploySpace(), traceInfo, Long.valueOf(tailId));
                }
                if (serverType != null && !serverType.equals("local")) {
                    alertLogService.processAlertLog(alert, alertInfo.getInfo("ip"), matchedCondition, regex, alertRule.get(), traceId);
                }
            }
        } catch (Exception e) {
            log.error("consume milog alert error,content:{},error:", content, e);
        }
    }

    private Long setAlertAlreadySent(AlertInfo alertInfo) throws NoSuchAlgorithmException {
        // 日志告警存redis的过期时间
        Long expireDuration = Long.valueOf(Config.ins().get("alert.expire.duration", "1314900000"));
        String lineNumStr = alertInfo.getInfo("lineNumber");
        String fileName = alertInfo.getInfo("fileName");
        // 计算日志整体hash
        String alertHash = getLogHash(alertInfo.getInfo("log"));
        // 组装redis key
        String redisKey = HERA_LOG_ALTER_KEY + alertInfo.getAlertId() + ":" + fileName + ":" + lineNumStr + ":" + alertHash;
        if (null == jedisCluster) {
            throw new RuntimeException("jedisCluster is null");
        }
        // 写入redis
        Long result = jedisCluster.setnx(redisKey, alertHash);
        if (result.equals(SET_NX_SUCCESS)) {
            jedisCluster.pexpire(redisKey, expireDuration);
        }
        return result;
    }

    private String getLogHash(String log) throws NoSuchAlgorithmException {
        // 创建一个MessageDigest实例，使用MD5算法
        MessageDigest digest = MessageDigest.getInstance("MD5");
        // 计算输入字符串的哈希值
        byte[] hashBytes = digest.digest(log.getBytes(StandardCharsets.UTF_8));
        // 将哈希值转换为十六进制字符串
        StringBuilder hexString = new StringBuilder();
        for (byte b : hashBytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

    public Map<String, Object> analysisLog(AlertInfo alertInfo, MilogLogTailDo milogLogtailDo, MilogLogStoreDO logStoreDO) throws ExecutionException {
        if (null != milogLogtailDo) {
            String keyList = logStoreDO.getKeyList();
            String valueList = milogLogtailDo.getValueList();
//            //如果是正则，先去掉，大量数据堆积有点多
//            if (!Objects.equals(milogLogtailDo.getParseType(), LogParserFactory.LogParserEnum.REGEX_PARSE.getCode())) {
//                return Maps.newHashMap();
//            }
            LogParser logParser = TAIL_PARSER_CACHE.get(milogLogtailDo.getId(), () -> LogParserFactory.getLogParser(milogLogtailDo.getParseType(), Utils.parse2KeyAndTypeList(keyList, logStoreDO.getColumnTypeList()), valueList, milogLogtailDo.getParseScript(), "", milogLogtailDo.getTail(), "", logStoreDO.getLogstoreName(), keyList));
            String logInfo = alertInfo.getInfo(LOG_KEY);
            String collectStamp = alertInfo.getInfoOrDefault(esKeyMap_timestamp, String.valueOf(Instant.now().toEpochMilli()));
            return logParser.parseSimple(logInfo, Long.valueOf(collectStamp));
        }
        return Maps.newHashMap();
    }

    public String generateLogParam(Long spaceId, MilogLogStoreDO logStoreDO, String tailName, Map<String, Object> analysisLogMap, AlertInfo alertInfo)
            throws UnsupportedEncodingException {
        String commonParam = String.format("spaceId=%s&storeId=%s&storeName=%s&", spaceId, logStoreDO.getId(), encodeParam(logStoreDO.getLogstoreName()));

        long curTimestamp = Clock.systemUTC().instant().toEpochMilli();
        long timeStamp = getMinTimestamp(analysisLogMap, alertInfo, curTimestamp);
//        long timeStamp = Math.min((Long) analysisLogMap.getOrDefault(esKeyMap_timestamp, curTimestamp), Long.valueOf(alertInfo.getInfoOrDefault("timeStamp", String.valueOf(curTimestamp))));

        String lineNumber = alertInfo.getInfoOrDefault("lineNumber", "0");
        String logIp = alertInfo.getInfo("ip");
        String fileName = alertInfo.getInfo("fileName");
        String middleParam = String.format("inputV=linenumber:%s%sand%slogip:%s%sand%stail:%s%sand%sfilename:%s", lineNumber, encodeParam(" "), encodeParam(" "), logIp, encodeParam(" "), encodeParam(" "), encodeParam(StringUtils.wrap(tailName, '"')), encodeParam(" "), encodeParam(" "), encodeParam(StringUtils.wrap(fileName, '"')));
        String timeParam = String.format("&startTime=%s&endTime=%s", timeStamp - TimeUnit.MINUTES.toMillis(5), timeStamp + TimeUnit.MINUTES.toMillis(5));
        return commonParam + middleParam + timeParam;
    }

    public long getMinTimestamp(Map<String, Object> analysisLogMap, AlertInfo alertInfo, long curTimestamp) {
        try {
            Object timestampObj = analysisLogMap.getOrDefault(esKeyMap_timestamp, curTimestamp);
            if (timestampObj instanceof Long) {
                long logTimestamp = Long.parseLong(String.valueOf(timestampObj));
                String alertTimestampStr = alertInfo.getInfoOrDefault("timeStamp", String.valueOf(curTimestamp));
                long alertTimestamp = Long.parseLong(alertTimestampStr);

                return Math.min(logTimestamp, alertTimestamp);
            }

        } catch (NumberFormatException e) {
            // 处理时间戳格式错误的情况
            log.error("Invalid timestamp format: ", e);
        }
        return curTimestamp;
    }

    private String encodeParam(String param) throws UnsupportedEncodingException {
        return URLEncoder.encode(param, "UTF-8");
    }

    private AlertCondition checkCondition(List<AlertCondition> alertConditions, int count) {
        if (alertConditions == null) {
            return null;
        }
        for (AlertCondition current : alertConditions) {
            if (evaluate(current.getOperation(), current.getValue(), count)) {
                return current;
            }
        }
        return null;
    }

    private boolean evaluate(String operation, int limit, int value) {
        if (StringUtils.isEmpty(operation)) {
            return false;
        }
        boolean success;
        switch (operation) {
            case ">":
                success = value > limit;
                break;
            case ">=":
                success = value >= limit;
                break;
            case "<":
                success = value < limit;
                break;
            case "<=":
                success = value <= limit;
                break;
            case "=":
                success = (value == limit);
                break;
            case "!=":
                success = value != limit;
                break;
            default:
                success = false;
                break;
        }
        return success;
    }

    private boolean canSendAlert(AlertInfo alertInfo, long lastAlertTime, long period, MilogLogStoreDO store, MilogLogTailDo tail) throws NoSuchAlgorithmException {
        // 判断该段日志是否发送过告警
        if (alertAlreadySent(alertInfo)) {
            log.debug("alert already sent, skip send, alert hash:{}, alertId:{}, alertInfo:{}", getLogHash(alertInfo.getInfo("log")), alertInfo.getAlertId(), alertInfo);
            return false;
        }
        // 判断该告警是否过期
        if (alertExpire(alertInfo, store, tail)) {
            log.debug("alert expire, skip send, alertId:{}, alertInfo:{}", alertInfo.getAlertId(), alertInfo);
            return false;
        }
        return (System.currentTimeMillis() - lastAlertTime) >= period;
    }

    private Boolean alertAlreadySent(AlertInfo alertInfo) throws NoSuchAlgorithmException {
        // 获取redis锁
        Long value = setAlertAlreadySent(alertInfo);
        return !SET_NX_SUCCESS.equals(value);
    }

    private Boolean alertExpire(AlertInfo alertInfo, MilogLogStoreDO store, MilogLogTailDo tail) {
        Long expireDuration = Long.valueOf(Config.ins().get("alert_expire_duration", "86400000"));
        Long collectStamp;
        Long logTime;
        String collectStampStr = alertInfo.getInfo("timeStamp");
        String originLog = alertInfo.getInfo("log");

        if (isValidTimeStamp(collectStampStr)) {
            collectStamp = Long.valueOf(collectStampStr);
        } else {
            log.error("collect timestamp is not valid,alertId:{},timestamp:{},skip send alert", alertInfo.getAlertId(), collectStampStr);
            return true;
        }

        // 解析脚本解析日志获取日志时间戳
        logTime = scriptMatchTimestamp(alertInfo, originLog, collectStamp, store, tail);
        if (ObjectUtils.isNotEmpty(logTime)) {
            return System.currentTimeMillis() - logTime >= expireDuration;
        }
        // 行首正则接卸日志匹配时间戳
        logTime = regexMatchTimestamp(originLog);
        if (ObjectUtils.isNotEmpty(logTime)) {
            return System.currentTimeMillis() - logTime >= expireDuration;
        }
        // 解析和行首匹配都为空对比采集时间
        return System.currentTimeMillis() - collectStamp >= expireDuration;
    }

    private Long scriptMatchTimestamp(AlertInfo alertInfo, String originLog, Long collectStamp, MilogLogStoreDO store, MilogLogTailDo tail) {
        String ip = alertInfo.getInfo("ip");
        String lineNumStr = alertInfo.getInfo("lineNum");
        String fileName = alertInfo.getInfo("fileName");
        Long lineNum = null;
        // 获取采集时间戳
        // 获取日志的logStore、logTail获取解析方式
        if (null == alertInfo.getInfo("tag")) {
            log.error("alertId:{},tag is null,skip send alert,alertInfo:{},originLog:{}", alertInfo.getAlertId(), gson.toJson(alertInfo), originLog);
            return null;
        }
        String[] idList = alertInfo.getInfo("tag").split("_");
        String storeId = idList[2];
        String tailId = idList[3];
        if (!StringUtils.isNumeric(storeId) || !StringUtils.isNumeric(tailId)) {
            return null;
        }
        if (!StringUtils.isNumeric(lineNumStr)) {
            lineNum = 0L;
        }
        // 解析日志获取日志时间戳
//        AbstractLogParser logParser = (AbstractLogParser) LogParserFactory.getLogParser(tail.getParseType(), Utils.parse2KeyAndTypeList(store.getKeyList(), store.getColumnTypeList()), tail.getValueList(), tail.getParseScript(), store.getKeyList());
        try {
            AbstractLogParser logParser = (AbstractLogParser) TAIL_PARSER_CACHE.get(tail.getId(), () -> LogParserFactory.getLogParser(tail.getParseType(), Utils.parse2KeyAndTypeList(store.getKeyList(), store.getColumnTypeList()), tail.getValueList(), tail.getParseScript(), "", tail.getTail(), "", store.getLogstoreName(), store.getKeyList()));

            Map<String, Object> parseMsg = logParser.doParse(originLog, ip, lineNum, collectStamp, fileName);
            Object logTimestampObj = parseMsg.get(esKeyMap_timestamp);
            return parseTimestamp(logTimestampObj, collectStamp);
        } catch (Exception e) {
            log.error("alertId:{},parse log error,originLog:{},tailId:{},storeId:{},tail:{},store:{},parseScript:{}", alertInfo.getAlertId(), originLog, tailId, storeId, tail.getTail(), store.getLogstoreName(), tail.getParseScript(), e);
        }
        return System.currentTimeMillis();
    }

    private Long regexMatchTimestamp(String originLog) {
        String tsRegexListJSON = Config.ins().get("log_timestamp_regex", "");
        String tsFormatListJSON = Config.ins().get("log_timestamp_format", "");
        String[] tsRegexList = gson.fromJson(tsRegexListJSON, String[].class);
        String[] tsFormatList = gson.fromJson(tsFormatListJSON, String[].class);
        // 截取行首
        String logHead = originLog.split("\n")[0];
        if (StringUtils.isEmpty(logHead)) {
            return null;
        }

        for (int i = 0; i < tsRegexList.length; i++) {
            // 时间戳正则匹配
            String timestampRegex = tsRegexList[i];
//            Pattern pattern = Pattern.compile(timestampRegex);
            try {
                Pattern pattern = SCRIPT_COMPILE_CACHE.get(timestampRegex, () -> Pattern.compile(timestampRegex));
                Matcher matcher = pattern.matcher(logHead);
                if (matcher.find()) {
                    String timestamp = matcher.group(1);
                    return DateUtil.parse(timestamp, tsFormatList[i]).getTime();
                }
            } catch (ExecutionException e) {
            }
        }
        return null;
    }

    private Long parseTimestamp(Object logTimestampObj, Long collectStamp) {
        if (ObjectUtils.isEmpty(logTimestampObj)) {
            return null;
        }
        if (logTimestampObj instanceof String logTimeStr) {
            if (!isValidTimeStamp(logTimeStr)) {
                return null;
            }
            return Long.valueOf(logTimeStr);
        }
        if (logTimestampObj instanceof Long logTime) {
            return logTimestampObj.equals(collectStamp) ? null : logTime;
        }
        return null;
    }

    private Boolean isValidTimeStamp(String timestamp) {
        String regex = "\\d{13}";
        return timestamp.matches(regex);
    }

    @PreDestroy
    private void shutdown() {
        talosConsumer.shutDown();
    }

    private String parseTraceId(String logMessage, String[] logFormat) {
        if (StringUtils.isEmpty(logMessage) || logFormat == null || logFormat.length != 2 || StringUtils.isEmpty(logFormat[0]) || StringUtils.isEmpty(logFormat[1])) {
            return null;
        }
        String parseScript = logFormat[0];
        String valueList = logFormat[1];
        String[] logParts = logMessage.split("[" + parseScript + "]");
        String[] valueParts = valueList.split(",");

        int traceIdIndex = -1;
        for (int i = 0; i < valueParts.length; i++) {
            if (valueParts[i].equals("traceId")) {
                traceIdIndex = i;
                break;
            }
        }
        return traceIdIndex >= 0 && traceIdIndex < logParts.length ? logParts[traceIdIndex].trim() : null;
    }

    // callback for consumer to process messages, that is, consuming logic
    private class MyMessageProcessor implements MessageProcessor {
        @Override
        public void init(TopicAndPartition topicAndPartition, long messageOffset) {

        }

        @Override
        public void process(List<MessageAndOffset> messages, MessageCheckpointer messageCheckpointer) {
            try {
                for (MessageAndOffset messageAndOffset : messages) {
                    handleAlertMsg(new String(messageAndOffset.getMessage().getMessage(), "UTF-8"));
                }
            } catch (Throwable throwable) {
                log.error(throwable.toString());
            }
        }

        @Override
        public void shutdown(MessageCheckpointer messageCheckpointer) {

        }
    }

    // using for thread-safe when processing different partition data
    private class MyMessageProcessorFactory implements MessageProcessorFactory {
        @Override
        public MessageProcessor createProcessor() {
            return new MyMessageProcessor();
        }
    }

    private String[] getLogFormat(Alert alert) {
        String appId = alert.getApp();
        String department = alert.getArgument("department");
        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(department)) {
            return null;
        }
        try {

            List<LogPathTopicVo> logPathTopicVoList = milogAppTopicService.queryTopicConfigByAppId(alert.getMilogAppId());
            for (LogPathTopicVo current : logPathTopicVoList) {
                if (current.getLogPath() != null && alert.getLogPath().equals(current.getLogPath())) {
                    return new String[]{current.getParseScript().trim(), current.getValueList().trim()};
                }
            }

        } catch (Exception e) {
            log.info("traceId missing for appId: " + appId);
        }
        return null;
    }
}
