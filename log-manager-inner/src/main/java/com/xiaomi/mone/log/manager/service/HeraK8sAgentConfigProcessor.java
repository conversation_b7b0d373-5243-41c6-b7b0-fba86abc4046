package com.xiaomi.mone.log.manager.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.mione.tesla.k8s.bo.LogAgentListBo;
import com.xiaomi.youpin.docean.anno.Service;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ozhera.app.api.response.AppBaseInfo;
import org.apache.ozhera.log.api.model.meta.LogCollectMeta;
import org.apache.ozhera.log.api.model.meta.LogPattern;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;
import org.apache.ozhera.log.manager.service.impl.HeraAppServiceImpl;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.INNER_LOG_AGENT_SERVICE;
import static org.apache.ozhera.log.common.Constant.SYMBOL_COLON;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/6/7 17:32
 */
@Service
public class HeraK8sAgentConfigProcessor implements AgentConfigProcessor {

    @Resource(name = INNER_LOG_AGENT_SERVICE)
    private InnerLogAgentService logAgentService;

    @Resource
    private HeraAppServiceImpl heraAppService;

    @Resource
    private MilogLogTailDao milogLogtailDao;

    @Override
    public LogCollectMeta queryLogCollectMeta(String ip) {
        List<LogAgentListBo> k8sPodIps = logAgentService.queryK8sPodIps(ip.split(SYMBOL_COLON)[0]);
        LogCollectMeta logCollectMeta = queryLogAgentConfigK8s(ip, k8sPodIps);
        return logCollectMeta;
    }

    public LogCollectMeta queryLogAgentConfigK8s(String agentIp, List<LogAgentListBo> podIps) {
        LogCollectMeta logCollectMeta = logAgentService.initializeLogCollectMeta(agentIp);
        Map<MilogLogTailDo, List<LogAgentListBo>> tailDoListMap = mapLogTailsToAgents(podIps);
        List<MilogLogTailDo> logTailDos = tailDoListMap.keySet().stream().distinct().collect(Collectors.toList());
        Map<Long, List<MilogLogTailDo>> idTailListMap = warpAppAndTailRel(logTailDos);

        List<Long> appBaseInfoIds = Lists.newArrayList(idTailListMap.keySet());

        logCollectMeta.setAppLogMetaList(appBaseInfoIds.stream()
                .map(appBaseInfoId -> logAgentService.assembleSingleConfig(appBaseInfoId, queryLogPattern(idTailListMap.get(appBaseInfoId), tailDoListMap)))
                .filter(appLogMeta -> CollectionUtils.isNotEmpty(appLogMeta.getLogPatternList()))
                .collect(Collectors.toList()));
        return logCollectMeta;
    }

    public Map<MilogLogTailDo, List<LogAgentListBo>> mapLogTailsToAgents(List<LogAgentListBo> podIps) {
        Map<LogAgentListBo, List<MilogLogTailDo>> listBoListMap = Maps.newConcurrentMap();
        Map<MilogLogTailDo, List<LogAgentListBo>> logTailDoListMap = Maps.newConcurrentMap();
        podIps.parallelStream().forEach(logAgentBo -> listBoListMap.put(logAgentBo, milogLogtailDao.queryByIp(logAgentBo.getPodIP())));
        for (Map.Entry<LogAgentListBo, List<MilogLogTailDo>> entry : listBoListMap.entrySet()) {
            entry.getValue().forEach(miLogLogTailDo -> {
                //二次过滤，避免外网包被覆盖时偶现的错误下发
                if (!miLogLogTailDo.getCollectionReady()) {
                    return;
                }
                logTailDoListMap.putIfAbsent(miLogLogTailDo, Lists.newArrayList());
                logTailDoListMap.get(miLogLogTailDo).add(entry.getKey());
            });
        }
        return logTailDoListMap;
    }

    public Map<Long, List<MilogLogTailDo>> warpAppAndTailRel(List<MilogLogTailDo> logTailDos) {
        Map<Long, List<MilogLogTailDo>> appTopicRelListMap = Maps.newHashMap();
        logTailDos.forEach(logTailDo -> {
            AppBaseInfo appBaseInfo = heraAppService.queryById(logTailDo.getMilogAppId());
            if (null != appBaseInfo) {
                appTopicRelListMap.putIfAbsent(appBaseInfo.getId().longValue(), Lists.newArrayList());
                appTopicRelListMap.get(appBaseInfo.getId().longValue()).add(logTailDo);
            }
        });
        return appTopicRelListMap;
    }

    private List<LogPattern> queryLogPattern(List<MilogLogTailDo> logTailDos, Map<MilogLogTailDo, List<LogAgentListBo>> tailDoListMap) {
        List<LogPattern> logPatternList = Lists.newArrayList();
        for (MilogLogTailDo milogLogtailDo : logTailDos) {
            logPatternList.addAll(logAgentService.queryLogPattern(milogLogtailDo.getId(), tailDoListMap.get(milogLogtailDo)));
        }
        return logPatternList;
    }
}
