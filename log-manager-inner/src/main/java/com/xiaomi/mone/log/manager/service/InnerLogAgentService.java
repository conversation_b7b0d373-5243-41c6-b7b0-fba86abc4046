package com.xiaomi.mone.log.manager.service;

import cn.hutool.core.thread.ThreadUtil;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.xiaomi.mione.tesla.k8s.bo.LogAgentListBo;
import com.xiaomi.mone.app.enums.PlatFormTypeInnerEnum;
import com.xiaomi.mone.enums.*;
import com.xiaomi.mone.log.manager.common.context.MilogUserUtil;
import com.xiaomi.mone.log.manager.dao.InnerLogAppMiddlewareRelDao;
import com.xiaomi.mone.log.manager.dao.InnerLogTailDao;
import com.xiaomi.mone.log.manager.model.convert.MilogAgentConvert;
import com.xiaomi.mone.log.manager.model.dto.MatrixAppsDTO;
import com.xiaomi.mone.log.manager.model.dto.MatrixDataDTO;
import com.xiaomi.mone.log.manager.model.dto.MetaAppInfoDTO;
import com.xiaomi.mone.log.manager.model.dto.TraceAppInfoDTO;
import com.xiaomi.mone.log.manager.model.vo.K8sToAgentVo;
import com.xiaomi.mone.log.manager.service.impl.*;
import com.xiaomi.mone.miline.api.dto.PipelineDeployDto;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.common.NamedThreadFactory;
import com.xiaomi.youpin.docean.plugin.dubbo.anno.Reference;
import com.xiaomi.youpin.gwdash.bo.MachineBo;
import com.xiaomi.youpin.gwdash.bo.Page;
import com.xiaomi.youpin.gwdash.bo.ProjectDeployInfoDTO;
import com.xiaomi.youpin.gwdash.bo.openApi.ProjectDeployInfoQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.app.api.response.AppBaseInfo;
import org.apache.ozhera.log.api.enums.AppTypeEnum;
import org.apache.ozhera.log.api.enums.MachineTypeEnum;
import org.apache.ozhera.log.api.enums.OperateEnum;
import org.apache.ozhera.log.api.model.meta.*;
import org.apache.ozhera.log.api.model.vo.AgentLogProcessDTO;
import org.apache.ozhera.log.api.service.PublishConfigService;
import org.apache.ozhera.log.common.Constant;
import org.apache.ozhera.log.common.Result;
import org.apache.ozhera.log.manager.common.Utils;
import org.apache.ozhera.log.manager.dao.MilogAppMiddlewareRelDao;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.dao.MilogLogstoreDao;
import org.apache.ozhera.log.manager.dao.MilogMiddlewareConfigDao;
import org.apache.ozhera.log.manager.domain.LogProcess;
import org.apache.ozhera.log.manager.model.bo.MilogAgentIpParam;
import org.apache.ozhera.log.manager.model.dto.MotorRoomDTO;
import org.apache.ozhera.log.manager.model.dto.PodDTO;
import org.apache.ozhera.log.manager.model.pojo.*;
import org.apache.ozhera.log.manager.model.vo.AgentListQuery;
import org.apache.ozhera.log.manager.service.extension.agent.MilogAgentService;
import org.apache.ozhera.log.manager.service.impl.HeraAppServiceImpl;
import org.jetbrains.annotations.NotNull;
import org.nutz.lang.Strings;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.xiaomi.mone.enums.InnerProjectTypeEnum.*;
import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.INNER_LOG_AGENT_SERVICE;
import static org.apache.ozhera.log.common.Constant.SYMBOL_COLON;
import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getPhysicsDirectory;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/4/10 21:02
 */
@Service(name = INNER_LOG_AGENT_SERVICE)
@Slf4j
public class InnerLogAgentService implements MilogAgentService {

    @Resource
    private ChinaMilogRpcConsumerServiceImpl chinaMilogRpcConsumerService;

    @Resource
    private YouPinMilogRpcConsumerServiceImpl youPinMilogRpcConsumerService;

    @Resource
    private HeraAppServiceImpl heraAppService;

    @Resource
    private MatrixLogServiceImpl matrixLogServiceImpl;

    @Resource
    private MilogLogTailDao milogLogtailDao;

    @Resource
    private InnerLogTailDao innerLogTailDao;

    @Resource
    private MilogLogstoreDao milogLogstoreDao;

    private Gson gson = Constant.GSON;

    @Resource
    private LogProcess logProcess;

    @Resource
    private MilogAppMiddlewareRelDao milogAppMiddlewareRelDao;
    @Resource
    private MilogMiddlewareConfigDao milogMiddlewareConfigDao;

    @Resource
    private K8sRpcComsumerServiceImpl k8sRpcComsumerService;

    @Resource
    private MilineRpcConsumerServiceImpl milineRpcConsumerService;
    @Resource
    private InnerLogTailService milogLogtailService;
    @Resource
    private DockerAgentConfigProcessor dockerAgentConfigProcessor;
    @Resource
    private HeraK8sAgentConfigProcessor heraK8sAgentConfigProcessor;
    @Resource
    private MatrixAgentConfigProcessor matrixAgentConfigProcessor;
    @Resource
    private CloudMLAgentConfigProcessor cloudMLAgentConfigProcessor;

    @Resource
    private InnerLogAppMiddlewareRelDao innerLogAppMiddlewareRelDao;
    @Resource
    private CloudPlatformK8sAppServiceImpl cloudPlatformK8sAppService;

    @Reference(interfaceClass = PublishConfigService.class, group = "$dubbo.env.group", check = false, timeout = 14000)
    private PublishConfigService publishConfigService;

    private static final ThreadPoolExecutor THREAD_POOL_EXECUTOR;

    private static final Integer K8S_DEPLOYED = 5;

    private static final String IGNORE_APP_NAME = "milog-agent";

    private static final AtomicInteger COUNT_INCR = new AtomicInteger(0);

    private Map<Integer, AgentConfigProcessor> agentConfigProcessorMap = new HashMap<>();

    private static final Cache<Long, MilogLogStoreDO> LOG_STORE_CACHE = CacheBuilder.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(30, TimeUnit.SECONDS)
            .build();

    private static final Cache<Integer, MilogAppMiddlewareRel> APP_TYPE_CACHE = CacheBuilder.newBuilder()
            .maximumSize(10)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build();

    private static final Cache<Long, MilogMiddlewareConfig> WARE_CONFIG_CACHE = CacheBuilder.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(60, TimeUnit.SECONDS)
            .build();

    static {
        THREAD_POOL_EXECUTOR = new ThreadPoolExecutor(6, 20,
                1, TimeUnit.MINUTES, new ArrayBlockingQueue<>(800),
                new NamedThreadFactory("coll-base-data-start"),
                new ThreadPoolExecutor.DiscardOldestPolicy());
        //定时打印 线程池信息
        Executors.newSingleThreadScheduledExecutor(
                ThreadUtil.newNamedThreadFactory("log-printf send config executor info", false)
        ).scheduleAtFixedRate(() -> log.warn("coll-base-data-start statistic TP_EXECUTOR:{}", THREAD_POOL_EXECUTOR),
                2, 2, TimeUnit.MINUTES);
    }

    public void init() {
        agentConfigProcessorMap.put(0, dockerAgentConfigProcessor);
        agentConfigProcessorMap.put(1, heraK8sAgentConfigProcessor);
        agentConfigProcessorMap.put(2, matrixAgentConfigProcessor);
        agentConfigProcessorMap.put(3, cloudMLAgentConfigProcessor);
    }

    /**
     * 获取agent列表
     *
     * @param agentListQuery
     * @return
     */
    public Result<Page<ProjectDeployInfoDTO>> getList(AgentListQuery agentListQuery) {
        ProjectDeployInfoQuery query = MilogAgentConvert.INSTANCE.toQueryDeploy(agentListQuery);
        query.setProjectName(AppTypeEnum.LOG_AGENT.getName());
        Page<ProjectDeployInfoDTO> agentInfoDTOList;
        if (com.xiaomi.mone.log.manager.common.context.MilogUserUtil.isYoupin()) {
            agentInfoDTOList = youPinMilogRpcConsumerService.queryProjectDeployInfoList(query);
        } else {
            agentInfoDTOList = chinaMilogRpcConsumerService.queryProjectDeployInfoList(query);
        }
        return Result.success(agentInfoDTOList);
    }

    @Override
    public Result<List<AgentLogProcessDTO>> process(String ip) {
        List<AgentLogProcessDTO> dtoList = logProcess.getAgentLogProcess(ip);
        return Result.success(dtoList);
    }

    @Override
    public Result<String> configIssueAgent(String agentId, String agentIp, String agentMachine) {
        if (StringUtils.isEmpty(agentIp)) {
            return Result.failParam("agentIp不能为空");
        }
        // 1.查询该物理机下全量的配置
        LogCollectMeta logCollectMeta = queryMilogAgentConfig(agentId, agentIp, agentMachine);
        log.info("{},this ip config data:{}", agentIp, gson.toJson(logCollectMeta));
        String k8sNodeIP = queryNodeIpByPodIp(agentIp);
        if (StringUtils.isNotEmpty(k8sNodeIP)) {
            log.info("query k8s ip succeed,ip:{},k8sNodeIP:{}", agentIp, k8sNodeIP);
            agentIp = k8sNodeIP;
        }
        List<String> ipAddress = Lists.newArrayList();
        //2.下发配置
        sendConfigToAgent(agentIp, logCollectMeta);
        return Result.success("success");
    }

    public Result<String> configIssueAgentK8s(Long tailId, String agentIp,
                                              List<LogAgentListBo> logAgentListBos, K8sToAgentVo k8sToAgentVo) {
        if (StringUtils.isEmpty(agentIp)) {
            return Result.failParam("agentIp不能为空");
        }
        // 1.查询该agent机器下下全量的配置
        LogCollectMeta logCollectMeta = queryMilogAgentConfigK8s(tailId, agentIp, logAgentListBos);
        logCollectMeta.setPodType(k8sToAgentVo.getPodType());
        logCollectMeta.setSingleMetaData(true);
        log.info("{},this k8sip config data:{}", agentIp, gson.toJson(logCollectMeta));
        List<String> ipAddress = publishConfigService.getAllAgentList();
        log.debug("agent ip list:{}", gson.toJson(ipAddress));
        //2.下发配置
        sendConfigToAgent(agentIp, logCollectMeta);
        return Result.success("success");
    }

    /**
     * 下发配置到指定的ip
     *
     * @param logCollectMeta
     * @param agentIp
     */
    public void sendConfigToAgent(final String agentIp, LogCollectMeta logCollectMeta) {
        if (CollectionUtils.isEmpty(logCollectMeta.getAppLogMetaList()) || logCollectMeta.getAppLogMetaList()
                .stream().allMatch(appLogMeta -> CollectionUtils.isEmpty(appLogMeta.getLogPatternList()))) {
            return;
        }
        // 放在线程池中 执行
        THREAD_POOL_EXECUTOR.execute(() -> {
            log.info("send to agent by publishConfigService, agentIp is {}, and logCollectMeta is {}", agentIp, gson.toJson(logCollectMeta));
            publishConfigService.sengConfigToAgent(agentIp, logCollectMeta);
        });
    }

    /**
     * 发送增量的配置
     * 1.找到部署这台app的所有物理机ip
     * 2.这条新增的配置推送给这些物理机或者容器
     *
     * @param milogAppId
     * @param ips
     */
    @Override
    public void publishIncrementConfig(Long tailId, Long milogAppId, List<String> ips) {
        try {
            log.info("processLogTail, push agent params,milogAppId:{},ips:{}", milogAppId, ips);
            if (CollectionUtils.isEmpty(ips)) {
                return;
            }
            printMangerInfo();
            MilogLogTailDo milogLogtailDo = milogLogtailDao.queryById(tailId);
            if (MATRIX_TYPE.getCode().equals(milogLogtailDo.getAppType())) {
                // handle pod ip 2 host ip
                try {
                    MatrixAppsDTO.MatrixDeploySpace space = matrixLogServiceImpl.getMatrixPodInfoByAppInfo(milogLogtailDo.getAppId(), milogLogtailDo.getDeploySpace());
                    if (space == null) {
                        log.error("processLogTail, getMatrixPodInfoByAppInfo got null space, skip this round, tailId:{}, milogAppId:{}", milogLogtailDo.getId(), milogLogtailDo.getMilogAppId());
                        return;
                    }
                    List<LogAgentListBo> pods = space.getPodIp2PodMap().values().stream().map(MatrixDataDTO.Pod::toAgentListBo).collect(Collectors.toList());
                    String appNamespace = matrixLogServiceImpl.getTraceBaseInfoByAppId(milogLogtailDo.getAppId()).getAppNamespace();
                    publishIncrementConfigForCloudPlatformK8s(appNamespace, milogLogtailDo, pods);
                } catch (Exception e) {
                    log.error("processLogTail, getMatrixPodInfoByAppInfo got exception, skip this round, tailId:{}, milogAppId:{}", milogLogtailDo.getId(), milogLogtailDo.getMilogAppId(), e);
                }
                return;
            }
            if (CLOUDML_TYPE.getCode().equals(milogLogtailDo.getAppType())) {
                TraceAppInfoDTO traceAppInfoDTO = matrixLogServiceImpl.queryTraceAppInfos(milogLogtailDo.getAppId());
                if (traceAppInfoDTO == null || CollectionUtils.isEmpty(traceAppInfoDTO.getCloudmlPodList())) {
                    log.info("processLogTail, cloudml app has no instances: appId{}, ips:{}", milogAppId, ips);
                    return;
                }
                String appNamespace = matrixLogServiceImpl.getTraceBaseInfoByAppId(milogLogtailDo.getAppId()).getAppNamespace();
                publishIncrementConfigForCloudPlatformK8s(appNamespace, milogLogtailDo, traceAppInfoDTO.getCloudmlPodList());
                return;
            }
            if (MIKS_TYPE.getCode().equals(milogLogtailDo.getAppType())) {
                MetaAppInfoDTO appInfoDTO = cloudPlatformK8sAppService.getMetaAppByAppId(milogLogtailDo.getAppId(), true);
                List<LogAgentListBo> pods = appInfoDTO.getPodsForCluster(milogLogtailDo.getEnvName(), true);
                if (pods == null) {
                    log.info("processLogTail, miks app has no instances: appId{}, ips:{}", milogAppId, ips);
                    return;
                }
                publishIncrementConfigForCloudPlatformK8s(appInfoDTO.getExtra().getLogNamespace(), milogLogtailDo, pods);
                return;
            }

            Map<String, List<LogAgentListBo>> agentK8sMap = milogLogtailService.queryAgentIpByPodIps(ips);
            if (null == tailId || InnerDeployWayEnum.MIONE.getCode().equals(milogLogtailDo.getDeployWay()) || agentK8sMap.isEmpty()) {
                log.info("processLogTail, mione or miline machine config,milogAppId:{}", milogAppId);
                ips.forEach(ip -> {
                    AppLogMeta appLogMeta = assembleSingleConfig(milogAppId, queryLogPattern(milogAppId, ip));
                    LogCollectMeta logCollectMeta = new LogCollectMeta();
                    logCollectMeta.setAgentIp(ip);
                    logCollectMeta.setAppLogMetaList(Arrays.asList(appLogMeta));
                    AgentDefine agentDefine = new AgentDefine();
                    agentDefine.setFilters(new ArrayList<>());
                    logCollectMeta.setAgentDefine(agentDefine);
                    // 查看是否是miline中的k8s，如果是则要转换ip
                    if (InnerDeployWayEnum.MILINE.getCode().equals(milogLogtailDo.getDeployWay())) {
                        String k8sNodeIP = queryK8sNodeIP(milogAppId, ip);
                        if (StringUtils.isNotEmpty(k8sNodeIP)) {
                            log.info("processLogTail, query k8s ip succeed,milogAppId:{},ip:{},k8sNodeIP:{}", milogAppId, ip, k8sNodeIP);
                            ip = k8sNodeIP;
                        }
                    }
                    log.info("processLogTail ip:{}, push agent config data:{}", ip, gson.toJson(logCollectMeta));
                    sendConfigToAgent(ip, logCollectMeta);
                });
            } else {
                log.info("processLogTail, k8s machine config:{}", milogAppId);
                milogLogtailService.k8sPodIpsSend(milogLogtailDo.getId(), ips, K8sToAgentVo.init());
            }
        } catch (Exception e) {
            log.error("processLogTail, push agent config failed, milogAppId:{}, error:", milogAppId, e);
        }
    }

    /**
     * 发送增量的配置
     * 1.拼接采集路径
     * 2.这条新增的配置推送给这些容器所在的物理机
     *
     * @param appNamespace 日志路径中的特殊段
     * @param tail         tail
     * @param podList      key=pod-ip,value=pod，都是这个app-deploy_space 下的,但是可能在不同的宿主机上
     */
    public void publishIncrementConfigForCloudPlatformK8s(String appNamespace, MilogLogTailDo tail, List<LogAgentListBo> podList) {
        log.info("push increment agent params for cloud platform k8s,appNamespace:{}, tailId:{}, milogAppId:{}, pods:{}", appNamespace, tail.getId(), tail.getMilogAppId(), podList);
        if (CollectionUtils.isEmpty(podList)) {
            return;
        }
        printMangerInfo();
        // 按 agent ip 分个组
        HashMap<String, List<LogAgentListBo>> agentIp2PodsMap = new HashMap<>();
        podList.stream().forEach(pod -> {
            agentIp2PodsMap.putIfAbsent(pod.getAgentIP(), new ArrayList<>());
            agentIp2PodsMap.get(pod.getAgentIP()).add(pod);
        });

        agentIp2PodsMap.forEach((key, value) -> {
            //将tail传进去，不查数据库。因为此时数据库的 ips 可能还没更新
            List<LogPattern> logPatterns = assembleLogPatternForCloudPlatformK8s(appNamespace, tail, value);
            AppLogMeta appLogMeta = assembleSingleConfig(tail.getMilogAppId(), logPatterns);
            LogCollectMeta logCollectMeta = new LogCollectMeta();
            // matrix 接口获取的 nodeIP 是机器名，做一次到 ip 的转换
            String agentRealIP = matrixLogServiceImpl.hostNameToIP(key);
            logCollectMeta.setAgentIp(agentRealIP);
            logCollectMeta.setAppLogMetaList(Arrays.asList(appLogMeta));
            AgentDefine agentDefine = new AgentDefine();
            agentDefine.setFilters(new ArrayList<>());
            logCollectMeta.setAgentDefine(agentDefine);
            log.info("push increment agent config for matrix, data:{}", gson.toJson(logCollectMeta));

            sendConfigToAgent(agentRealIP, logCollectMeta);
        });
    }

    /**
     * 创建类 k8s 的 log path， 组成为 /home/<USER>/log/{namespace}/{podName1|podName2|...}/log/
     *
     * @param namespace matrix 场景下由 appNamespace-deploySpace 组成;cloudml 为 appNamespace;miks 可能为空
     * @param podList
     * @param logDir
     * @return
     */
    String generateK8sLikeLogPath(String namespace, List<LogAgentListBo> podList, String logDir) {
        if (StringUtils.isEmpty(logDir) ||
                null == podList ||
                podList.isEmpty()) {
            return logDir;
        }
        if (!StringUtils.isEmpty(namespace)) {
            namespace = STR."\{namespace}/";
        } else {
            namespace = "";
        }
        String podNames = podList.stream()
                .sorted(Comparator.comparing(LogAgentListBo::getPodIP))
                .map(LogAgentListBo::getPodName)
                .collect(Collectors.joining("|"));
        String logPathSuffix = StringUtils.substringAfter(logDir, "/home/<USER>");
        return new StringBuilder().append(LOG_PATH_PREFIX)
                .append("/")
                .append(namespace)
                .append(String.format(podNames.split("\\|").length > 1 ? "(%s)" : "%s", podNames))
                .append(logPathSuffix).toString();
    }

    /*
        组成为 /home/<USER>/log/appNamespace-deploySpaceName/{podName1|podName2|...}/log/
     */
    private String generateMatrixLogPath(String appNamespace, String deploySpace,
                                         List<LogAgentListBo> podList, String logDir) {
        if (StringUtils.isEmpty(logDir) ||
                StringUtils.isEmpty(appNamespace) ||
                StringUtils.isEmpty(deploySpace) ||
                podList.isEmpty()) {
            return logDir;
        }
        return generateK8sLikeLogPath(STR."\{appNamespace}-\{deploySpace}", podList, logDir);
    }

    private void printMangerInfo() {
        List<String> remoteAddress = publishConfigService.getAllAgentList();
        if (COUNT_INCR.getAndIncrement() % 200 == 0) {
            log.info("连接的agent机器远程地址集合为:{}", gson.toJson(remoteAddress));
        }
    }

    private String queryK8sNodeIP(Long milogAppId, String ip) {
        try {
            List<MilogLogTailDo> milogLogtailDos = milogLogtailDao.queryByAppIdAgentIp(milogAppId, ip);
            if (CollectionUtils.isNotEmpty(milogLogtailDos)) {
                boolean isK8s = milogLogtailDos.stream().anyMatch(logTailDo -> {
                    if (InnerDeployWayEnum.MILINE.getCode().equals(logTailDo.getDeployWay())) {
                        PipelineDeployDto pipelineDeployDto = milineRpcConsumerService.qryDeployInfo(logTailDo.getAppId(), logTailDo.getEnvId());
                        if (null != pipelineDeployDto && pipelineDeployDto.getDeployType() == K8S_DEPLOYED.intValue()) {
                            return true;
                        }
                    }
                    return false;
                });
                if (isK8s) {
                    return queryNodeIpByPodIp(ip);
                }
            }
        } catch (Exception e) {
            log.error(String.format("query ip belong k8s error,milogAppId:%s,ip:%s", milogAppId.intValue(), ip), e);
        }
        return "";
    }

    public String queryNodeIpByPodIp(String ip) {
        try {
            List<LogAgentListBo> logAgentList = k8sRpcComsumerService.getLogAgent(Arrays.asList(ip));
            if (CollectionUtils.isNotEmpty(logAgentList)) {
                return logAgentList.get(0).getAgentIP();
            }
        } catch (Exception e) {
            log.error(String.format("query k8s node ip error,podIp:%s", ip), e);
        }
        return "";
    }

    /**
     * 删除配置，直接发送删除的请求，是会直接删除正在进行的采集任务
     *
     * @param tailId
     * @param milogAppId
     * @param ips
     */
    @Override
    public void publishIncrementDel(Long tailId, Long milogAppId, List<String> ips) {
        log.info("删除配置同步到 logAgent,tailId:{},milogAppId:{},ips:{}", tailId, milogAppId, gson.toJson(ips));
        AppLogMeta appLogMeta = new AppLogMeta();
        LogPattern logPattern = new LogPattern();
        AppBaseInfo appBaseInfo = assemblyAppInfo(milogAppId, appLogMeta);
        logPattern.setLogtailId(tailId);
        logPattern.setOperateEnum(OperateEnum.DELETE_OPERATE);
        //ip转换，ip装为pod ip
        List<String> agentIps = buildAgentIps(appBaseInfo, ips);
        appLogMeta.setLogPatternList(Arrays.asList(logPattern));
        agentIps.forEach(agentIp -> {
            LogCollectMeta logCollectMeta = new LogCollectMeta();
            logCollectMeta.setAgentIp(agentIp);
            logCollectMeta.setAgentMachine("");
            logCollectMeta.setAgentId("");
            logCollectMeta.setAppLogMetaList(Arrays.asList(appLogMeta));
            sendConfigToAgent(agentIp, logCollectMeta);
        });
    }

    public void publishIncrementDelDirect(Long tailId, List<String> ips) {
        AppLogMeta appLogMeta = new AppLogMeta();
        LogPattern logPattern = new LogPattern();
        logPattern.setLogtailId(tailId);
        logPattern.setOperateEnum(OperateEnum.DELETE_OPERATE);
        appLogMeta.setLogPatternList(Lists.newArrayList(logPattern));
        //ip转换，ip装为pod ip
        ips.forEach(agentIp -> {
            LogCollectMeta logCollectMeta = new LogCollectMeta();
            logCollectMeta.setAgentIp(agentIp);
            logCollectMeta.setAgentMachine("");
            logCollectMeta.setAgentId("");
            logCollectMeta.setAppLogMetaList(Arrays.asList(appLogMeta));
            sendConfigToAgent(agentIp, logCollectMeta);
        });
    }


    private List<String> buildAgentIps(AppBaseInfo appBaseInfo, List<String> ips) {
        try {
            if (Objects.equals(PlatFormTypeInnerEnum.CHINA.getCode(), appBaseInfo.getPlatformType()) ||
                    Objects.equals(PlatFormTypeInnerEnum.YOUPIN.getCode(), appBaseInfo.getPlatformType())) {
                List<LogAgentListBo> logAgentList = k8sRpcComsumerService.getLogAgent(ips);
                if (CollectionUtils.isEmpty(logAgentList)) {
                    return ips;
                }
                return logAgentList.stream()
                        .map(LogAgentListBo::getAgentIP)
                        .distinct()
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("buildAgentIps error,appBaseInfo:{},ips:{}", gson.toJson(appBaseInfo), gson.toJson(ips), e);
        }
        return ips;
    }

    private AppBaseInfo assemblyAppInfo(Long milogAppId, AppLogMeta appLogMeta) {
        AppBaseInfo appBaseInfo = heraAppService.queryById(milogAppId);
        appLogMeta.setAppId(milogAppId);
        if (null != appBaseInfo) {
            appLogMeta.setAppName(appBaseInfo.getAppName());
        }
        return appBaseInfo;
    }

    @Override
    public void delLogCollDirectoryByIp(Long tailId, String directory, List<String> ips) {
        modifyLogCollDirectoryByIp(tailId, directory, ips, OperateEnum.DELETE_OPERATE);
    }

    public void modifyLogCollDirectoryByIp(Long tailId, String directory, List<String> ips, OperateEnum operateEnum) {
        log.info("modifyLogCollDirectoryByIp logAgent, tailId:{}, directory:{}, ips:{}, operate:{}", tailId, directory, gson.toJson(ips), operateEnum);
        AppLogMeta appLogMeta = new AppLogMeta();
        LogPattern logPattern = new LogPattern();
        logPattern.setLogtailId(tailId);
        logPattern.setOperateEnum(operateEnum);
        appLogMeta.setLogPatternList(Arrays.asList(logPattern));
        LogCollectMeta logCollectMeta = new LogCollectMeta();

        for (String ip : ips) {
            logCollectMeta.setAgentIp(ip);
            logCollectMeta.setDelDirectory(directory);
            logCollectMeta.setAppLogMetaList(Arrays.asList(appLogMeta));
            sendConfigToAgent(ip, logCollectMeta);
        }
    }


    public void stopLogCollDirectoryByIp(Long tailId, String directory, List<String> ips) {
        modifyLogCollDirectoryByIp(tailId, directory, ips, OperateEnum.STOP_OPERATE);
    }

    @Override
    public Result<String> agentOfflineBatch(MilogAgentIpParam agentIpParam) {
        if (null == agentIpParam || CollectionUtils.isEmpty(agentIpParam.getIps())) {
            return Result.failParam("ip不能为空");
        }
        return null;
    }

    @Override
    public LogCollectMeta getLogCollectMetaFromManager(String ip) {
        Integer agentProcessorKey = 0;
        if (ip.split(SYMBOL_COLON).length > 1) {
            agentProcessorKey = Integer.parseInt(ip.split(SYMBOL_COLON)[1]);
            ip = ip.split(SYMBOL_COLON)[0];
        }
        return agentConfigProcessorMap.getOrDefault(agentProcessorKey, dockerAgentConfigProcessor).queryLogCollectMeta(ip);
    }


    public List<LogAgentListBo> queryK8sPodIps(String agentIP) {
        return k8sRpcComsumerService.queryPodInfoByAgentIp(agentIP);
    }

    public List<MachineBo> query(Long appId) {
        return chinaMilogRpcConsumerService.queryIpsByAppId(appId, null, "");
    }

    /**
     * 查询该物理机IP下的全量配置
     *
     * @param agentId
     * @param agentIp      物理机IP
     * @param agentMachine
     * @return
     */
    public LogCollectMeta queryMilogAgentConfig(String agentId, String agentIp, String agentMachine) {
        LogCollectMeta logCollectMeta = initializeLogCollectMeta(agentIp);

        List<MilogLogTailDo> logTailDos = milogLogtailDao.queryByIp(agentIp);
        if (CollectionUtils.isNotEmpty(logTailDos)) {
            //mife日志一个机器下的配置很多，所以如果是mife的日志特殊处理。缩短rs
            if (isMifeLogTailDos(logTailDos)) {
                return handleMifeLogTails(agentIp, logTailDos, logCollectMeta);
            } else {
                return handleGeneralLogTails(agentIp, logTailDos, logCollectMeta);
            }
        }
        return logCollectMeta;
    }

    private boolean isMifeLogTailDos(List<MilogLogTailDo> logTailDos) {
        return logTailDos.stream().allMatch(logTailDo -> InnerProjectTypeEnum.MIFE_TYPE.getCode().equals(logTailDo.getAppType()));
    }

    private LogCollectMeta handleMifeLogTails(String agentIp, List<MilogLogTailDo> logTailDos, LogCollectMeta logCollectMeta) {
        List<AppLogMeta> appLogMetaList = Lists.newArrayList();
        Map<Long, List<MilogLogTailDo>> logTailDoMap = logTailDos.stream()
                .collect(Collectors.groupingBy(MilogLogTailDo::getMilogAppId));

        logTailDoMap.values().parallelStream().forEach(listEntry -> {
            List<LogPattern> logPatterns = buildLogPatterns(listEntry.get(0).getMilogAppId(), agentIp, listEntry);
            AppLogMeta appLogMeta = new AppLogMeta();
            appLogMeta.setLogPatternList(logPatterns);
            appLogMeta.setAppId(listEntry.get(0).getAppId());
            appLogMeta.setAppName(listEntry.get(0).getAppName());
            appLogMetaList.add(appLogMeta);
        });

        logCollectMeta.setAppLogMetaList(appLogMetaList);
        return logCollectMeta;
    }

    private LogCollectMeta handleGeneralLogTails(String agentIp, List<MilogLogTailDo> logTailDos, LogCollectMeta logCollectMeta) {
        filterPlatformResourceLogTails(logTailDos);
        List<AppBaseInfo> appBaseInfos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(logTailDos)) {
            appBaseInfos = heraAppService.queryByIds(logTailDos.stream().map(MilogLogTailDo::getMilogAppId).collect(Collectors.toList()));
        }
        logCollectMeta.setAppLogMetaList(appBaseInfos.parallelStream()
                .map(appBaseInfo -> assembleSingleConfig(Long.valueOf(appBaseInfo.getId()), queryLogPattern(Long.valueOf(appBaseInfo.getId()), agentIp)))
                .filter(appLogMeta -> CollectionUtils.isNotEmpty(appLogMeta.getLogPatternList()))
                .collect(Collectors.toList()));
        return logCollectMeta;
    }

    private void filterPlatformResourceLogTails(List<MilogLogTailDo> logTailDos) {
        //过滤掉 matrix 类型 sidecar 情况下的配置拉取

        // 获取所有需要查询的storeIds
        Set<Long> storeIds = logTailDos.stream()
                .map(MilogLogTailDo::getStoreId)
                .collect(Collectors.toSet());

        // 查询所有storeIds对应的MilogLogStoreDO对象
        Map<Long, MilogLogStoreDO> storeDOMap = milogLogstoreDao.queryByIds(storeIds.stream().toList())
                .stream()
                .collect(Collectors.toMap(MilogLogStoreDO::getId, Function.identity()));

        // 过滤掉平台资源存储的logTailDos
        logTailDos.removeIf(tail -> {
            MilogLogStoreDO logStoreDO = storeDOMap.get(tail.getStoreId());
            if (null != logStoreDO) {
                return logStoreDO.isPlatformResourceStore();
            }
            return false;
        });
    }

    public LogCollectMeta queryMilogAgentConfigK8s(Long tailId, String agentIp, List<LogAgentListBo> logAgentListBos) {
        LogCollectMeta logCollectMeta = initializeLogCollectMeta(agentIp);
        List<AppBaseInfo> appBaseInfos = Lists.newArrayList();

        appBaseInfos.add(heraAppService.queryById(milogLogtailDao.queryById(tailId).getMilogAppId()));
        logCollectMeta.setAppLogMetaList(appBaseInfos.stream()
                .map(appBaseInfo -> assembleSingleConfig(Long.valueOf(appBaseInfo.getId()), queryLogPattern(tailId, logAgentListBos)))
                .collect(Collectors.toList()));
        return logCollectMeta;
    }


    public LogCollectMeta initializeLogCollectMeta(String agentIp) {
        LogCollectMeta logCollectMeta = new LogCollectMeta();
        logCollectMeta.setAgentIp(agentIp);
        logCollectMeta.setAgentMachine("");
        logCollectMeta.setAgentId("");
        return logCollectMeta;
    }

    /**
     * 组装增量配置
     *
     * @param milogAppId
     * @param logPatternList
     * @return
     */
    public AppLogMeta assembleSingleConfig(Long milogAppId, List<LogPattern> logPatternList) {
        AppBaseInfo appBaseInfo = heraAppService.queryById(milogAppId);
        AppLogMeta appLogMeta = new AppLogMeta();
        if (null != appBaseInfo) {
            appLogMeta.setAppId(Long.valueOf(appBaseInfo.getBindId()));
            appLogMeta.setAppName(appBaseInfo.getAppName());
            appLogMeta.setLogPatternList(logPatternList);
        }
        return appLogMeta;
    }

    private MQConfig decorateMQConfig(MilogLogTailDo milogLogtailDo) {
        MQConfig mqConfig = new MQConfig();
        try {
            Long mqResourceId = LOG_STORE_CACHE.get(milogLogtailDo.getStoreId(), () -> milogLogstoreDao.queryById(milogLogtailDo.getStoreId())).getMqResourceId();
            MilogAppMiddlewareRel milogAppMiddlewareRel = getMilogAppMiddlewareRel(milogLogtailDo, mqResourceId);

            MilogMiddlewareConfig middlewareConfig = WARE_CONFIG_CACHE.get(milogAppMiddlewareRel.getMiddlewareId(), () -> milogMiddlewareConfigDao.queryById(milogAppMiddlewareRel.getMiddlewareId()));
            if (InnerMiddlewareEnum.ROCKETMQ.getCode().equals(middlewareConfig.getType())) {
                mqConfig.setClusterInfo(middlewareConfig.getNameServer());
                fillMqConfigData(mqConfig, InnerMiddlewareEnum.ROCKETMQ.getName(), middlewareConfig, milogAppMiddlewareRel.getConfig());
            }
            if (InnerMiddlewareEnum.TALOS.getCode().equals(middlewareConfig.getType()) || InnerMiddlewareEnum.PLATFORM_DEFAULT_TALOS.getCode().equals(middlewareConfig.getType())) {
                mqConfig.setClusterInfo(middlewareConfig.getServiceUrl());
                fillMqConfigData(mqConfig, InnerMiddlewareEnum.TALOS.getName(), middlewareConfig, milogAppMiddlewareRel.getConfig());
            }
            return mqConfig;
        } catch (Exception e) {
            log.error("组装mq配置信息异常,data:{}", gson.toJson(milogLogtailDo), e);
        }
        return mqConfig;
    }

    private MilogAppMiddlewareRel getMilogAppMiddlewareRel(MilogLogTailDo milogLogtailDo, Long mqResourceId) {
        MilogAppMiddlewareRel milogAppMiddlewareRel = null;

        if (InnerProjectTypeEnum.MIFE_TYPE.getCode().equals(milogLogtailDo.getAppType())) {
            milogAppMiddlewareRel = APP_TYPE_CACHE.getIfPresent(MIFE_TYPE.getCode());
            if (null == milogAppMiddlewareRel) {
                MilogLogTailDo tailDo = innerLogTailDao.queryOneByAppTypeOne(InnerProjectTypeEnum.MIFE_TYPE.getCode());
                if (null != tailDo) {
                    List<MilogAppMiddlewareRel> appMiddlewareRels = milogAppMiddlewareRelDao.queryByCondition(null, mqResourceId, tailDo.getId());
                    if (CollectionUtils.isNotEmpty(appMiddlewareRels)) {
                        APP_TYPE_CACHE.put(MIFE_TYPE.getCode(), appMiddlewareRels.getFirst());
                        milogAppMiddlewareRel = appMiddlewareRels.getFirst();
                    }
                }
            }
        }
        if (null == milogAppMiddlewareRel) {
            List<MilogAppMiddlewareRel> milogAppMiddlewareRels = milogAppMiddlewareRelDao.queryByCondition(milogLogtailDo.getMilogAppId(), mqResourceId, milogLogtailDo.getId());
            if (CollectionUtils.isEmpty(milogAppMiddlewareRels)) {
                milogAppMiddlewareRels = milogAppMiddlewareRelDao.queryByCondition(milogLogtailDo.getMilogAppId(), null, milogLogtailDo.getId());
            }
            milogAppMiddlewareRel = milogAppMiddlewareRels.get(milogAppMiddlewareRels.size() - 1);
        }
        return milogAppMiddlewareRel;
    }

    private void fillMqConfigData(MQConfig mqConfig, String typeName, MilogMiddlewareConfig middlewareConfig, MilogAppMiddlewareRel.Config config) {
        mqConfig.setType(typeName);
        mqConfig.setAk(middlewareConfig.getAk());
        mqConfig.setProducerGroup(config.getConsumerGroup());
        mqConfig.setSk(middlewareConfig.getSk());
        mqConfig.setTopic(config.getTopic());
        mqConfig.setTag(config.getTag());
        mqConfig.setPartitionCnt(config.getPartitionCnt());
        mqConfig.setEsConsumerGroup(config.getEsConsumerGroup());
        mqConfig.setBatchSendSize(config.getBatchSendSize());
    }

    /**
     * 查询该物理机下的应用
     * 1.查询出该物理机下的所有应用
     * 2.找寻出接入过日志的应用
     *
     * @param ip
     * @return 废弃
     */
    @Deprecated
    public List<MilogAppTopicRelDO> queryAppsExistInMachine(String ip) {
        List<MilogAppTopicRelDO> topicRels = Lists.newArrayList();
        List<String> machineInfos;
        if (MilogUserUtil.isYoupin()) {
            machineInfos = youPinMilogRpcConsumerService.queryAppsByIp(ip);
        } else {
            machineInfos = chinaMilogRpcConsumerService.queryAppsByIp(ip);
        }
//        List<MilogAppTopicRelDO> milogAppTopicRels = milogAppTopicRelDao.queryAppTopicList(Cnd.NEW(), null);
//        if (CollectionUtils.isNotEmpty(machineInfos)) {
//            log.info("查询到该Ip下的所有应用为，ip:{},app:{}", ip, machineInfos);
//            final Iterator iterator = milogAppTopicRels.iterator();
//            while (iterator.hasNext()) {
//                final MilogAppTopicRelDO next = (MilogAppTopicRelDO) iterator.next();
//                if (machineInfos.contains(next.getAppId().toString())) {
//                    topicRels.add(next);
//                }
//            }
//        }
//        log.info("该物理机：{}下接入日志的所有项目为：{}", ip, topicRels.stream().map(MilogAppTopicRelDO::getAppName).collect(Collectors.toList()));
        return topicRels;
    }

    public List<String> testQueryProjectByIP(String ip) {
        return chinaMilogRpcConsumerService.queryAppsByIp(ip);
    }

    public List<LogPattern> queryLogPattern(Long tailId, List<LogAgentListBo> logAgentListBos) {
        MilogLogTailDo milogLogtailDo = milogLogtailDao.queryById(tailId);
        if (null != milogLogtailDo) {
            LogPattern logPattern = generateLogPattern(milogLogtailDo);
            try {
                logPattern.setLogPattern(generateK8sTailLogPathK8s(milogLogtailDo.getLogPath(), logAgentListBos, milogLogtailDo.getAppName(), milogLogtailDo.getAppType()));
                logPattern.setLogSplitExpress(generateK8sTailLogPathK8s(milogLogtailDo.getLogSplitExpress(), logAgentListBos, milogLogtailDo.getAppName(), milogLogtailDo.getAppType()));
                String agentIP = logAgentListBos.stream().findFirst().get().getAgentIP();
                logPattern.setIps(generateK8sTailIps(agentIP, logAgentListBos));
                logPattern.setIpDirectoryRel(generateK8sIpRel(agentIP, logAgentListBos));
            } catch (Exception e) {
                log.error("assemble log path data error:", e);
            }
            //设置mq配置
            MQConfig mqConfig = decorateMQConfig(milogLogtailDo);
            logPattern.setMQConfig(mqConfig);
            return Lists.newArrayList(logPattern);
        }
        return Collections.emptyList();
    }


    /**
     * 云平台统一容器场景下的日志采集下发逻辑：cloudml、matrix、miks
     * 基于 tail 下的 pods 组装下发给 agent 的采集： tail->pods 一对多
     *
     * @param tail 单个 tail
     * @param pods 该 tail 对应的 pods，且这些 pods 都在一台宿主机上
     */
    public List<LogPattern> assembleLogPatternForCloudPlatformK8s(String appNamespace, MilogLogTailDo tail, List<LogAgentListBo> pods) {
        if (CollectionUtils.isNotEmpty(pods)) {
            //todo 这个方法里可以把应用类型的判断去掉，抽的更干净点
            if (Strings.isEmpty(appNamespace) &&
                    !MIKS_TYPE.getCode().equals(tail.getAppType())) {
                log.error("assembleLogPatternForCloudPlatformK8s error: got empty appNamespace from trace, skip this round,appType:{}, tailId:{}, appId:{}, pods:{}",
                        InnerProjectTypeEnum.fromCode(tail.getAppType()).toString(), tail.getId(), tail.getAppId(), gson.toJson(pods));
                return Lists.newArrayList();
            }
            log.debug("assembleLogPatternForCloudPlatformK8s, tailId:{},appNamespace:{},pods:{}", tail.getId(), appNamespace, gson.toJson(pods));
            LogPattern logPattern = generateLogPattern(tail);
            try {
                if (CLOUDML_TYPE.getCode().equals(tail.getAppType()) ||
                        MIKS_TYPE.getCode().equals(tail.getAppType())) {
                    logPattern.setLogPattern(generateK8sLikeLogPath(appNamespace, pods,
                            logPattern.getLogPattern()));
                    logPattern.setLogSplitExpress(generateK8sLikeLogPath(appNamespace, pods,
                            logPattern.getLogSplitExpress()));
                } else {
                    logPattern.setLogPattern(generateMatrixLogPath(appNamespace, tail.getDeploySpace(), pods,
                            logPattern.getLogPattern()));
                    logPattern.setLogSplitExpress(generateMatrixLogPath(appNamespace, tail.getDeploySpace(), pods,
                            logPattern.getLogSplitExpress()));
                }
                String agentIP = pods.stream().findFirst().get().getAgentIP();
                logPattern.setIps(generateK8sTailIps(agentIP, pods));
                logPattern.setIpDirectoryRel(generateK8sIpRel(agentIP, pods));
            } catch (Exception e) {
                log.error("assembleLogPatternForCloudPlatformK8s error, tailId:{}, appNamespace:{}, pods:{}, error:", tail.getId(), appNamespace, gson.toJson(pods), e);
            }
            //设置mq配置
            MQConfig mqConfig = decorateMQConfig(tail);
            logPattern.setMQConfig(mqConfig);


            return Lists.newArrayList(logPattern);
        }
        return Collections.emptyList();
    }

    private List<LogPattern> queryLogPattern(Long milogAppId, String agentIp) {
        List<MilogLogTailDo> logTailDos = milogLogtailDao.queryByAppIdAgentIp(milogAppId, agentIp);
        return buildLogPatterns(milogAppId, agentIp, logTailDos);
    }

    @NotNull
    private List<LogPattern> buildLogPatterns(Long milogAppId, String agentIp, List<MilogLogTailDo> logTailDos) {
        if (CollectionUtils.isNotEmpty(logTailDos)) {
            return logTailDos.parallelStream().map(milogLogtailDo -> {
                log.debug("assemble data:{}", milogAppId);
                LogPattern logPattern = generateLogPattern(milogLogtailDo);
                logPattern.setIps(Lists.newArrayList(agentIp));
                logPattern.setIpDirectoryRel(Lists.newArrayList(LogPattern.IPRel.builder().key(getPhysicsDirectory(milogLogtailDo.getLogPath())).ip(agentIp).build()));
                try {
                    if (InnerProjectTypeEnum.MIS_TYPE.getCode().equals(milogLogtailDo.getAppType())) {
                        if (!MachineTypeEnum.PHYSICAL_MACHINE.getType().equals(milogLogtailDo.getMachineType())) {
                            MilogLogStoreDO milogLogstoreDO = milogLogstoreDao.queryById(milogLogtailDo.getStoreId());
                            //如果是欧洲机房 采用的是demonset模式，日志路径需要转化
                            if (InnerMachineRegionEnum.AMS_MACHINE.getEn().equals(milogLogstoreDO.getMachineRoom())) {
                                logPattern.setLogPattern(generateMisTailLogPath(milogLogtailDo.getLogPath(), agentIp, milogLogtailDo.getMotorRooms()));
                                logPattern.setLogSplitExpress(generateMisTailLogPath(milogLogtailDo.getLogSplitExpress(), agentIp, milogLogtailDo.getMotorRooms()));
                                logPattern.setIps(generateMisTailIps(agentIp, milogLogtailDo.getMotorRooms()));
                                logPattern.setIpDirectoryRel(generateMisIpRel(agentIp, milogLogtailDo.getMotorRooms()));
                            }
                        }
                    } else {
                        if (InnerDeployWayEnum.MILINE.getCode().equals(milogLogtailDo.getDeployWay()) &&
                                InnerProjectTypeEnum.buildMoneAppType().contains(milogLogtailDo.getAppType())) {
                            PipelineDeployDto pipelineDeployDto = milineRpcConsumerService.qryDeployInfo(milogLogtailDo.getAppId(), milogLogtailDo.getEnvId());
                            log.info("miline assemble data:{}", gson.toJson(pipelineDeployDto));
                            if (!IGNORE_APP_NAME.equals(milogLogtailDo.getAppName()) && pipelineDeployDto != null && pipelineDeployDto.getDeployType() == 5) {
                                List<LogAgentListBo> agentListBos = k8sRpcComsumerService.getLogAgent(milogLogtailDo.getIps());
                                logPattern.setLogPattern(generateK8sTailLogPath(milogLogtailDo.getLogPath(), agentListBos, milogLogtailDo.getAppType()));
                                logPattern.setLogSplitExpress(generateK8sTailLogPath(milogLogtailDo.getLogSplitExpress(), agentListBos, milogLogtailDo.getAppType()));
                                logPattern.setIps(generateK8sTailIps(agentIp, agentListBos));
                                logPattern.setIpDirectoryRel(generateK8sIpRel(agentIp, agentListBos));
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("assemble log path data error:", e);
                }
                //设置mq配置
                MQConfig mqConfig = decorateMQConfig(milogLogtailDo);
                logPattern.setMQConfig(mqConfig);
                return logPattern;
            }).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    private LogPattern generateLogPattern(MilogLogTailDo milogLogtailDo) {
        LogPattern logPattern = new LogPattern();
        MilogLogStoreDO milogLogstoreDO;
        try {
            milogLogstoreDO = LOG_STORE_CACHE.get(milogLogtailDo.getStoreId(), () -> milogLogstoreDao.queryById(milogLogtailDo.getStoreId()));
        } catch (ExecutionException e) {
            milogLogstoreDO = milogLogstoreDao.queryById(milogLogtailDo.getStoreId());
        }
        logPattern.setLogtailId(milogLogtailDo.getId());
        logPattern.setTailName(milogLogtailDo.getTail());
        logPattern.setLogPattern(milogLogtailDo.getLogPath());
        logPattern.setLogSplitExpress(milogLogtailDo.getLogSplitExpress());
        logPattern.setFilters(milogLogtailDo.getFilter());
        logPattern.setFirstLineReg(milogLogtailDo.getFirstLineReg());
        if (null != milogLogstoreDO && null != milogLogstoreDO.getLogType()) {
            logPattern.setLogType(milogLogstoreDO.getLogType());
            if (InnerLogTypeEnum.NGINX.getType().equals(milogLogstoreDO.getLogType())) {
                logPattern.setLogPattern(milogLogtailDo.getLogPath());
            }
        }
        if (null != milogLogtailDo.getFilterLogLevelList() && !milogLogtailDo.getFilterLogLevelList().isEmpty()) {
            logPattern.setFilterLogLevelList(milogLogtailDo.getFilterLogLevelList());
        }
        String tag = Utils.createTag(milogLogtailDo.getSpaceId(), milogLogtailDo.getStoreId(), milogLogtailDo.getId());
        logPattern.setPatternCode(tag);
        return logPattern;
    }

    private String generateK8sTailLogPath(String logPath, List<LogAgentListBo> agentListBos, Integer appType) {
        if (StringUtils.isEmpty(logPath) || CollectionUtils.isEmpty(agentListBos)) {
            return logPath;
        }
        return generateK8sTailLogPathK8s(logPath, agentListBos, "", appType);
    }

    private static final String SIDECAR_DIRECTORY_PREFIX = "/home/<USER>/log/sidecar/";
    private static final String SIDECAR_DIRECTORY_SUFFIX = "/faas-sidecar/mifaas/server.log";

    private String generateK8sTailLogPathK8s(String logPath, List<LogAgentListBo> logAgentList,
                                             String appName, Integer appType) {
        if (StringUtils.isEmpty(logPath) || logAgentList.isEmpty()) {
            return logPath;
        }
        if (Objects.equals(IGNORE_APP_NAME, appName)) {
            return logPath;
        }
        try {
            String podNames = logAgentList.stream()
                    .sorted(Comparator.comparing(LogAgentListBo::getPodIP))
                    .map(LogAgentListBo::getPodName)
                    .collect(Collectors.joining("|"));
            String logPathSuffix = StringUtils.substringAfter(logPath, LOG_PATH_PREFIX);
            if (!logPathSuffix.startsWith("/")) {
                //处理目录在/home/<USER>/logs/下的情况
                logPathSuffix = STR."/\{StringUtils.substringAfter(logPathSuffix, "/")}";
            }
            String format = String.format(podNames.split("\\|").length > 1 ? "(%s)" : "%s", podNames);
            String businessLogPath = new StringBuilder().append(LOG_PATH_PREFIX)
                    .append("/")
                    .append(format)
                    .append(logPathSuffix).toString();
            if (!Objects.equals(InnerProjectTypeEnum.ODIN_MESH_TYPE.getCode(), appType)) {
                return businessLogPath;
            }
            //mesh层需要加一层采集日志
            String sidecarLogPath = new StringBuilder().append(SIDECAR_DIRECTORY_PREFIX)
                    .append(format)
                    .append(SIDECAR_DIRECTORY_SUFFIX).toString();
            return String.format("%s,%s", businessLogPath, sidecarLogPath);
        } catch (Exception e) {
            log.error(String.format("generateK8sTailLogPath error,logPath:%s,ipList:%s", logPath, gson.toJson(logAgentList)), e);
        }
        return logPath;
    }

    public String generateMisTailLogPath(String logPath, String agentIp, List<MotorRoomDTO> motorRooms) {
        if (StringUtils.isBlank(logPath)) {
            return StringUtils.EMPTY;
        }
        String logPathPrefix = "/home/<USER>/logs";
        String logPathMiddle = "/neo-logs/";
        String logPathSuffix = StringUtils.substringAfter(logPath, logPathPrefix);
        String nodeNames = motorRooms.stream()
                .flatMap(motorRoomDTO -> motorRoomDTO.getPodDTOList().stream())
                .filter(podDTO -> StringUtils.equalsIgnoreCase(agentIp, podDTO.getNodeIP()))
                .sorted(Comparator.comparing(PodDTO::getPodIP))
                .map(PodDTO::getPodName).collect(Collectors.joining("|"));
        return new StringBuilder().append(logPathPrefix)
                .append(logPathMiddle)
                .append(String.format(nodeNames.split("\\|").length > 1 ? "(%s)" : "%s", nodeNames))
                .append(logPathSuffix).toString();
    }

    public List<String> generateMisTailIps(String agentIp, List<MotorRoomDTO> motorRooms) {
        if (StringUtils.isBlank(agentIp)) {
            return Collections.EMPTY_LIST;
        }
        return motorRooms.stream().flatMap(motorRoomDTO -> motorRoomDTO.getPodDTOList().stream())
                .filter(podDTO -> Objects.equals(agentIp, podDTO.getNodeIP()))
                .map(PodDTO::getPodIP)
                .sorted()
                .collect(Collectors.toList());
    }

    public List<LogPattern.IPRel> generateMisIpRel(String agentIp, List<MotorRoomDTO> motorRooms) {
        if (StringUtils.isBlank(agentIp)) {
            return Collections.EMPTY_LIST;
        }
        return motorRooms.stream().flatMap(motorRoomDTO -> motorRoomDTO.getPodDTOList().stream())
                .filter(podDTO -> Objects.equals(agentIp, podDTO.getNodeIP()))
                .map(agentBo -> LogPattern.IPRel.builder()
                        .key(agentBo.getPodName())
                        .ip(agentBo.getPodIP())
                        .build())
                .collect(Collectors.toList());
    }


    public List<String> generateK8sTailIps(String agentIp, List<LogAgentListBo> agentListBos) {
        if (StringUtils.isBlank(agentIp)) {
            return Collections.EMPTY_LIST;
        }
        return agentListBos.stream()
                .filter(podDTO -> Objects.equals(agentIp, podDTO.getAgentIP()))
                .map(LogAgentListBo::getPodIP)
                .sorted()
                .collect(Collectors.toList());
    }

    public List<LogPattern.IPRel> generateK8sIpRel(String agentIp, List<LogAgentListBo> agentListBos) {
        if (StringUtils.isBlank(agentIp)) {
            return Collections.EMPTY_LIST;
        }
        return agentListBos.stream()
                .filter(podDTO -> Objects.equals(agentIp, podDTO.getAgentIP()))
                .map(agentBo -> LogPattern.IPRel
                        .builder()
                        .key(agentBo.getPodName())
                        .ip(agentBo.getPodIP())
                        .build())
                .collect(Collectors.toList());
    }
}
