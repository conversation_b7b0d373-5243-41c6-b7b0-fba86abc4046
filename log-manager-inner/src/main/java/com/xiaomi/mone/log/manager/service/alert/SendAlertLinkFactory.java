package com.xiaomi.mone.log.manager.service.alert;

import com.xiaomi.mone.log.manager.model.bo.alert.AlertCallBackParam;
import com.xiaomi.youpin.docean.Ioc;
import com.xiaomi.youpin.feishu.FeiShu;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/9/2 16:23
 */
public class SendAlertLinkFactory {

    /**
     * @param level
     * @return
     */
    public static SendAlertLink getSendAlertLink(Long alertId, String level, RemoteSendAlertLink.Meta meta,
            String[] receivers, String[] feishuGroups, String[] notifyGroups,
            String callbackUrl, AlertCallBackParam callBackParam) {
        List<RemoteSendAlertLink.Target> targets = new ArrayList<>();
        if (arrayNotEmpty(receivers)) {
            targets.addAll(Arrays.stream(receivers)
                    .map(emailPrefix -> RemoteSendAlertLink.Target.builder().name(emailPrefix).type(3).build())
                    .toList());
        }
        if (arrayNotEmpty(feishuGroups)) {
            targets.addAll(Arrays.stream(feishuGroups)
                    .map(chatId -> RemoteSendAlertLink.Target.builder().name(chatId).type(4).build())
                    .toList());
        }
        if (arrayNotEmpty(notifyGroups)) {
            targets.addAll(Arrays.stream(notifyGroups)
                    .map(notify -> RemoteSendAlertLink.Target.builder().name(notify).type(7).build())
                    .toList());
        }
        SendAlertLink remoteSendAlertLink = getRemoteSendAlertLink(targets, RemoteSendAlertLink.LEVEL2NOX.get(level),
                meta);
        if (StringUtils.isNotEmpty(callbackUrl)) {
            remoteSendAlertLink.appNext(getCallBackSendAlertLink(alertId, callbackUrl, callBackParam));
        }
        return remoteSendAlertLink;
    }
    @Deprecated // 旧的发送方式
    public static SendAlertLink getFeiShuSendAlertLink(FeiShu feiShu, String content, String[] receivers,
            String[] feishuGroups, boolean sendCard) {
        return new FeiShuSendAlertLink(feiShu, content, receivers, feishuGroups, sendCard);
    }
    // 发送至报警平台
    public static SendAlertLink getRemoteSendAlertLink(List<RemoteSendAlertLink.Target> targets, Integer level,
            RemoteSendAlertLink.Meta meta) {
        OkHttpClient httpClient = Ioc.ins().getBean(OkHttpClient.class);
        return new RemoteSendAlertLink(targets, level, meta, null == httpClient ? new OkHttpClient() : httpClient);
    }

    private static CallBackSendAlertLink getCallBackSendAlertLink(Long alertId, String callBackUrl,
            AlertCallBackParam callBackParam) {
        OkHttpClient httpClient = Ioc.ins().getBean(OkHttpClient.class);
        return new CallBackSendAlertLink(callBackUrl, callBackParam,
                null == httpClient ? new OkHttpClient() : httpClient, alertId);
    }

    public static boolean arrayNotEmpty(String[] data) {
        return null != data && data.length > 0;
    }
}
