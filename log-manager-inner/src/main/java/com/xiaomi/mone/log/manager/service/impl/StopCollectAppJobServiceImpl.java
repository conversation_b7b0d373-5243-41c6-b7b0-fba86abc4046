package com.xiaomi.mone.log.manager.service.impl;

import com.alibaba.nacos.api.config.listener.Listener;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.xiaomi.mone.log.manager.model.bo.StopCollectConfig;
import com.xiaomi.mone.log.manager.service.InnerLogAgentService;
import com.xiaomi.mone.log.manager.service.StopCollectAppJobService;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.nacos.NacosConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.Executor;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.INNER_LOG_AGENT_SERVICE;
import static org.apache.ozhera.log.common.Constant.DEFAULT_GROUP_ID;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/10/22 10:58
 */
@Service
@Slf4j
public class StopCollectAppJobServiceImpl implements StopCollectAppJobService {

    @Resource
    private Gson gson;

    @Resource
    private MilogLogTailDao milogLogTailDao;

    @Resource(name = INNER_LOG_AGENT_SERVICE)
    private InnerLogAgentService innerLogAgentService;
    @Resource
    private NacosConfig nacosConfig;

    private static final String STOP_COLLECT_CONFIG = "stop_collect_config";

    /**
     * 1.从nacos中获取需要停止采集的的应用appId和流水线id
     * 2.根据appId和流水线id查找所在的机器列表
     * 3.根据机器列表下发停止采集列表
     */
    @Override
    public void stopCollectLogByConfig() {
        try {
            nacosConfig.addListener(STOP_COLLECT_CONFIG, DEFAULT_GROUP_ID, new Listener() {
                @Override
                public Executor getExecutor() {
                    return null;
                }

                @Override
                public void receiveConfigInfo(String configInfo) {
                    if (StringUtils.isNotBlank(configInfo)) {
                        log.info("StopCollectAppJobServiceImpl receiveConfigInfo:{}", configInfo);
                        TypeToken<List<StopCollectConfig>> token = new TypeToken<>() {
                        };
                        List<StopCollectConfig> stopCollectConfigs = gson.fromJson(configInfo, token);
                        for (StopCollectConfig stopCollectConfig : stopCollectConfigs) {
                            if (!stopCollectConfig.isStarted()) {
                                startedCollectConfig(stopCollectConfig);
                                continue;
                            }
                            if (CollectionUtils.isNotEmpty(stopCollectConfig.getIpList())) {
                                List<MilogLogTailDo> logTailDos = milogLogTailDao.queryByAppAndEnv(stopCollectConfig.getAppId(), stopCollectConfig.getEnvId());
                                for (MilogLogTailDo logTailDo : logTailDos) {
                                    stopCollectLogByIdAndIp(logTailDo.getId(), stopCollectConfig.getIpList());
                                }
                            } else {
                                stopCollectLogByConfig(stopCollectConfig.getAppId(), stopCollectConfig.getEnvId());
                            }
                        }
                    }
                }
            });

        } catch (Exception e) {
            log.error("StopCollectAppJobServiceImpl stopCollectLogByConfig error:{}", e);
        }
    }

    private void startedCollectConfig(StopCollectConfig stopCollectConfig) {
        List<MilogLogTailDo> logTailDos = milogLogTailDao.queryByAppAndEnv(stopCollectConfig.getAppId(), stopCollectConfig.getEnvId());

        if (CollectionUtils.isEmpty(logTailDos)) {
            return;
        }

        boolean hasIpList = CollectionUtils.isNotEmpty(stopCollectConfig.getIpList());

        for (MilogLogTailDo logTailDo : logTailDos) {
            if (hasIpList) {
                innerLogAgentService.publishIncrementConfig(logTailDo.getId(), logTailDo.getMilogAppId(), stopCollectConfig.getIpList());
            } else {
                innerLogAgentService.publishIncrementConfig(logTailDo.getId(), logTailDo.getMilogAppId(), logTailDo.getIps());
            }
        }
    }

    @Override
    public void stopCollectLogByConfig(Long appID, Long envId) {
        List<MilogLogTailDo> logTailDos = milogLogTailDao.queryByAppAndEnv(appID, envId);

        for (MilogLogTailDo milogLogTailDo : logTailDos) {
            innerLogAgentService.publishIncrementDel(milogLogTailDo.getId(), milogLogTailDo.getMilogAppId(), milogLogTailDo.getIps());
        }
    }

    @Override
    public void stopCollectLogByIdAndIp(Long tailId, List<String> ips) {
        innerLogAgentService.publishIncrementDelDirect(tailId, ips);
    }

}
