package com.xiaomi.mone.log.manager.service.remoting.loki;

import cn.hutool.http.HttpStatus;
import com.google.gson.*;
import com.xiaomi.mone.log.manager.common.context.MoneUserContext;
import com.xiaomi.mone.log.manager.model.dto.MergeLogQuery;
import com.xiaomi.mone.log.manager.service.LokiDataService;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import com.xiaomi.cloud.client.Client;
import com.xiaomi.cloud.client.Request;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.ozhera.log.common.Config;
import org.apache.ozhera.log.common.Result;

import java.lang.reflect.Type;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description loki日志查询
 * @date 2024/10/28/11:31
 */
@Service
@Slf4j
public class LokiDataServiceImpl implements LokiDataService {
    private static final OkHttpClient okHttpClient = new OkHttpClient().newBuilder().connectTimeout(60, TimeUnit.SECONDS)
            .readTimeout(10 * 60, TimeUnit.SECONDS)
            .writeTimeout(10 * 60, TimeUnit.SECONDS)
            .connectionPool(new ConnectionPool(50, 5, TimeUnit.MINUTES))
            .build();

    @Value(value = "$loki.domain")
    private String LOKI_DOMAIN;

    public static Gson gson = new GsonBuilder().registerTypeAdapter(Boolean.class, new BooleanToStringSerializer()).create();

    @Override
    public Result<JsonObject> logQuery(MergeLogQuery logQuery) {
        String url = LOKI_DOMAIN + "/api/v1/log/query";
        String requestBody = gson.toJson(logQuery);
        Response response = null;
        JsonObject logData = null;
        try {
            response = request("POST", url, requestBody);
            if (response.code() != HttpStatus.HTTP_OK) {
                log.error("Log query error, loki search error,logQuery:[{}],user:[{}], err:[{}]", logQuery, MoneUserContext.getCurrentUser(), response.message());
                return Result.failParam(response.message());
            }
            String string = response.body().string();
            JsonObject jsonObject = gson.fromJson(string, JsonObject.class);
            logData = jsonObject.getAsJsonObject("data");
            if (null != logData) {
                logData.addProperty("storageType", "Loki");
            } else {
                log.warn("Log query error, loki search error,logQuery:[{}],msg:[{}]", logQuery, string);
            }
        } catch (Exception e) {
            log.error("Log query error, loki search error,logQuery:[{}],user:[{}]", logQuery, MoneUserContext.getCurrentUser(), e);
            return Result.failParam(e.getMessage());
        }
        return Result.success(logData);
    }

    @Override
    public Result<JsonObject> logStatistic(MergeLogQuery logQuery) {
        String url = LOKI_DOMAIN + "/api/v1/log/query/statistic";
        String requestBody = gson.toJson(logQuery);
        Response response = null;
        JsonObject logData = null;
        try {
            response = request("POST", url, requestBody);
            if (response.code() != HttpStatus.HTTP_OK) {
                log.error("Log query errors while query loki and log bar chart statistics report errors[{}],logQuery:[{}],user:[{}]", response.message(), logQuery, MoneUserContext.getCurrentUser());
                return Result.failParam(response.message());
            }
            String string = response.body().string();
            JsonObject jsonObject = gson.fromJson(string, JsonObject.class);
            logData = jsonObject.getAsJsonObject("data");
            logData.addProperty("storageType", "LOKI");
        } catch (Exception e) {
            log.error("Log query errors while query loki and log bar chart statistics report errors[{}],logQuery:[{}],user:[{}]", e, logQuery, MoneUserContext.getCurrentUser(), e);
            return Result.failParam(e.getMessage());
        }
        return Result.success(logData);
    }

    static class BooleanToStringSerializer implements JsonSerializer<Boolean>, JsonDeserializer<Boolean> {
        @Override
        public JsonElement serialize(Boolean src, Type typeOfSrc, JsonSerializationContext context) {
            return new JsonPrimitive(src ? "true" : "false");
        }

        @Override
        public Boolean deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
            // 将字符串转换回布尔值
            String value = json.getAsString();
            return "true".equals(value);
        }
    }

    private Response request(String httpMethod, String url, String httpBody) throws Exception {
        Request iamRequest = new Request();
        iamRequest.setKey(Config.ins().get("xsight.iam_ak",""));
        iamRequest.setSecret(Config.ins().get("xsight.iam_sk",""));
        iamRequest.setMethod(httpMethod);
        iamRequest.setUrl(url);
        iamRequest.setBody(httpBody);
        iamRequest.addHeader("X-Iam-Tree-Id", Config.ins().get("xsight.iam_tree_id","22596"));
        okhttp3.Request signedRequest = Client.signOkhttp(iamRequest);
        Response response = okHttpClient.newCall(signedRequest).execute();
        return response;
    }
}
