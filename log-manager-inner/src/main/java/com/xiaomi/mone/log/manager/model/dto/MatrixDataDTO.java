package com.xiaomi.mone.log.manager.model.dto;

import com.xiaomi.mione.tesla.k8s.bo.LogAgentListBo;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;


/**
 * <AUTHOR>
 * @Description matrix 接口标准返回结构体
 * 在具体类中覆盖 data 字段
 * @date 2022-11-14
 */
@Data
public abstract class MatrixDataDTO implements Serializable {
    private Long code;
    private String message;
    private String level;

    @Data
    public static class DeployUnit {
        private Long id;
    }

    @Data
    public static class Pod {
        private String name;
        private String podIP;
        // Matrix 接口返回的 hostIP 实际为机器名
        private String hostIP;
        private String status;

        public Boolean isAlivePod() {
            return Objects.equals(this.getStatus(), "Running");
        }

        public static Pod agentListBoToPod(LogAgentListBo bo) {
            Pod p = new Pod();
            p.name = bo.getPodName();
            p.podIP = bo.getPodIP();
            p.hostIP = bo.getAgentIP();
            return p;
        }

        public LogAgentListBo toAgentListBo() {
            LogAgentListBo l = new LogAgentListBo();
            l.setAgentIP(hostIP);
            l.setAgentName(hostIP);
            l.setPodName(name);
            l.setPodIP(podIP);
            return l;
        }
    }

    @Data
    public static class PodInfo {
        private String podName;
        private String podIP;
        private String nodeIp;
        private String nodeName;
        private String treeId;
        private int state;
        private int deploySpaceId;

        public Pod toPod() {
            Pod p = new Pod();
            p.setPodIP(this.getPodIP());
            p.setName(this.getPodName());
            p.setHostIP(this.getNodeIp());
            return p;
        }

        public Boolean isAlivePod() {
            return this.getState() != 1;
        }

        public LogAgentListBo toLogAgentListBo() {
            LogAgentListBo l = new LogAgentListBo();
            l.setAgentIP(this.getNodeIp());
            l.setAgentName(this.getNodeName());
            l.setPodName(this.getPodName());
            l.setPodIP(this.getPodIP());
            return l;
        }
    }

}
