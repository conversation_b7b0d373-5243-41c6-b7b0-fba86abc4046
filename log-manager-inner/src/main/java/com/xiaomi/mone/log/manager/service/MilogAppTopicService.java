package com.xiaomi.mone.log.manager.service;


import org.apache.ozhera.log.common.Result;
import org.apache.ozhera.log.manager.model.bo.AccessMilogParam;
import org.apache.ozhera.log.manager.model.bo.AppTopicParam;
import org.apache.ozhera.log.manager.model.dto.MapDTO;
import org.apache.ozhera.log.manager.model.dto.MilogAppConfigTailDTO;
import org.apache.ozhera.log.manager.model.dto.MilogAppOpenVo;
import org.apache.ozhera.log.manager.model.page.PageInfo;
import org.apache.ozhera.log.manager.model.vo.AccessMiLogVo;
import org.apache.ozhera.log.manager.model.vo.LogPathTopicVo;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2021/7/27 11:20
 */
public interface MilogAppTopicService {

    Result<PageInfo<MilogAppConfigTailDTO>> queryAppTopicList(AppTopicParam param);

    Result<String> createTopic(Long appId, String appName);

    Result<String> updateExistsTopic(Long id, String existTopic);

    Result<List<MapDTO>> queryAllExistTopicList();

    Result<String> delTopicRecord(Long appId);

    Result<String> delTopicRecordAll();

    Result<List<MilogAppOpenVo>> queryAllMilogAppList();

    List<LogPathTopicVo> queryTopicConfigByAppId(Long milogAppId);

    Result<AccessMiLogVo> accessToMilog(AccessMilogParam milogParam);
}
