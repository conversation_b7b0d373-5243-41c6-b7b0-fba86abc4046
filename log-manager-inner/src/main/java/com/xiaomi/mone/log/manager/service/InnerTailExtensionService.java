package com.xiaomi.mone.log.manager.service;

import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.google.common.collect.Lists;
import com.xiaomi.mione.tesla.k8s.bo.LogAgentListBo;
import com.xiaomi.mone.enums.*;
import com.xiaomi.mone.log.manager.common.ManagerConfig;
import com.xiaomi.mone.log.manager.dao.InnerLogTailDao;
import com.xiaomi.mone.log.manager.dao.InnerMilogLogStoreDao;
import com.xiaomi.mone.log.manager.dao.alert.AlertDao;
import com.xiaomi.mone.log.manager.model.alert.Alert;
import com.xiaomi.mone.log.manager.model.dto.MetaAppInfoDTO;
import com.xiaomi.mone.log.manager.model.po.InnerMilogLogStoreDO;
import com.xiaomi.mone.log.manager.service.impl.*;
import com.xiaomi.mone.miline.api.bo.common.DeployTypeEnum;
import com.xiaomi.mone.miline.api.bo.common.EnvEnum;
import com.xiaomi.mone.miline.api.dto.PipelineDeployDto;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import com.xiaomi.youpin.gwdash.bo.MachineBo;
import com.xiaomi.youpin.gwdash.bo.MiLogMachineBo;
import com.xiaomi.youpin.gwdash.bo.SimplePipleEnvBo;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.app.api.response.AppBaseInfo;
import org.apache.ozhera.app.enums.OperateEnum;
import org.apache.ozhera.app.enums.ProjectTypeEnum;
import org.apache.ozhera.log.api.enums.AppTypeEnum;
import org.apache.ozhera.log.api.enums.MachineTypeEnum;
import org.apache.ozhera.log.manager.common.exception.MilogManageException;
import org.apache.ozhera.log.manager.dao.MilogAppMiddlewareRelDao;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.dao.MilogLogstoreDao;
import org.apache.ozhera.log.manager.dao.MilogMiddlewareConfigDao;
import org.apache.ozhera.log.manager.model.bo.LogTailParam;
import org.apache.ozhera.log.manager.model.dto.MilogAppEnvDTO;
import org.apache.ozhera.log.manager.model.dto.MotorRoomDTO;
import org.apache.ozhera.log.manager.model.dto.PodDTO;
import org.apache.ozhera.log.manager.model.pojo.MilogAppMiddlewareRel;
import org.apache.ozhera.log.manager.model.pojo.MilogLogStoreDO;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;
import org.apache.ozhera.log.manager.model.pojo.MilogMiddlewareConfig;
import org.apache.ozhera.log.manager.service.extension.tail.TailExtensionService;
import org.apache.ozhera.log.manager.service.impl.LogTailServiceImpl;
import org.apache.ozhera.log.manager.service.nacos.FetchStreamMachineService;
import org.apache.ozhera.log.manager.service.nacos.impl.StreamConfigNacosPublisher;
import org.apache.ozhera.log.model.LogtailConfig;
import org.apache.ozhera.log.model.MiLogStreamConfig;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.xiaomi.mone.enums.InnerLogTypeEnum.isBindMq;
import static com.xiaomi.mone.enums.InnerProjectTypeEnum.*;
import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.*;
import static com.xiaomi.mone.log.manager.common.ManagerConfig.EXECUTOR_COMMON;
import static org.apache.ozhera.log.common.Constant.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/4/10 17:11
 */
@Service(name = INNER_TAIL_SERVICE)
@Slf4j
public class InnerTailExtensionService implements TailExtensionService {

    public static final String LOG_AGENT_YP_SUFFIX = "-youpin";

    public static final String BACKUP_DATA_ID = "log_manage_create_namespace_config_backup";

    public static final String DATA_ID_SUFFIX = "ip";

    @Resource
    private MilogLogstoreDao milogLogstoreDao;

    @Resource
    private LogTailServiceImpl logTailService;

    @Resource
    private InnerMilogAppMiddlewareRelServiceImpl milogAppMiddlewareRelService;

    @Resource
    private ChinaMilogRpcConsumerServiceImpl chinaMilogRpcConsumerService;

    @Resource
    private YouPinMilogRpcConsumerServiceImpl youPinMilogRpcConsumerService;

    @Resource
    private MilineRpcConsumerServiceImpl milineRpcConsumerServiceImpl;

    @Resource
    private CloudPlatformK8sAppServiceImpl cloudPlatformK8sAppService;

    @Resource(name = INNER_LOG_AGENT_SERVICE)
    private InnerLogAgentService milogAgentService;

    @Resource(name = INNER_COMMON_SERVICE)
    private InnerCommonExtensionService commonExtensionService;

    @Resource
    private InnerRocketMqConfigService rocketMqConfigService;

    @Resource
    private MilogAppMiddlewareRelDao milogAppMiddlewareRelDao;

    @Resource
    private MilogMiddlewareConfigDao milogMiddlewareConfigDao;

    @Resource
    private InnerMilogLogStoreDao innerMilogLogStoreDao;

    @Resource
    private AlertDao alertDao;

    @Resource
    private MilogLogTailDao milogLogtailDao;

    @Resource
    private InnerLogTailDao innerLogTailDao;

    @Value("$dubbo.miline.rpc.env")
    private String dubboMilineRpcEnv;

    @Value(value = "$europe.ip.key")
    private String europeIpKey;

    @Value(value = "$app.env")
    private String appEnv;

    @Resource
    private ManagerConfig managerConfig;
    /**
     * list中第一个是线上，第二个是测试
     */
    private static final Map<String, List<String>> milineMachineRoomRel = new HashMap<>() {
        {
            put(InnerMachineRegionEnum.ALSG_MACHINE.getEn(), Lists.newArrayList(EnvEnum.SGP_ONLINE.getName(), EnvEnum.SGP_STAGING.getName()));
            put(InnerMachineRegionEnum.GLOBAL_MACHINE.getEn(), Lists.newArrayList(EnvEnum.GLOBAL_ONLINE.getName(), EnvEnum.GLOBAL_STAGING.getName()));
            put(InnerMachineRegionEnum.AMS_MACHINE.getEn(), Lists.newArrayList(EnvEnum.EUR_ONLINE.getName(), EnvEnum.EUR_ONLINE.getName()));
        }
    };

    @Override
    public boolean tailHandlePreprocessingSwitch(MilogLogStoreDO milogLogStoreDO, LogTailParam milogLogtailParam) {
        return !milogLogStoreDO.isPlatformResourceStore() && isBindMq(milogLogStoreDO.getLogType());
    }

    @Override
    public boolean bindMqResourceSwitch(MilogLogStoreDO logStore, Integer appType) {
        boolean isLokiAppLogType = Objects.equals(InnerLogTypeEnum.LOKI_APP_LOG.getType(), logStore.getLogType());

        if (isLokiAppLogType) {
            return false;
        }

        List<Integer> allowedAppTypes = Arrays.asList(
                InnerProjectTypeEnum.MIONE_TYPE.getCode(),
                InnerProjectTypeEnum.MIS_TYPE.getCode(),
                InnerProjectTypeEnum.RADAR_TYPE.getCode(),
                InnerProjectTypeEnum.MIFAAS_TYPE.getCode(),
                InnerProjectTypeEnum.ODIN_MESH_TYPE.getCode(),
                InnerProjectTypeEnum.RELEASE_TYPE.getCode(),
                InnerProjectTypeEnum.MIKS_TYPE.getCode(),
                InnerProjectTypeEnum.MIFE_TYPE.getCode(),
                InnerProjectTypeEnum.MATRIX_TYPE.getCode()
        );

        return allowedAppTypes.contains(appType) && !logStore.isPlatformResourceStore();
    }

    @Override
    public boolean bindPostProcessSwitch(Long storeId) {
        InnerMilogLogStoreDO logStoreDO = innerMilogLogStoreDao.queryById(storeId);
        if (null != logStoreDO) {
            return logStoreDO.isPlatformResourceStore();
        }
        return false;
    }

    @Override
    public void postProcessing() {

    }

    @Override
    public void defaultBindingAppTailConfigRel(Long id, Long milogAppId, Long middleWareId, String topicName, Integer batchSendSize) {
        milogAppMiddlewareRelService.defaultBindingAppTailConfigRel(id, milogAppId, middleWareId, topicName, batchSendSize);
    }

    @Override
    public void defaultBindingAppTailConfigRelPostProcess(Long spaceId, Long storeId, Long tailId, Long milogAppId, Long storeMqResourceId) {
        milogAppMiddlewareRelService.bindingPlatformTailConfigRel(spaceId, storeId, tailId, milogAppId, storeMqResourceId);
    }

    @Override
    public void sendMessageOnCreate(LogTailParam param, MilogLogTailDo mt, Long milogAppId, boolean supportedConsume) {
        MilogMiddlewareConfig config = milogAppMiddlewareRelService.queryMiddlewareConfig(param.getMiddlewareConfigId());
        /**
         * 创建consumerGroup
         */
        createConsumerGroup(param.getSpaceId(), param.getStoreId(), mt.getId(), config, milogAppId, supportedConsume);
        /**
         * 发送配置信息---log-agent
         */
        if (param.getCollectionReady() && sendConfigToAgent(mt.getAppType())) {
            CompletableFuture.runAsync(() -> logTailService.sengMessageToAgent(milogAppId, mt), EXECUTOR_COMMON);
        }
        /**
         * 发送最终配置信息---log-stream-- 查看日志模板类型，如果是opentelemetry日志，只发送mq不消费
         */
        if (supportedConsume) {
            sendToStream(mt, OperateEnum.ADD_OPERATE.getCode());
        }
    }

    public void sendToStream(MilogLogTailDo mt, Integer type) {
        MilogLogStoreDO milogLogstoreDO = milogLogstoreDao.queryById(mt.getStoreId());
        logTailService.handleNacosConfigByMotorRoom(mt, milogLogstoreDO.getMachineRoom(), type, mt.getAppType());
    }

    @Override
    public void updateSendMsg(MilogLogTailDo milogLogtailDo, List<String> oldIps, boolean supportedConsume) {
        /**
         * 同步log-agent
         */
        if (milogLogtailDo.getCollectionReady() && sendConfigToAgent(milogLogtailDo.getAppType())) {
            CompletableFuture.runAsync(() -> milogAgentService.publishIncrementConfig(milogLogtailDo.getId(), milogLogtailDo.getMilogAppId(), milogLogtailDo.getIps()), EXECUTOR_COMMON);
        }
        /**
         * 同步 log-stream 如果是opentelemetry日志，只发送mq不消费
         */
        if (supportedConsume) {
            List<MilogAppMiddlewareRel> middlewareRels = milogAppMiddlewareRelDao.queryByCondition(milogLogtailDo.getMilogAppId(), null, milogLogtailDo.getId());
            if (CollectionUtils.isNotEmpty(middlewareRels)) {
                createConsumerGroup(milogLogtailDo.getSpaceId(), milogLogtailDo.getStoreId(), milogLogtailDo.getId(), milogMiddlewareConfigDao.queryById(middlewareRels.get(0).getMiddlewareId()), milogLogtailDo.getMilogAppId(), true);
                sendToStream(milogLogtailDo, OperateEnum.UPDATE_OPERATE.getCode());
            }
        }

        CompletableFuture.runAsync(() -> compareChangeDelIps(milogLogtailDo, milogLogtailDo.getMilogAppId(), oldIps), EXECUTOR_COMMON);
    }

    public void compareChangeDelIps(MilogLogTailDo milogLogtailDo, Long milogAppId, List<String> oldIps) {
        if (CollectionUtils.isEmpty(oldIps)) {
            return;
        }
        if (null == milogLogtailDo.getIps()) {
            milogLogtailDo.setIps(new ArrayList<>());
        }
        // tail关掉采集按钮的情况下，要停掉所有的 oldIp 采集
        List<String> delIps = new ArrayList<>(oldIps);
        // 如果开了采集，则只推送给不在待采集列表里的 oldIp
        if (milogLogtailDo.getCollectionReady()) {
            delIps = oldIps.stream().filter(s -> !milogLogtailDo.getIps().contains(s)).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(delIps)) {
            if (MIKS_TYPE.getCode().equals(milogLogtailDo.getAppType()) ||
                    MATRIX_TYPE.getCode().equals(milogLogtailDo.getAppType()) ||
                    CLOUDML_TYPE.getCode().equals(milogLogtailDo.getAppType())) {
                MetaAppInfoDTO appInfoDTO = cloudPlatformK8sAppService.getMetaAppByAppId(milogLogtailDo.getAppId(), true);
                List<LogAgentListBo> pods = appInfoDTO.getPodsByPodIps(delIps);
                for (LogAgentListBo pod : pods) {
                    milogAgentService.delLogCollDirectoryByIp(milogLogtailDo.getId(), pod.getPodName(), Lists.newArrayList(pod.getAgentIP()));
                }
                return;
            }
            milogAgentService.publishIncrementDel(milogLogtailDo.getId(), milogAppId, delIps);
        }
    }

    @Override
    public void logTailDoExtraFiled(MilogLogTailDo milogLogTailDo, MilogLogStoreDO logStoreDO, LogTailParam logTailParam) {
        Integer appType = logTailParam.getAppType();
        List<String> list = logTailParam.getIps();
        if (InnerProjectTypeEnum.MIS_TYPE.getCode().equals(appType)) {
            if (MachineTypeEnum.PHYSICAL_MACHINE.getType().equals(logTailParam.getMachineType())) {
                milogLogTailDo.setIps(list);
            } else {
                List<MotorRoomDTO> motorRooms = logTailParam.getMotorRooms();
                if (InnerMachineRegionEnum.AMS_MACHINE.getEn().equals(logStoreDO.getMachineRoom())) {
                    // mis应用。提取物理机ip
                    milogLogTailDo.setIps(motorRooms.stream()
                            .flatMap(motorRoomDTO -> motorRoomDTO.getPodDTOList().stream())
                            .map(PodDTO::getNodeIP).collect(Collectors.toList()));
                } else {
                    milogLogTailDo.setIps(motorRooms.stream()
                            .flatMap(motorRoomDTO -> motorRoomDTO.getPodDTOList().stream())
                            .map(PodDTO::getPodIP).collect(Collectors.toList()));
                }
                milogLogTailDo.setMotorRooms(motorRooms);
            }
        } else {
            milogLogTailDo.setIps(list);
        }
        if (InnerProjectTypeEnum.RADAR_TYPE.getCode().equals(appType)) {
            List<MotorRoomDTO> motorRooms = logTailParam.getMotorRooms();
            milogLogTailDo.setIps(motorRooms.stream()
                    .flatMap(motorRoomDTO -> motorRoomDTO.getPodDTOList().stream())
                    .map(PodDTO::getPodIP).collect(Collectors.toList()));
            milogLogTailDo.setMotorRooms(motorRooms);
        }
        if (InnerProjectTypeEnum.MIFAAS_TYPE.getCode().equals(logTailParam.getAppType())) {
            milogLogTailDo.setDeployWay(InnerDeployWayEnum.MILINE.getCode());
        }
    }

    @Override
    public void logTailConfigExtraField(LogtailConfig logtailConfig, MilogMiddlewareConfig middlewareConfig) {
        if (InnerMiddlewareEnum.TALOS.getCode().equals(middlewareConfig.getType()) || InnerMiddlewareEnum.PLATFORM_DEFAULT_TALOS.getCode().equals(middlewareConfig.getType())) {
            logtailConfig.setType(InnerMiddlewareEnum.TALOS.getName());
            logtailConfig.setClusterInfo(middlewareConfig.getServiceUrl());
        }
    }

    @Override
    public void logTailDelPostProcess(MilogLogStoreDO logStoreDO, MilogLogTailDo milogLogtailDo) {
        if (logStoreDO.isPlatformResourceStore() && null != logStoreDO.getMqResourceId()) {
            milogAppMiddlewareRelService.unBindingPlatformTailConfigRel(logStoreDO.getId(), milogLogtailDo.getId(), milogLogtailDo.getMilogAppId(), logStoreDO.getMqResourceId());
        }
    }

    @Override
    public List<MilogAppEnvDTO> getEnInfosByAppId(AppBaseInfo appBaseInfo, Long milogAppId, Integer deployWay, String machineRoom) {
        List<SimplePipleEnvBo> simplePipeEnvBos = null;
        Long appId = Long.valueOf(appBaseInfo.getBindId());
        try {
            if (InnerDeployWayEnum.MIONE.getCode().equals(deployWay)) {
                if (InnerProjectSourceEnum.CHINA_SOURCE.getSource().equals(appBaseInfo.getPlatformName())) {
                    simplePipeEnvBos = chinaMilogRpcConsumerService.querySimplePipleEnvBoByProjectId(appId);
                } else {
                    simplePipeEnvBos = youPinMilogRpcConsumerService.querySimplePipleEnvBoByProjectId(appId);
                }
            } else if (InnerDeployWayEnum.MILINE.getCode().equals(deployWay)) {
                String dubboRpcEnv = dubboMilineRpcEnv;
                if (milineMachineRoomRel.containsKey(machineRoom) && managerConfig.isProd()) {
                    dubboRpcEnv = milineMachineRoomRel.get(machineRoom).get(0);
                }
                if (milineMachineRoomRel.containsKey(machineRoom) && !managerConfig.isProd()) {
                    dubboRpcEnv = milineMachineRoomRel.get(machineRoom).get(1);
                }
                simplePipeEnvBos = milineRpcConsumerServiceImpl.querySimplePipleEnvBoByProjectId(appId, dubboRpcEnv);
                if (managerConfig.isStaging()) {
                    List<SimplePipleEnvBo> faaSimplePipeEnvBos = milineRpcConsumerServiceImpl.queryStagingSimplePipleEnvBoByProjectId(appId, dubboMilineRpcEnv);
                    if (CollectionUtils.isNotEmpty(faaSimplePipeEnvBos)) {
                        simplePipeEnvBos.addAll(faaSimplePipeEnvBos);
                    }
                }
            }
        } catch (Exception e) {
            log.error(String.format("query ip error:milogAppId:%s,deployWay:%s", milogAppId, deployWay), e);
        }
        if (appBaseInfo.getAppName().startsWith(AppTypeEnum.LOG_AGENT.getName()) &&
                InnerMachineRegionEnum.CN_MACHINE.getEn().equalsIgnoreCase(machineRoom)) {
            buildDockerMachines(appId, appBaseInfo.getPlatformName(), appBaseInfo.getAppName(), simplePipeEnvBos);
        }
        if (CollectionUtils.isNotEmpty(simplePipeEnvBos)) {
            return simplePipeEnvBos.stream().map(envBo -> MilogAppEnvDTO.builder().label(envBo.getName()).value(envBo.getId()).ips(envBo.getIps()).build()).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    private boolean hasMachinesExtensionInEnvironment(List<SimplePipleEnvBo> environments, Long environmentId) {
        return environments.stream()
                .filter(env -> env.getId().equals(environmentId))
                .findFirst()
                .map(SimplePipleEnvBo::getIps)
                .map(CollectionUtils::isNotEmpty)
                .orElse(false);
    }

    @Override
    public boolean decorateTailDTOValId(Integer logType, Integer appType) {
        boolean isReleaseType = Objects.equals(InnerProjectTypeEnum.RELEASE_TYPE.getCode(), appType);
        boolean isLokiAppLogType = Objects.equals(InnerLogTypeEnum.LOKI_APP_LOG.getType(), logType);

        return isReleaseType ? !isLokiAppLogType : true;
    }

    @Override
    public List<String> getStreamMachineUniqueList(Integer projectTypeCode, String motorRoomEn) {
        List<String> uniqueList = Lists.newArrayList();
        if (ProjectTypeEnum.MIONE_TYPE.getCode().equals(projectTypeCode) ||
                Objects.equals(InnerMachineRegionEnum.CN_MACHINE.getEn(), motorRoomEn)) {
            Set<String> ips = queryStreamMachineIps(InnerAppTypeEnum.LOG_STREAM_ENV);
            if (CollectionUtils.isNotEmpty(ips)) {
                uniqueList = new ArrayList<>(ips);
            }
        } else {
            if (InnerMachineRegionEnum.AMS_MACHINE.getEn().equals(motorRoomEn)) {
                uniqueList.add(europeIpKey);
            } else {
                uniqueList.add(motorRoomEn + ".ip");
            }
        }
        return uniqueList;
    }


    @Override
    public String deleteCheckProcessPre(Long tailId) {
        //如果有日志告警，不能删除，先让去删除对应的日志告警
        List<Alert> alertList = alertDao.queryByTailId(tailId);
        if (CollectionUtils.isNotEmpty(alertList)) {
            return "请先删除对应配置的日志告警";
        }
        return StringUtils.EMPTY;
    }

    @Override
    public String validLogPath(LogTailParam param) {
        if (Objects.equals(InnerProjectTypeEnum.MIONE_TYPE.getCode(), param.getAppType())) {
            // 校验同名日志文件
            List<MilogLogTailDo> appLogTails = milogLogtailDao.queryByMilogAppAndEnv(param.getMilogAppId(), param.getEnvId());
            for (int i = 0; i < appLogTails.size() && null == param.getId(); i++) {
                if (appLogTails.get(i).getLogPath().equals(param.getLogPath())) {
                    return STR."当前部署环境该文件\{param.getLogPath()}已配置日志采集,别名为：\{appLogTails.get(i).getTail()}";
                }
            }
        }
        if (Objects.equals(InnerProjectTypeEnum.MATRIX_TYPE.getCode(), param.getAppType())) {
            // 校验同名日志文件
            List<MilogLogTailDo> appLogTails = milogLogtailDao.queryByMilogAppAndEnvId(param.getMilogAppId(), param.getEnvId());
            for (int i = 0; i < appLogTails.size() && null == param.getId(); i++) {
                if ((null != appLogTails.get(i).getDeploySpace()) && appLogTails.get(i).getDeploySpace().equals(param.getDeploySpace())
                        && appLogTails.get(i).getLogPath().equals(param.getLogPath())) {
                    return STR."当前部署空间该文件\{param.getLogPath()}已配置日志采集,别名为：\{appLogTails.get(i).getTail()}";
                }
            }
        }
        if (Objects.equals(InnerProjectTypeEnum.MIKS_TYPE.getCode(), param.getAppType())) {
            // 校验同名日志文件
            List<MilogLogTailDo> appLogTails = milogLogtailDao.queryByAppId(param.getAppId());
            for (int i = 0; i < appLogTails.size() && null == param.getId(); i++) {
                if (appLogTails.get(i).getEnvName().equals(param.getEnvName()) &&
                        appLogTails.get(i).getLogPath().equals(param.getLogPath())) {
                    return STR."当前部署环境该文件\{param.getLogPath()}已配置日志采集,别名为：\{appLogTails.get(i).getTail()}";
                }
            }
        }
        // 空间 + appName 可以唯一确定 cloudml 应用，插入时判重
        if (Objects.equals(InnerProjectTypeEnum.CLOUDML_TYPE.getCode(), param.getAppType()) && null == param.getId()) {
            val appLogtail = milogLogtailDao.getTailByName(param.getTail(), param.getAppType());
            if (appLogtail != null && appLogtail.getSpaceId().equals(param.getSpaceId())) {
                return STR."当前服务\{param.getAppName()}已配置日志采集，名称为\{param.getTail()}";
            }
        }

        return StringUtils.EMPTY;
    }

    @Override
    public void publishStreamConfigPostProcess(StreamConfigNacosPublisher streamConfigNacosPublisher, Long spaceId, String motorRoomEn) {
        if (shouldProcessBackupTask(managerConfig, motorRoomEn, spaceId)) {
            try {
                String config = getConfigFromNacos(streamConfigNacosPublisher);
                if (StringUtils.isNotEmpty(config)) {
                    MiLogStreamConfig miLogStreamConfig = GSON.fromJson(config, MiLogStreamConfig.class);
                    Map<String, Map<Long, String>> streamConfigConfig = miLogStreamConfig.getConfig();
                    if (!containsSpaceId(streamConfigConfig, spaceId)) {
                        String key = getKeyWithLeastSpaces(streamConfigConfig);
                        String spaceKey = buildSpaceKey(spaceId);
                        streamConfigConfig.get(key).put(spaceId, spaceKey);
                        publishConfigToNacos(streamConfigNacosPublisher, GSON.toJson(miLogStreamConfig));
                    }
                }
            } catch (NacosException e) {
                log.error("publishStreamConfigPostProcess error", e);
            }
        }
    }

    @Override
    public List<String> fetchStreamUniqueKeyList(FetchStreamMachineService fetchStreamMachineService, Long spaceId, String motorRoomEn) {
        if (StringUtils.equalsIgnoreCase(InnerMachineRegionEnum.CN_MACHINE.getEn(), motorRoomEn) && (managerConfig.isStaging() || managerConfig.isLocal())) {
            return getCommonStreamUniqueList(fetchStreamMachineService, motorRoomEn);
        }
        if (InnerMachineRegionEnum.CN_MACHINE.getEn().equals(motorRoomEn)) {
            if (commonExtensionService.matchMatrixSpace(spaceId)) {
                return Lists.newArrayList(String.format("%s.%s", "matrix", DATA_ID_SUFFIX));
            }
            if (isAllMifeTails(spaceId)) {
                return Lists.newArrayList(String.format("%s.%s", "mife", DATA_ID_SUFFIX));
            }
            return getCommonStreamUniqueList(fetchStreamMachineService, motorRoomEn);
        } else {
            return Lists.newArrayList(String.format("%s.%s", motorRoomEn, DATA_ID_SUFFIX));
        }
    }

    private List<String> getCommonStreamUniqueList(FetchStreamMachineService fetchStreamMachineService, String motorRoomEn) {
        List<String> mioneStreamIpList = fetchStreamMachineService.streamMachineUnique();
        if (CollectionUtils.isEmpty(mioneStreamIpList)) {
            mioneStreamIpList = getStreamMachineUniqueList(ProjectTypeEnum.MIONE_TYPE.getCode(), motorRoomEn);
        }
        return mioneStreamIpList;
    }

    private boolean shouldProcessBackupTask(ManagerConfig managerConfig, String motorRoomEn, Long spaceId) {
        return managerConfig.isProd() &&
                Objects.equals(InnerMachineRegionEnum.CN_MACHINE.getEn(), motorRoomEn) &&
                !isAllMifeTails(spaceId);
    }

    private boolean isAllMifeTails(Long spaceId) {
        List<MilogLogTailDo> logTailDos = innerLogTailDao.queryTailsBySpace(spaceId);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(logTailDos)) {
            return logTailDos.stream()
                    .allMatch(tail ->
                            Objects.equals(InnerProjectTypeEnum.MIFE_TYPE.getCode(), tail.getAppType()));
        }
        return false;
    }


    private String getConfigFromNacos(StreamConfigNacosPublisher streamConfigNacosPublisher) throws NacosException {
        ConfigService configService = streamConfigNacosPublisher.getConfigService();
        return configService.getConfig(BACKUP_DATA_ID, DEFAULT_GROUP_ID, DEFAULT_TIME_OUT_MS);
    }

    private boolean containsSpaceId(Map<String, Map<Long, String>> streamConfigConfig, Long spaceId) {
        return streamConfigConfig.values().stream().anyMatch(map -> map.containsKey(spaceId));
    }

    private String getKeyWithLeastSpaces(Map<String, Map<Long, String>> streamConfigConfig) {
        return Collections.min(streamConfigConfig.entrySet(), Comparator.comparingInt(entry -> entry.getValue().size())).getKey();
    }

    private String buildSpaceKey(Long spaceId) {
        return LOG_MANAGE_PREFIX + TAIL_CONFIG_DATA_ID + spaceId;
    }

    private void publishConfigToNacos(StreamConfigNacosPublisher streamConfigNacosPublisher, String config) throws NacosException {
        ConfigService configService = streamConfigNacosPublisher.getConfigService();
        configService.publishConfig(BACKUP_DATA_ID, DEFAULT_GROUP_ID, config);
    }

    /**
     * rpc 查询机器信息--测试环境查询中国区mione / 线上查询有品 mione 由于线上机器用了物理机全部网络
     *
     * @param appTypeEnum
     * @return
     */
    public Set<String> queryStreamMachineIps(InnerAppTypeEnum appTypeEnum) {
        MiLogMachineBo miLogMachineBo;
        if (StringUtils.equals(PRODENV, appEnv)) {
            miLogMachineBo = youPinMilogRpcConsumerService.queryMachineInfoByProject(AppTypeEnum.LOG_MILOG.getName(), appTypeEnum.getName());
        } else {
            miLogMachineBo = milineRpcConsumerServiceImpl.queryMachineInfoByProject(InnerAppTypeEnum.LOG_STREAM.getName(), appTypeEnum.getName());
        }
        return miLogMachineBo.getMachineInfos().stream().map(MachineBo::getIp).collect(Collectors.toSet());
    }

    private void buildDockerMachines(Long appId, String source, String appName, List<SimplePipleEnvBo> simplePipeEnvBos) {
        List<String> machineList;
        try {
            if (!Objects.equals(AppTypeEnum.LOG_AGENT.getName(), appName) &&
                    appName.startsWith(AppTypeEnum.LOG_AGENT.getName()) &&
                    appName.endsWith(LOG_AGENT_YP_SUFFIX)) {
                machineList = youPinMilogRpcConsumerService.getLiveMachines();
            } else {
                machineList = chinaMilogRpcConsumerService.getLiveMachines();
            }

            simplePipeEnvBos.forEach(envBo -> {
                if (CollectionUtils.isNotEmpty(envBo.getIps())) {
                    return;
                }
                /**
                 * 如果是miline且部署方式不为k8s
                 */
                if (InnerProjectSourceEnum.CHINA_SOURCE.getSource().equals(source)) {
                    PipelineDeployDto pipelineDeployDto = milineRpcConsumerServiceImpl.qryDeployInfo(appId, envBo.getId());
                    if (pipelineDeployDto != null && pipelineDeployDto.getDeployType() == DeployTypeEnum.K8S.getId()) {
                        return;
                    }
                }
                envBo.setIps(machineList);
            });
        } catch (Exception e) {
            throw new MilogManageException("query docker machines error,source:" + source, e);
        }
    }

    private void createConsumerGroup(Long spaceId, Long storeId, Long tailId, MilogMiddlewareConfig config, Long milogAppId, boolean notSendStream) {
        if (!notSendStream && config.getType().equals(InnerMiddlewareEnum.ROCKETMQ.getCode())) {
            rocketMqConfigService.createSubscribeGroup(config.getServiceUrl(), config.getAuthorization(), config.getOrgId(),
                    spaceId, storeId, tailId, milogAppId);
        }
    }
}
