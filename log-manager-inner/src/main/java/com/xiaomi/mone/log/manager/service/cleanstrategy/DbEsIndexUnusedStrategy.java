package com.xiaomi.mone.log.manager.service.cleanstrategy;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xiaomi.mone.log.manager.dao.InnerMilogLogStoreDao;
import com.xiaomi.mone.log.manager.model.po.InnerMilogLogStoreDO;
import com.xiaomi.mone.log.manager.model.vo.ClearDtResourceParam;
import com.xiaomi.mone.log.manager.service.InnerEsCluster;
import com.xiaomi.youpin.docean.anno.Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.ozhera.log.manager.common.exception.MilogManageException;
import org.apache.ozhera.log.manager.mapper.MilogEsIndexMapper;
import org.apache.ozhera.log.manager.model.pojo.MilogEsClusterDO;
import org.apache.ozhera.log.manager.model.pojo.MilogEsIndexDO;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;

/**
 * 清理数据库es未被引用的情况
 *
 * @author: songyutong1
 * @date: 2024/09/13/16:27
 */
@Slf4j
@Service
public class DbEsIndexUnusedStrategy extends AbstractCleanStrategy{

    @Resource
    private MilogEsIndexMapper esIndexMapper;

    @Resource
    private InnerEsCluster esCluster;

    @Resource
    private InnerMilogLogStoreDao innerMilogLogStoreDao;

    @Override
    public void clean(ClearDtResourceParam param, String uuid) {
        log.info("uuid:{}, start fix esIndex not in used", uuid);
        MilogEsClusterDO cluster = esCluster.getPlatformEsCluster(param.getMachineRoom());
        List<String> esDeleteInDb = new ArrayList<>();
        if (ObjectUtils.isEmpty(cluster)) {
            log.info("uuid:{}, fix esIndex not in used failed, esCluster not exist, please check the machineRoom:{}", uuid, param.getMachineRoom());
            throw new MilogManageException("fix esIndex not in used failed, esCluster not exist, please check the machineRoom:" + param.getMachineRoom());
        }
        QueryWrapper<MilogEsIndexDO> wrapper = new QueryWrapper<MilogEsIndexDO>().eq("cluster_id", cluster.getId());
        List<MilogEsIndexDO> list = esIndexMapper.selectList(wrapper);
        list.forEach(esIndex -> {
            List<InnerMilogLogStoreDO> logStore = innerMilogLogStoreDao.getStoresByESInfo(esIndex.getIndexName(), cluster.getId());
            if (CollectionUtils.isEmpty(logStore)) {
                esDeleteInDb.add(esIndex.getIndexName());
                if (Boolean.FALSE.equals(param.getClearFlag())) {
                    return;
                }
                esIndexMapper.deleteById(esIndex.getId());
            }
        });
        log.info("uuid:{}, fix esIndex not in used finished, esIndexDeleteInDb:{}", uuid, esDeleteInDb);
    }
}
