package com.xiaomi.mone.log.manager.service.impl;

import com.google.gson.reflect.TypeToken;
import com.xiaomi.mone.app.enums.PlatFormTypeInnerEnum;
import com.xiaomi.mone.log.manager.model.alert.LogNumAlertMsg;
import com.xiaomi.mone.log.manager.model.dto.MisAppInfoDTO;
import com.xiaomi.mone.log.manager.model.dto.MisResponseDTO;
import com.xiaomi.mone.log.manager.service.alert.AlertMessageBuilder;
import com.xiaomi.mone.log.manager.service.alert.FeishuService;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.common.StringUtils;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import com.xiaomi.youpin.docean.plugin.dubbo.anno.Reference;
import com.xiaomi.youpin.gwdash.bo.ProjectRoleBo;
import com.xiaomi.youpin.gwdash.service.GwdashApiService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.ozhera.app.api.response.AppBaseInfo;
import org.apache.ozhera.log.api.service.LogCountService;
import org.apache.ozhera.log.manager.common.exception.MilogManageException;
import org.apache.ozhera.log.manager.mapper.MilogLogCountMapper;
import org.apache.ozhera.log.manager.mapper.MilogLogNumAlertMapper;
import org.apache.ozhera.log.manager.model.pojo.MilogLogNumAlertDO;
import org.apache.ozhera.log.manager.service.impl.HeraAppServiceImpl;
import org.apache.ozhera.log.utils.DateUtils;

import javax.annotation.Resource;
import java.io.Serializable;
import java.text.DecimalFormat;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.xiaomi.mone.log.manager.user.MoneUserDetailService.GSON;


@Slf4j
@Service
@com.xiaomi.youpin.docean.plugin.dubbo.anno.Service(interfaceClass = LogCountService.class, async = true)
public class InnerLogCountServiceImpl implements LogCountService {
    @Resource
    private MilogLogCountMapper logCountMapper;

    @Resource
    private MilogLogNumAlertMapper logNumMapper;

    @Resource
    private HeraAppServiceImpl heraAppService;

    @Resource
    private FeishuService feishuService;

    @Resource
    private OkHttpClient okHttpClient;

    @Reference(interfaceClass = GwdashApiService.class, group = "$dubbo.gwdash.china.group", check = false, timeout = 5000)
    private GwdashApiService gwdashChinaService;

    @Reference(interfaceClass = GwdashApiService.class, group = "$dubbo.gwdash.youpin.group", check = false, timeout = 5000)
    private GwdashApiService gwdashYoupinService;

    @Value("$mis.url")
    private String misUrl;

    @Value("$mis.token")
    private String misToken;

    @Override
    public void alarmLogNum() {
        try {
            Long threshold = 40000000L;
            alarmLogNum(DateUtils.getDaysAgo(1), threshold, Boolean.TRUE);
            deleteHistoryAlertLog(DateUtils.getDaysAgo(60));
            log.info("日志量提醒发送完成");
        } catch (Exception e) {
            log.error("发送日志量提醒错误,[{}]", e.getMessage());
        }

    }

    public void alarmLogNum(String day, Long threshold, boolean isSend) {
        List<Map<String, Object>> appList = logCountMapper.collectAppLog(day, threshold);
        DecimalFormat df = new DecimalFormat("#,###");
        for (Map<String, Object> app : appList) {
            if (app.get("milogAppId") == null) {
                return;
            }
            Long milogAppId = Long.valueOf(String.valueOf(app.get("milogAppId")));
            // 幂等
            if (logNumMapper.isSend(milogAppId, day) != null) {
                continue;
            }
            ProjectInfo projectInfo = null;
            try {
                projectInfo = getProjectInfo(milogAppId);
                if (projectInfo == null) {
                    log.error("发送日志量提醒错误，获取app成员未成功。milogAppId:[{}]", milogAppId);
                    return;
                }
            } catch (Exception e) {
                log.error("发送日志量提醒错误，获取app成员未成功。milogAppId:[{}]", milogAppId, e);
                continue;
            }
            if (isSend) {
                Long number = Long.valueOf(String.valueOf(app.get("number")));
                LogNumAlertMsg logNumAlertMsg = new LogNumAlertMsg(projectInfo.getAppName(), day, df.format(number));
                boolean isTrue = feishuService.sendFeishu(AlertMessageBuilder.buildLogNumAlertMsg(logNumAlertMsg), projectInfo.getMemberArray(), null, true);
                if (isTrue) {
                    logNumMapper.insert(new MilogLogNumAlertDO(day, number, milogAppId, projectInfo.getAppName(), Arrays.toString(projectInfo.getMemberArray()), System.currentTimeMillis()));
                } else {
                    log.error("发送日志量提醒错误, 飞书消息发送未成功。milogAppId:{}, appName:{}, member:{}", milogAppId, projectInfo.getAppName(), Arrays.toString(projectInfo.getMemberArray()));
                }
            }
        }
    }

    public void deleteHistoryAlertLog(String day) {
        if (StringUtils.isEmpty(day)) {
            return;
        }
        logNumMapper.deleteThisDay(day);
    }

    public ProjectInfo getProjectInfo(Long milogAppId) throws Exception {
        if (milogAppId == null) {
            return null;
        }
        AppBaseInfo appBaseInfo = heraAppService.queryById(milogAppId);
        if (null == appBaseInfo || StringUtils.isEmpty(appBaseInfo.getPlatformName())) {
            log.info("query hera app base info not exist,heraAppId:{}", milogAppId);
            return null;
        }
        switch (PlatFormTypeInnerEnum.getEnum(appBaseInfo.getPlatformName())) {
            case CHINA:
                com.xiaomi.youpin.infra.rpc.Result<List<ProjectRoleBo>> chinaMioneRes = gwdashChinaService.getMembersByProjectId(Integer.valueOf(appBaseInfo.getBindId()));
                if (chinaMioneRes == null || chinaMioneRes.getCode() != 0 || chinaMioneRes.getData() == null || chinaMioneRes.getData().isEmpty()) {
                    throw new MilogManageException("query china app members error");
                }
                String chinaMioneAppName = String.valueOf(chinaMioneRes.getData().get(0).getProjectName());
                String[] chinaMioneMemberArray = chinaMioneRes.getData().stream().filter(p -> p != null && StringUtils.isNotEmpty(p.getUserName())).map(ProjectRoleBo::getUserName).toArray(String[]::new);
                return new ProjectInfo(chinaMioneAppName, chinaMioneMemberArray);
            case YOUPIN:
                com.xiaomi.youpin.infra.rpc.Result<List<ProjectRoleBo>> youpinMioneRes = gwdashYoupinService.getMembersByProjectId(Integer.valueOf(appBaseInfo.getBindId()));
                if (youpinMioneRes == null || youpinMioneRes.getCode() != 0 || youpinMioneRes.getData() == null || youpinMioneRes.getData().isEmpty()) {
                    throw new MilogManageException("query youpin app members error");
                }
                String youpinMioneAppName = String.valueOf(youpinMioneRes.getData().get(0).getProjectName());
                String[] youpinMioneMemberArray = youpinMioneRes.getData().stream().filter(p -> p != null && StringUtils.isNotEmpty(p.getUserName())).map(ProjectRoleBo::getUserName).toArray(String[]::new);
                return new ProjectInfo(youpinMioneAppName, youpinMioneMemberArray);
            case MIS:
                String url = misUrl + "/api/service/allservice";
                Request request = new Request.Builder()
                        .url(url)
                        .post(new FormBody.Builder().add("token", misToken).add("id", appBaseInfo.getBindId()).build())
                        .build();
                Response response = okHttpClient.newCall(request).execute();
                if (response.isSuccessful()) {
                    MisResponseDTO<MisAppInfoDTO> rst = GSON.fromJson(response.body().string(), new TypeToken<MisResponseDTO<MisAppInfoDTO>>() {
                    }.getType());
                    MisAppInfoDTO data = rst.getData();
                    return new ProjectInfo(data.getService_cn(), data.getManagers().toArray(new String[data.getManagers().size()]));
                }
            default:
                return new ProjectInfo(appBaseInfo.getAppName(), new String[0]);
        }
    }

    @Data
    @AllArgsConstructor
    public class ProjectInfo implements Serializable {
        private String appName;
        private String[] memberArray;
    }

}
