package com.xiaomi.mone.log.manager.service.alert;

import com.xiaomi.mone.log.manager.model.bo.alert.AlertCallBackParam;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import static com.xiaomi.mone.log.manager.user.MoneUserDetailService.GSON;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/5/24 11:00
 */
@Slf4j
public class CallBackSendAlertLink extends SendAlertLink {

    private final String callBackUrl;
    private final AlertCallBackParam callBackParam;
    private final OkHttpClient httpClient;
    private final Long alertId;

    public CallBackSendAlertLink(String callBackUrl, AlertCallBackParam callBackParam, OkHttpClient httpClient, Long alertId) {
        this.callBackUrl = callBackUrl;
        this.callBackParam = callBackParam;
        this.httpClient = httpClient;
        this.alertId = alertId;
    }

    @Override
    public boolean doSend() {
        String param = GSON.toJson(callBackParam);
        log.info("CallBackSendAlertLink,alertId:{},param:{}", alertId, param);
        try {
            RequestBody requestBody = RequestBody.create(param, MediaType.parse("application/json; charset=utf-8"));
            Request request = new Request.Builder()
                    .url(callBackUrl)
                    .post(requestBody)
                    .build();
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                String rstJson = response.body().string();
                log.info("send callBack succeed, alertId:{}, result:{}", alertId, rstJson);
            } else {
                log.info("send callBack failed, alertId:{}, param:{}", alertId, param);
            }
        } catch (Exception e) {
            log.error(String.format("send callBack exception, alertId:%d, param:%s, url:%s", alertId, param, callBackParam), e);
        }
        SendAlertLink next = super.next();
        if (null == next) {
            return true;
        }
        return next.doSend();
    }
}
