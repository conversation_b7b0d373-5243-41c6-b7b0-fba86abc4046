package com.xiaomi.mone.log.manager.service;

import com.xiaomi.youpin.gwdash.bo.*;
import com.xiaomi.youpin.gwdash.bo.openApi.ProjectDeployInfoQuery;

import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2021/8/17 18:57
 */
public interface MilogRpcConsumerService {

    MiLogMachineBo queryMachineInfoByProject(String projectName, String name);

    Page<ProjectDeployInfoDTO> queryProjectDeployInfoList(ProjectDeployInfoQuery query);

    List<String> queryAppsByIp(String ip);

    List<MachineBo> queryIpsByAppId(Long projectId, Long projectEnvId, String envName);

    List<SimplePipleEnvBo> querySimplePipleEnvBoByProjectId(Long projectId);

    List<SimplePipleEnvBo> querySimplePipleEnvBoByProjectId(Long projectId, String env);


    List<ProjectMemberDTO> queryProjectIdsByUserName(String userName);

    List<Long> queryDeleteProjectId();

    List<SimplePipleEnvBo> queryStagingSimplePipleEnvBoByProjectId(Long projectId, String env);

}
