package com.xiaomi.mone.log.manager.controller;

import cn.hutool.core.lang.Assert;
import com.xiaomi.mone.log.manager.service.LogDiscoverService;
import com.xiaomi.youpin.docean.anno.Controller;
import com.xiaomi.youpin.docean.anno.RequestMapping;
import com.xiaomi.youpin.docean.anno.RequestParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.manager.common.Result;
import org.apache.ozhera.log.manager.model.vo.LogQuery;
import org.elasticsearch.action.fieldcaps.FieldCapabilities;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 实现kibana的discover功能
 * @date 2024/11/5 16:27
 */
@Controller
@Slf4j
public class LogDiscoverController {

    @Resource
    private LogDiscoverService logDiscoverService;

    /**
     * 获取可以聚合的字段
     *
     * @param logQuery
     * @return
     */
    @RequestMapping(path = "/stat/fields")
    public Result<List<FieldCapabilities>> getAggregateFields(@RequestParam("param") LogQuery logQuery) {
        Assert.notNull(logQuery, "logQuery not null");
        Assert.notNull(logQuery.getStoreId(), "logQuery.storeId not null");
        return Result.success(logDiscoverService.getAggregateFields(logQuery));
    }

    /**
     * 获取top5消息
     *
     * @param logQuery
     * @return
     */
    @RequestMapping(path = "/top/five/log")
    public Result<List<Map<String, String>>> getTop5Messages(@RequestParam("param") LogQuery logQuery) {
        return Result.success(logDiscoverService.getTop5Messages(logQuery));
    }
}
