package com.xiaomi.mone.log.manager.service;

import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import com.xiaomi.data.push.client.HttpClientV6;
import com.xiaomi.mone.enums.*;
import com.xiaomi.mone.log.manager.service.impl.InnerRocketMqConfigService;
import com.xiaomi.youpin.docean.anno.Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ozhera.log.api.enums.ResourceEnum;
import org.apache.ozhera.log.common.Constant;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.dao.MilogMiddlewareConfigDao;
import org.apache.ozhera.log.manager.model.dto.DictionaryDTO;
import org.apache.ozhera.log.manager.model.dto.RocketMqResponseDTO;
import org.apache.ozhera.log.manager.model.dto.TopicInfo;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;
import org.apache.ozhera.log.manager.model.pojo.MilogMiddlewareConfig;
import org.apache.ozhera.log.manager.service.CommonRocketMqService;
import org.apache.ozhera.log.manager.service.extension.dictionary.DictionaryExtensionService;
import org.nutz.lang.Strings;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.INNER_DICTIONARY_SERVICE;
import static com.xiaomi.mone.log.manager.service.MoneUserDetailService.GSON;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/4/12 10:42
 */
@Service(name = INNER_DICTIONARY_SERVICE)
@Slf4j
public class InnerDictionaryExtensionService implements DictionaryExtensionService, CommonRocketMqService {

    @Resource
    private MilogMiddlewareConfigDao milogMiddlewareConfigDao;

    @Resource
    private InnerRocketMqConfigService rocketMqConfigService;

    @Resource
    private TalosMqConfigService talosMqConfigService;

    @Resource
    private MilogLogTailDao milogLogtailDao;

    private static ExecutorService THREAD_POOL = ExecutorBuilder.create()
            .setCorePoolSize(5)
            .setMaxPoolSize(30)
            .setWorkQueue(new LinkedBlockingQueue<>(1000))
            .setThreadFactory(ThreadFactoryBuilder.create().setNamePrefix("Log-Manager-Pool-").build())// 线程池命名
            .build();


    @Override
    public List<DictionaryDTO<?>> queryMiddlewareConfigDictionary(String monitorRoomEn) {
        List<MilogMiddlewareConfig> milogMiddlewareConfigs = milogMiddlewareConfigDao.queryCurrentMontorRoomMQ(monitorRoomEn);
        List<DictionaryDTO<?>> dictionaryDTOS = Lists.newArrayList();

        List<InnerMiddlewareEnum> middlewareEnums = Arrays.stream(InnerMiddlewareEnum.values())
                .filter(middlewareEnum -> middlewareEnum == InnerMiddlewareEnum.ROCKETMQ ||
                        middlewareEnum == InnerMiddlewareEnum.TALOS ||
                        middlewareEnum == InnerMiddlewareEnum.PLATFORM_DEFAULT_TALOS).collect(Collectors.toList());
        CountDownLatch countDownLatch = new CountDownLatch(middlewareEnums.size());

        middlewareEnums.forEach(middlewareEnum ->
                CompletableFuture.runAsync(() -> {
                    DictionaryDTO dictionaryDTO = new DictionaryDTO<>();
                    dictionaryDTO.setValue(middlewareEnum.getCode());
                    dictionaryDTO.setLabel(middlewareEnum.getName());
                    if (CollectionUtils.isNotEmpty(milogMiddlewareConfigs)) {
                        List<CompletableFuture<DictionaryDTO<Long>>> completableFutureList = milogMiddlewareConfigs.stream().filter(middlewareConfig -> middlewareEnum.getCode().equals(middlewareConfig.getType()))
                                .map(middlewareConfig -> CompletableFuture.supplyAsync(() -> {
                                    DictionaryDTO<Long> childDictionaryDTO = new DictionaryDTO<>();
                                    childDictionaryDTO.setValue(middlewareConfig.getId());
                                    childDictionaryDTO.setLabel(middlewareConfig.getAlias());
                                    //上海机房由于网络个理获取不到域名，暂时注释掉
                                    if (InnerMachineRegionEnum.SH_MACHINE.getEn().equalsIgnoreCase(middlewareConfig.getRegionEn())) {
                                        return childDictionaryDTO;
                                    }
                                    if (InnerMiddlewareEnum.TALOS.getCode().equals(middlewareConfig.getType()) || InnerMiddlewareEnum.PLATFORM_DEFAULT_TALOS.getCode().equals(middlewareConfig.getType())) {
                                        List<DictionaryDTO> existsTopic = talosMqConfigService.queryExistsTopic(middlewareConfig.getAk(), middlewareConfig.getSk(),
                                                middlewareConfig.getNameServer(), middlewareConfig.getServiceUrl(), middlewareConfig.getAuthorization(), middlewareConfig.getOrgId(), middlewareConfig.getTeamId());
                                        childDictionaryDTO.setChildren(existsTopic);
                                    }
                                    if (InnerMiddlewareEnum.ROCKETMQ.getCode().equals(middlewareConfig.getType())) {
                                        List<DictionaryDTO> existsTopic = rocketMqConfigService.queryExistsTopic(middlewareConfig.getAk(), middlewareConfig.getSk(),
                                                middlewareConfig.getNameServer(), middlewareConfig.getServiceUrl(), middlewareConfig.getAuthorization(), middlewareConfig.getOrgId(), middlewareConfig.getTeamId());
                                        childDictionaryDTO.setChildren(existsTopic);
                                    }
                                    return childDictionaryDTO;
                                }, THREAD_POOL)).toList();
                        CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0])).join();
                        dictionaryDTO.setChildren(completableFutureList.stream().map(CompletableFuture::join).collect(Collectors.toList()));
                    }
                    dictionaryDTOS.add(dictionaryDTO);
                    countDownLatch.countDown();
                }));
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return dictionaryDTOS;
    }

    @Override
    public List<DictionaryDTO<?>> queryResourceDictionary() {
        return generateCommonDictionary(middlewareEnum -> Boolean.TRUE);
    }

    @Override
    public List<DictionaryDTO<?>> queryAppType() {
        return Arrays.stream(InnerProjectTypeEnum.values())
                .map(projectTypeEnum -> {
                    DictionaryDTO<Integer> dictionaryDTO = new DictionaryDTO<>();
                    dictionaryDTO.setValue(projectTypeEnum.getCode());
                    dictionaryDTO.setLabel(projectTypeEnum.getType());
                    if (InnerProjectTypeEnum.MIONE_TYPE == projectTypeEnum ||
                            InnerProjectTypeEnum.MIFAAS_TYPE == projectTypeEnum ||
                            InnerProjectTypeEnum.ODIN_MESH_TYPE == projectTypeEnum) {
                        moneTypeSwitchProcessing(dictionaryDTO);
                    }
                    if (InnerProjectTypeEnum.MIS_TYPE == projectTypeEnum ||
                            InnerProjectTypeEnum.RADAR_TYPE == projectTypeEnum) {
                        misTypeSwitchProcessing(dictionaryDTO);
                    }

                    if (InnerProjectTypeEnum.MATRIX_TYPE == projectTypeEnum) {
                        matrixTypeSwitchProcessing(dictionaryDTO);
                    }
                    if (InnerProjectTypeEnum.MIFE_TYPE == projectTypeEnum) {
                        mifeTypeSwitchProcessing(dictionaryDTO);
                    }
                    if (InnerProjectTypeEnum.RELEASE_TYPE == projectTypeEnum) {
                        dictionaryDTO.setShowMqConfig(Boolean.TRUE);
                    }
                    if (InnerProjectTypeEnum.FLINK_TYPE == projectTypeEnum) {
                        dictionaryDTO.setShowMqConfig(Boolean.TRUE);
                        dictionaryDTO.setShowEnvGroup(Boolean.TRUE);
                    }
                    if (InnerProjectTypeEnum.CLOUDML_TYPE == projectTypeEnum) {
                        dictionaryDTO.setShowMqConfig(Boolean.TRUE);
                    }
                    if (InnerProjectTypeEnum.MIKS_TYPE == projectTypeEnum) {
                        miksTypeSwitchProcessing(dictionaryDTO);
                    }
                    return dictionaryDTO;
                })
                .collect(Collectors.toList());
    }

    private void mifeTypeSwitchProcessing(DictionaryDTO<Integer> dictionaryDTO) {
        dictionaryDTO.setShowEnvGroup(Boolean.TRUE);
        dictionaryDTO.setShowServiceIp(Boolean.TRUE);
        dictionaryDTO.setShowMqConfig(Boolean.TRUE);
    }

    private void matrixTypeSwitchProcessing(DictionaryDTO<Integer> dictionaryDTO) {
        dictionaryDTO.setShowDeploymentSpace(Boolean.TRUE);
        dictionaryDTO.setShowServiceIp(Boolean.TRUE);
        dictionaryDTO.setShowMqConfig(Boolean.TRUE);
    }

    private void miksTypeSwitchProcessing(DictionaryDTO<Integer> dictionaryDTO) {
        dictionaryDTO.setShowMqConfig(Boolean.TRUE);
    }

    private void moneTypeSwitchProcessing(DictionaryDTO<Integer> dictionaryDTO) {
        dictionaryDTO.setShowDeploymentType(Boolean.TRUE);
        dictionaryDTO.setShowEnvGroup(Boolean.TRUE);
        dictionaryDTO.setShowServiceIp(Boolean.TRUE);
        dictionaryDTO.setShowMqConfig(Boolean.TRUE);
    }

    private void misTypeSwitchProcessing(DictionaryDTO<Integer> dictionaryDTO) {
        dictionaryDTO.setShowMachineType(Boolean.TRUE);
        dictionaryDTO.setShowMachineRegion(Boolean.TRUE);
        dictionaryDTO.setShowMqConfig(Boolean.TRUE);
    }

    @Override
    public List<MilogLogTailDo> querySpecialTails() {
        List<MilogLogTailDo> logTailDos = milogLogtailDao.queryByAppType(InnerProjectTypeEnum.MIS_TYPE.getCode());
        return logTailDos;
    }

    @Override
    public List<DictionaryDTO<?>> queryMachineRegion() {
        List<DictionaryDTO<?>> dictionaryDTOS = Lists.newArrayList();
        for (InnerMachineRegionEnum value : InnerMachineRegionEnum.values()) {
            DictionaryDTO dictionaryDTO = new DictionaryDTO();
            dictionaryDTO.setLabel(value.getCn());
            dictionaryDTO.setValue(value.getEn());
            dictionaryDTOS.add(dictionaryDTO);
        }
        return dictionaryDTOS;
    }

    @Override
    public List<DictionaryDTO<?>> queryDeployWay() {
        List<DictionaryDTO<?>> dictionaryDTOS = Lists.newArrayList();
        for (InnerDeployWayEnum value : InnerDeployWayEnum.values()) {
            DictionaryDTO dictionaryDTO = new DictionaryDTO();
            dictionaryDTO.setLabel(value.getName());
            dictionaryDTO.setValue(value.getCode());
            dictionaryDTOS.add(dictionaryDTO);
        }
        return dictionaryDTOS;
    }

    @Override
    public List<DictionaryDTO<?>> queryResourceTypeDictionary() {
        return generateCommonDictionary(middlewareEnum ->
                ResourceEnum.MQ == middlewareEnum ||
                        ResourceEnum.STORAGE == middlewareEnum);
    }

    @Override
    public List<DictionaryDTO> queryExistsTopic(String ak, String sk, String nameServer, String serviceUrl, String authorization, String orgId, String teamId) {
        List<DictionaryDTO> dictionaryDTOS = Lists.newArrayList();
        String returnGet = HttpClientV6.get(rocketmqAddress + "/topic/getTopicList", getSendMqHeader(authorization));
        log.info("【RocketMQ查询topic列表】返回值:{}", returnGet);
        try {
            RocketMqResponseDTO<List<LinkedTreeMap>> responseDTO = GSON.fromJson(returnGet, RocketMqResponseDTO.class);

            if (responseDTO.getCode().compareTo(Constant.SUCCESS_CODE) == 0
                    && Strings.equals(Constant.SUCCESS_MESSAGE, responseDTO.getMessage())) {
                log.info("【RocketMQ查询topic列表】:成功", returnGet);
                List<LinkedTreeMap> dataList = responseDTO.getData();
                dataList.forEach(data -> {
                    TopicInfo topicInfo = GSON.fromJson(GSON.toJson(data), TopicInfo.class);
                    if (Objects.equals(orgId, topicInfo.getOrgId())) {
                        DictionaryDTO<String> dictionaryDTO = new DictionaryDTO<>();
                        dictionaryDTO.setValue(topicInfo.getName());
                        dictionaryDTO.setLabel(topicInfo.getName());
                        dictionaryDTOS.add(dictionaryDTO);
                    }
                });
            } else {
                log.error("【RocketMQ查询topic列表】:失败,失败原因：{}", returnGet);
            }
        } catch (Exception e) {
            log.error(String.format("【RocketMQ查询topic列表】:返回值转化异常，返回值：%s:", returnGet), e);
        }
        return dictionaryDTOS;
    }

    @Override
    public List<DictionaryDTO<?>> queryMQDictionary() {
        return Arrays.stream(InnerMQSourceEnum.values()).sorted(Comparator.comparing(InnerMQSourceEnum::getSort))
                .map(mqSourceEnum -> {
                    DictionaryDTO<Integer> dictionaryDTO = new DictionaryDTO<>();
                    dictionaryDTO.setValue(mqSourceEnum.getCode());
                    dictionaryDTO.setLabel(mqSourceEnum.getName());
                    if (InnerMQSourceEnum.ROCKETMQ == mqSourceEnum) {
                        dictionaryDTO.setShowBrokerName(Boolean.TRUE);
                    }
                    if (InnerMQSourceEnum.TALOS == mqSourceEnum) {
                        dictionaryDTO.setShowOrgInfo(Boolean.TRUE);
                    }
                    return dictionaryDTO;
                }).collect(Collectors.toList());
    }

    private List<DictionaryDTO<?>> generateCommonDictionary(Predicate<ResourceEnum> filter) {
        List<DictionaryDTO> rDictionaryDTOS = Arrays.stream(InnerMachineRegionEnum.values())
                .map(machineRegionEnum ->
                        DictionaryDTO.Of(machineRegionEnum.getEn(), machineRegionEnum.getCn()))
                .collect(Collectors.toList());
        return Arrays.stream(ResourceEnum.values())
                .filter(filter)
                .map(middlewareEnum -> {
                    DictionaryDTO<Integer> dictionaryDTO = new DictionaryDTO<>();
                    dictionaryDTO.setValue(middlewareEnum.getCode());
                    dictionaryDTO.setLabel(middlewareEnum.getName());
                    dictionaryDTO.setChildren(rDictionaryDTOS);
                    return dictionaryDTO;
                }).collect(Collectors.toList());
    }
}
