package com.xiaomi.mone.log.manager.service.impl;

import com.alibaba.nacos.api.config.ConfigService;
import com.google.gson.reflect.TypeToken;
import com.xiaomi.mone.log.manager.dao.InnerLogTailDao;
import com.xiaomi.mone.log.manager.model.dto.AppEnvChangeDTO;
import com.xiaomi.mone.log.manager.service.AppEnvIpChangeService;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import com.xiaomi.youpin.gwdash.bo.SimplePipleEnvBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;
import org.apache.ozhera.log.manager.service.extension.agent.MilogAgentServiceImpl;
import org.apache.ozhera.log.manager.service.nacos.MultipleNacosConfig;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.xiaomi.mone.log.manager.user.MoneUserDetailService.GSON;
import static org.apache.ozhera.log.common.Constant.*;
import static org.apache.ozhera.log.manager.service.extension.agent.MilogAgentService.DEFAULT_AGENT_EXTENSION_SERVICE_KEY;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/9/19 17:28
 */
@Slf4j
@Service
public class AppEnvIpChangeServiceImpl implements AppEnvIpChangeService {

    private static final String APP_IP_CHANGE_DATA_ID = "app_ip_change_config";

    @Resource
    private InnerLogTailDao logTailDao;

    @Resource
    private MilineRpcConsumerServiceImpl milineRpcConsumerService;

    @Value("$dubbo.miline.rpc.env")
    private String dubboMilineRpcEnv;

    @Resource(name = DEFAULT_AGENT_EXTENSION_SERVICE_KEY)
    private MilogAgentServiceImpl logAgentService;

    @Resource
    private MilogLogTailDao milogLogtailDao;

    @Value(value = "$defaultNacosAddres")
    private String defaultNacosAddress;

    @Override
    public void ipChangeConsume() {
        try {
            ConfigService configService = MultipleNacosConfig.getConfigService(defaultNacosAddress);
            if (configService == null) {
                return;
            }

            String configStr = configService.getConfig(APP_IP_CHANGE_DATA_ID, DEFAULT_GROUP_ID, DEFAULT_TIME_OUT_MS);
            if (StringUtils.isBlank(configStr)) {
                return;
            }
            TypeToken<List<AppEnvChangeDTO>> token = new TypeToken<List<AppEnvChangeDTO>>() {
            };

            List<AppEnvChangeDTO> appEnvChangeDTOS = GSON.fromJson(configStr, token.getType());

            for (AppEnvChangeDTO appEnvChangeDTO : appEnvChangeDTOS) {
                processAppEnvChange(appEnvChangeDTO);
            }
        } catch (Throwable e) {
            log.error("ipChangeConsume error", e);
        }
    }

    private void processAppEnvChange(AppEnvChangeDTO appEnvChangeDTO) {
        try {
            List<SimplePipleEnvBo> simplePipeEnvBos = milineRpcConsumerService.querySimplePipleEnvBoByProjectId(appEnvChangeDTO.getAppId(), dubboMilineRpcEnv);

            for (Long envId : appEnvChangeDTO.getEnvIds()) {
                List<MilogLogTailDo> logTailDos = logTailDao.queryByAppAndEnv(appEnvChangeDTO.getAppId(), envId);

                if (CollectionUtils.isNotEmpty(logTailDos)) {
                    for (MilogLogTailDo logTailDo : logTailDos) {
                        List<String> oldIps = logTailDo.getIps();
                        SimplePipleEnvBo envBo = simplePipeEnvBos.stream()
                                .filter(simplePipleEnvBo -> Objects.equals(envId, simplePipleEnvBo.getId()))
                                .findFirst().orElse(null);
                        List<String> newIps = (envBo != null) ? envBo.getIps() : Collections.emptyList();

                        if (null != newIps && (null == oldIps || !CollectionUtils.isEqualCollection(oldIps, newIps))) {
                            log.info("processAppEnvChange,appId:{},envId:{},oldIps{},newIps:{}", appEnvChangeDTO.getAppId(),
                                    envId, GSON.toJson(oldIps), GSON.toJson(newIps));
                            updateLogTailIps(logTailDo, newIps);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error processing AppEnvChangeDTO,data:{}", GSON.toJson(appEnvChangeDTO), e);
        }
    }

    private void updateLogTailIps(MilogLogTailDo logTailDo, List<String> newIps) {
        try {
            logTailDo.setIps(newIps);
            logTailDo.setUtime(Instant.now().toEpochMilli());
            logTailDo.setUpdater("ipChangeConsume-" + DEFAULT_JOB_OPERATOR);
            milogLogtailDao.updateIps(logTailDo);
            for (String ip : newIps) {
                logAgentService.configIssueAgent("", ip, "");
            }
        } catch (Exception e) {
            log.error("updateLogTailIps error,logTailDo:{},newIps:{}",
                    GSON.toJson(logTailDo), GSON.toJson(newIps));
        }
    }
}
