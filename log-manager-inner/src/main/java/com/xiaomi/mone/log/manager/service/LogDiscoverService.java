package com.xiaomi.mone.log.manager.service;

import org.apache.ozhera.log.manager.model.vo.LogQuery;
import org.elasticsearch.action.fieldcaps.FieldCapabilities;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/11/4 15:32
 */
public interface LogDiscoverService {

    List<FieldCapabilities> getAggregateFields(LogQuery logQuery);

    List<Map<String, String>> getTop5Messages(LogQuery logQuery);

    void removeCache(Long storeId);
}

