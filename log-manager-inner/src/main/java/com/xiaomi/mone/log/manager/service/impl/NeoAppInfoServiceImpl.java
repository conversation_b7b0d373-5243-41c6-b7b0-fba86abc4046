package com.xiaomi.mone.log.manager.service.impl;

import com.google.common.collect.Lists;
import com.xiaomi.mone.log.manager.service.NeoAppInfoService;
import com.xiaomi.mone.log.manager.service.remoting.NeoHttp;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.manager.model.dto.PodDTO;
import org.apache.ozhera.log.manager.model.dto.RegionDTO;
import org.apache.ozhera.log.manager.model.dto.ZoneDTO;
import org.apache.ozhera.log.manager.model.pojo.MilogRegionAvailableZoneDO;
import org.codehaus.jackson.JsonNode;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.node.ArrayNode;
import org.codehaus.jackson.node.ObjectNode;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.apache.ozhera.log.common.Constant.GSON;


/**
 * <AUTHOR>
 * @date 2021-10-15
 */
@Service
@Slf4j
public class NeoAppInfoServiceImpl implements NeoAppInfoService {

    @Resource
    private NeoHttp neoHttp;

    @Resource
    private RegionAvailableZoneServiceImpl regionAvailableZoneService;

    @Value("$app.env")
    private String env;

    private static final String NOT_FOUND_CODE = "100032404";

    @Override
    public List<RegionDTO> getNeoAppInfo(List<String> treeIds) {

        List<RegionDTO> regionDTOList = new ArrayList<>();
        if (null == treeIds || treeIds.isEmpty()) {
            return regionDTOList;
        }

        List<String> neos = new ArrayList<>();
        treeIds.forEach(o -> {
            neos.add(neoHttp.getNeoInfo(o));
        });
        neos.forEach(o -> {
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                JsonNode node = objectMapper.readTree(o);
                if (node instanceof ArrayNode && node.size() == 0) {
                    return;
                }
                if (null != node.get("code") && Objects.equals(NOT_FOUND_CODE, node.get("code").asText())) {
                    //未找到服务树
                    log.debug("未找到服务树，treeIds:{}", GSON.toJson(treeIds), GSON.toJson(o));
                    return;
                }
                if (node instanceof ObjectNode && null != node.get("message") && node.get("message").asText().equals("未找到服务树")) {
                    return;
                }
                //遍历neo对象
                node.forEach(x -> {
                    // 本工程生产环境标记是prod，neo\treepath生产环境标记是pro
                    List<String> envs = Lists.newArrayList();
                    if ("prod".equals(env)) {
                        envs.add("pro");
                        envs.add("pre");
                    } else {
                        envs.add(env);
                    }
                    for (String env : envs) {
                        try {
                            String treePath = x.get("treePath").toString().substring(1, x.get("treePath").toString().length() - 1);
                            if (!StringUtils.equalsIgnoreCase(env, StringUtils.substringAfterLast(treePath, "-"))) {
                                continue;
                            }
                            JsonNode podList = x.get("podList");
                            if (podList != null && !"[]".equals(podList.toString()) && !"".equals(podList.toString())) {
                                // 遍历podList
                                podList.forEach(p -> {
                                    buildRegionZonePod(p, regionDTOList);
                                });
                            }
                        } catch (Exception e) {
                            log.error("treeIds:{},get pod info error,neo:{},异常:", GSON.toJson(treeIds), GSON.toJson(o), e);
                        }
                    }
                });
            } catch (IOException e) {
                log.error("转neo数据异常,neo:{},treeIds:{},异常:", GSON.toJson(o), GSON.toJson(treeIds), e);
            }
        });
        return regionDTOList;
    }

    private void buildRegionZonePod(JsonNode p, List<RegionDTO> regionDTOList) {
        if (p.get("ready") == null || !"true".equals(p.get("ready").toString())) {
            return;
        }
        String zoneEN = p.get("idc").toString().substring(1, p.get("idc").toString().length() - 1);
        //取region
        MilogRegionAvailableZoneDO regionZone = regionAvailableZoneService.getRegionAndZone(zoneEN);
        if (regionZone == null) {
            return;
        }
        String zoneCN = regionZone.getZoneNameCN();
        String regionEN = regionZone.getRegionNameEN();
        String regionCN = regionZone.getRegionNameCN();
        String podName = p.get("podName").toString().substring(1, p.get("podName").toString().length() - 1);
        String podIP = p.get("podIP").toString().substring(1, p.get("podIP").toString().length() - 1);
        String nodeName = p.get("nodeName").toString().substring(1, p.get("nodeName").toString().length() - 1);
        String nodeIP = p.get("nodeIP").toString().substring(1, p.get("nodeIP").toString().length() - 1);//System.getenv("host.ip") == null ? NetUtils.getLocalHost() : System.getenv("host.ip");
        boolean regionExist = false;
        for (RegionDTO regionDTO : regionDTOList) {
            if (regionEN.equals(regionDTO.getRegionNameEN())) {
                List<ZoneDTO> zoneDTOList = regionDTO.getZoneDTOList();
                if (zoneDTOList != null && !zoneDTOList.isEmpty()) {
                    boolean zoneExist = false;
                    for (ZoneDTO zoneDTO : zoneDTOList) {
                        if (zoneEN.equals(zoneDTO.getZoneNameEN())) {
                            zoneExist = true;
                            PodDTO podDTO = PodDTO.builder().podIP(podIP).podName(podName).nodeIP(nodeIP).nodeName(nodeName).build();//new PodDTO();
                            List<PodDTO> podDTOList = zoneDTO.getPodDTOList();
                            if (podDTO == null) {
                                podDTOList = new ArrayList<>();
                            }
                            podDTOList.add(podDTO);
                            podDTOList = podDTOList.stream().distinct().collect(Collectors.toList());
                            zoneDTO.setPodDTOList(podDTOList);
                            break;
                        }
                    }
                    if (!zoneExist) {
                        ZoneDTO zoneDTO = buildZone(zoneEN, zoneCN, podIP, podName, nodeIP, nodeName);
                        regionDTO.getZoneDTOList().add(zoneDTO);
                    }
                } else {
                    ZoneDTO zoneDTO = buildZone(zoneEN, zoneCN, podIP, podName, nodeIP, nodeName);
                    zoneDTOList = new ArrayList<>();
                    zoneDTOList.add(zoneDTO);
                    regionDTO.setZoneDTOList(zoneDTOList);
                }

                regionExist = true;
                break;
            }
        }
        if (!regionExist) {
            RegionDTO regionDTO = RegionDTO.builder().regionNameEN(regionEN).regionNameCN(regionCN).build();
            ZoneDTO zoneDTO = buildZone(zoneEN, zoneCN, podIP, podName, nodeIP, nodeName);
            List<ZoneDTO> zoneDTOList = new ArrayList<>();
            zoneDTOList.add(zoneDTO);
            regionDTO.setZoneDTOList(zoneDTOList);
            regionDTOList.add(regionDTO);
        }
    }

    private ZoneDTO buildZone(String zoneEN, String zoneCN, String podIP, String podName, String nodeIP, String nodeName) {
        ZoneDTO zoneDTO = ZoneDTO.builder().zoneNameEN(zoneEN).zoneNameCN(zoneCN).build();
        List<PodDTO> podDTOList = buildPodList(podIP, podName, nodeIP, nodeName);
        zoneDTO.setPodDTOList(podDTOList);
        return zoneDTO;
    }

    private List<PodDTO> buildPodList(String podIP, String podName, String nodeIP, String nodeName) {
        PodDTO podDTO = PodDTO.builder().podIP(podIP).podName(podName).nodeIP(nodeIP).nodeName(nodeName).build();
        List<PodDTO> podDTOList = new ArrayList<>();
        podDTOList.add(podDTO);
        return podDTOList;
    }

}
