package com.xiaomi.mone.log.manager.common.utils;

import com.xiaomi.youpin.docean.plugin.es.EsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.ozhera.log.common.Constant;
import org.apache.ozhera.log.manager.model.pojo.MilogEsClusterDO;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 合并日志查询
 * @date 2024/10/28/14:29
 */
@Slf4j
public class EsUtils {

    public static final Long DISABLE_LIFECYCLE = -1L;

    public static EsService createEsService(MilogEsClusterDO cluster) {
        switch (cluster.getConWay()) {
            case Constant.ES_CONWAY_PWD:
                return new EsService(cluster.getAddr(), cluster.getUser(), cluster.getPwd());
            case Constant.ES_CONWAY_TOKEN:
                return new EsService(cluster.getAddr(), cluster.getToken(), cluster.getDtCatalog(), cluster.getDtDatabase());
            default:
                log.warn("The ES cluster entered an exception: [{}]", cluster);
                throw new IllegalArgumentException("Invalid ES connection way");
        }
    }

    public static Long getEsIndexStoragePeriod(String lifeCycle, Long defaultValue) {
        if (StringUtils.isBlank(lifeCycle) || lifeCycle.contains("Disable")) {
            return DISABLE_LIFECYCLE;
        }
        Long holdTime = defaultValue;

        // 查cold
        if (lifeCycle.contains("Cold")) {
            int index = lifeCycle.indexOf("Cold");
            lifeCycle = lifeCycle.substring(0, index);
            lifeCycle = lifeCycle.substring(lifeCycle.lastIndexOf("_") + 1, index);
        } else if (lifeCycle.contains("Del")) {
            // 查del
            int index = lifeCycle.indexOf("Del");
            lifeCycle = lifeCycle.substring(0, index);
            lifeCycle = lifeCycle.substring(lifeCycle.lastIndexOf("_") + 1, index);
        }
        if (NumberUtils.isNumber(lifeCycle)) {
            holdTime = Long.parseLong(lifeCycle);
        }
        return holdTime;
    }

}
