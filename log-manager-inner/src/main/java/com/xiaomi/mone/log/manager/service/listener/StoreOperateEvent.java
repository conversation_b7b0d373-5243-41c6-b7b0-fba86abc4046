package com.xiaomi.mone.log.manager.service.listener;

import com.xiaomi.youpin.docean.listener.event.Event;
import com.xiaomi.youpin.docean.listener.event.EventType;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.ozhera.log.api.enums.OperateEnum;
import org.apache.ozhera.log.manager.model.pojo.MilogLogStoreDO;
import org.apache.ozhera.log.manager.model.vo.LogStoreParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/2/29 10:06
 */
@Getter
@Setter
public class StoreOperateEvent extends Event {

    private OperateEnum operateEnum;

    private MilogLogStoreDO storeDO;

    private LogStoreParam storeParam;

    public StoreOperateEvent(EventType eventType, OperateEnum operateEnum, MilogLogStoreDO storeDO, LogStoreParam storeParam) {
        super(eventType);
        this.operateEnum = operateEnum;
        this.storeDO = storeDO;
        this.storeParam = storeParam;
    }
}
