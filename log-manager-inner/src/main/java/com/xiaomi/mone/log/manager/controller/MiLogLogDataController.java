package com.xiaomi.mone.log.manager.controller;

import com.google.gson.JsonObject;
import com.xiaomi.mone.log.manager.model.dto.MergeLogQuery;
import com.xiaomi.mone.log.manager.service.MergeLogDataService;
import com.xiaomi.youpin.docean.anno.Controller;
import com.xiaomi.youpin.docean.anno.RequestMapping;
import org.apache.ozhera.log.common.Result;

import java.io.IOException;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 日志查询接口
 * @date 2024/10/28/11:01
 */
@Controller
public class MiLogLogDataController {

    @Resource
    private MergeLogDataService mergeLogDataService;

    @RequestMapping(path = "/milog/merge/log/query")
    public Result<JsonObject> logQuery(MergeLogQuery logQuery) throws IOException {
        return mergeLogDataService.logQuery(logQuery);
    }

    @RequestMapping(path = "/milog/merge/statistic")
    public Result<JsonObject> logStatistic(MergeLogQuery logQuery) throws IOException {
        return mergeLogDataService.logStatistic(logQuery);
    }
}
