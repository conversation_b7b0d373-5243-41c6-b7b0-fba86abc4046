package com.xiaomi.mone.log.manager.service;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.xiaomi.mione.tesla.k8s.bo.LogAgentListBo;
import com.xiaomi.mone.log.manager.service.impl.CloudMLLogServiceImpl;
import com.xiaomi.mone.log.manager.service.impl.MatrixLogServiceImpl;
import com.xiaomi.youpin.docean.anno.Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ozhera.log.api.model.meta.LogCollectMeta;
import org.apache.ozhera.log.api.model.meta.LogPattern;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.INNER_LOG_AGENT_SERVICE;

/**
 * @Description
 * @auther tangdong
 * @create 2024-06-21 12:17 下午
 */
@Service
@Slf4j
public class CloudMLAgentConfigProcessor implements AgentConfigProcessor {

    @Resource(name = INNER_LOG_AGENT_SERVICE)
    private InnerLogAgentService logAgentService;
    @Resource
    private InnerLogTailService milogLogtailService;

    @Resource
    private MatrixLogServiceImpl matrixLogServiceImpl;

    @Resource
    private CloudMLLogServiceImpl cloudMLLogService;

    @Resource
    private HeraK8sAgentConfigProcessor heraK8sAgentConfigProcessor;

    private static final Cache<String, List<LogAgentListBo>> CACHE_LOCAL_PODS = CacheBuilder.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build();
    private static final Cache<String, LogCollectMeta> CACHE_LOCAL_METAS = CacheBuilder.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build();

    @Override
    public LogCollectMeta queryLogCollectMeta(String ip) {
        LogCollectMeta meta = CACHE_LOCAL_METAS.getIfPresent(ip);
        try {
            if (null == meta || CollectionUtils.isEmpty(meta.getAppLogMetaList())) {
                List<LogAgentListBo> pods = CACHE_LOCAL_PODS.getIfPresent(ip);
                if (CollectionUtils.isEmpty(pods)) {
                    pods = cloudMLLogService.queryPodListByIp(ip);
                    if (CollectionUtils.isNotEmpty(pods)) {
                        CACHE_LOCAL_PODS.put(ip, pods);
                    }
                }
                //过滤活着的pod
                meta = queryLogAgentConfig(ip, pods);
                CACHE_LOCAL_METAS.put(ip, meta);
            }
        } catch (Exception e) {
            log.error("queryLogCollectMeta failed for ip:{}, e", ip, e);
        }
        return meta;
    }

    /**
     * queryLogAgentConfig
     * 获取这个 agentIp(daemon set) 下的全量采集信息
     * 1. 根据每一个pod ip查出一个taillist
     * 2. 根据每个taillist 拼出一个logcollectmeta
     */
    public LogCollectMeta queryLogAgentConfig(String agentIp, List<LogAgentListBo> podIps) {
        LogCollectMeta logCollectMeta = logAgentService.initializeLogCollectMeta(agentIp);
        // tail 2 pods 关系，pods 都在该宿主机上的
        Map<MilogLogTailDo, List<LogAgentListBo>> tailDoListMap = heraK8sAgentConfigProcessor.mapLogTailsToAgents(podIps);
        List<MilogLogTailDo> logTailDos = tailDoListMap.keySet().stream().distinct().collect(Collectors.toList());
        // appId 2 tails 关系
        Map<Long, List<MilogLogTailDo>> idTailListMap = heraK8sAgentConfigProcessor.warpAppAndTailRel(logTailDos);
        List<Long> appBaseInfoIds = Lists.newArrayList(idTailListMap.keySet());
        logCollectMeta.setAppLogMetaList(appBaseInfoIds.stream()
                // 对每一个 app_id 下的 tail list
                .map(appBaseInfoId -> {
                    List<LogPattern> logPatternList = Lists.newArrayList();
                    // 遍历 tail
                    for (MilogLogTailDo milogLogtailDo : idTailListMap.get(appBaseInfoId)) {
                        // 对每一个 tail 对应的多个 pod
                        String appNamespace = matrixLogServiceImpl.getTraceBaseInfoByAppId(milogLogtailDo.getAppId()).getAppNamespace();
                        logPatternList.addAll(logAgentService.assembleLogPatternForCloudPlatformK8s(
                                appNamespace,
                                milogLogtailDo,
                                tailDoListMap.get(milogLogtailDo)));
                    }
                    return logAgentService.assembleSingleConfig(appBaseInfoId, logPatternList);
                })
                .filter(appLogMeta -> CollectionUtils.isNotEmpty(appLogMeta.getLogPatternList()))
                .collect(Collectors.toList()));
        return logCollectMeta;
    }
}
