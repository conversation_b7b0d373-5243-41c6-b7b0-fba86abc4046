package com.xiaomi.mone.log.manager.model.dt;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class TalosCreateDTO {
    @JsonProperty("name")
    private String name = null;
    @JsonProperty("partitionNum")
    private Integer partitionNum = null;
    @JsonProperty("catalog")
    private String catalog = null;
    @JsonProperty("preserveTime")
    private Integer preserveTime = null;
    @JsonProperty("dbName")
    private String dbName = null;
    @JsonProperty("serializationLib")
    private String serializationLib = null;
    @JsonProperty("owner")
    private String owner = null;
    @JsonProperty("description")
    private String description = null;
    @JsonProperty("partitionType")
    private Integer partitionType = null;
    @JsonProperty("columnDtos")
    private List<ColumnDTO2> columnDtos = null;
    @JsonProperty("storageType")
    private String storageType = null;

    public TalosCreateDTO() {
    }

    public TalosCreateDTO name(String name) {
        this.name = name;
        return this;
    }

    @ApiModelProperty(
            example = "table_name_en",
            value = "表英文名,为小写英文字母数字或下划线"
    )
    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public TalosCreateDTO partitionNum(Integer partitionNum) {
        this.partitionNum = partitionNum;
        return this;
    }

    @ApiModelProperty("")
    public Integer getPartitionNum() {
        return this.partitionNum;
    }

    public void setPartitionNum(Integer partitionNum) {
        this.partitionNum = partitionNum;
    }

    public TalosCreateDTO catalog(String catalog) {
        this.catalog = catalog;
        return this;
    }

    @ApiModelProperty(
            example = "catalog",
            value = "catalog"
    )
    public String getCatalog() {
        return this.catalog;
    }

    public void setCatalog(String catalog) {
        this.catalog = catalog;
    }

    public TalosCreateDTO preserveTime(Integer preserveTime) {
        this.preserveTime = preserveTime;
        return this;
    }

    @ApiModelProperty(
            example = "90",
            value = "有效期，单位为天"
    )
    public Integer getPreserveTime() {
        return this.preserveTime;
    }

    public void setPreserveTime(Integer preserveTime) {
        this.preserveTime = preserveTime;
    }

    public TalosCreateDTO dbName(String dbName) {
        this.dbName = dbName;
        return this;
    }

    @ApiModelProperty(
            example = "dbName",
            value = "库名"
    )
    public String getDbName() {
        return this.dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public TalosCreateDTO serializationLib(String serializationLib) {
        this.serializationLib = serializationLib;
        return this;
    }

    @ApiModelProperty("")
    public String getSerializationLib() {
        return this.serializationLib;
    }

    public void setSerializationLib(String serializationLib) {
        this.serializationLib = serializationLib;
    }

    public TalosCreateDTO owner(String owner) {
        this.owner = owner;
        return this;
    }

    @ApiModelProperty(
            example = "zhangsan",
            value = "所属者"
    )
    public String getOwner() {
        return this.owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public TalosCreateDTO description(String description) {
        this.description = description;
        return this;
    }

    @ApiModelProperty(
            example = "这是一张表的描述信息",
            value = "表描述"
    )
    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public TalosCreateDTO partitionType(Integer partitionType) {
        this.partitionType = partitionType;
        return this;
    }

    @ApiModelProperty(
            example = "1",
            value = "分区类型"
    )
    public Integer getPartitionType() {
        return this.partitionType;
    }

    public void setPartitionType(Integer partitionType) {
        this.partitionType = partitionType;
    }

    public TalosCreateDTO columnDtos(List<ColumnDTO2> columnDtos) {
        this.columnDtos = columnDtos;
        return this;
    }

    public TalosCreateDTO addColumnDtosItem(ColumnDTO2 columnDtosItem) {
        if (this.columnDtos == null) {
            this.columnDtos = new ArrayList();
        }

        this.columnDtos.add(columnDtosItem);
        return this;
    }

    @ApiModelProperty("表字段信息")
    public List<ColumnDTO2> getColumnDtos() {
        return this.columnDtos;
    }

    public void setColumnDtos(List<ColumnDTO2> columnDtos) {
        this.columnDtos = columnDtos;
    }

    public TalosCreateDTO storageType(String storageType) {
        this.storageType = storageType;
        return this;
    }

    @ApiModelProperty("存储类型")
    public String getStorageType() {
        return this.storageType;
    }

    public void setStorageType(String storageType) {
        this.storageType = storageType;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } else if (o != null && this.getClass() == o.getClass()) {
            TalosCreateDTO talosCreateDTO = (TalosCreateDTO)o;
            return Objects.equals(this.name, talosCreateDTO.name) && Objects.equals(this.partitionNum, talosCreateDTO.partitionNum) && Objects.equals(this.catalog, talosCreateDTO.catalog) && Objects.equals(this.preserveTime, talosCreateDTO.preserveTime) && Objects.equals(this.dbName, talosCreateDTO.dbName) && Objects.equals(this.serializationLib, talosCreateDTO.serializationLib) && Objects.equals(this.owner, talosCreateDTO.owner) && Objects.equals(this.description, talosCreateDTO.description) && Objects.equals(this.partitionType, talosCreateDTO.partitionType) && Objects.equals(this.columnDtos, talosCreateDTO.columnDtos) && Objects.equals(this.storageType, talosCreateDTO.storageType);
        } else {
            return false;
        }
    }

    public int hashCode() {
        return Objects.hash(new Object[]{this.name, this.partitionNum, this.catalog, this.preserveTime, this.dbName, this.serializationLib, this.owner, this.description, this.partitionType, this.columnDtos, this.storageType});
    }

    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class TalosCreateDTO {\n");
        sb.append("    name: ").append(this.toIndentedString(this.name)).append("\n");
        sb.append("    partitionNum: ").append(this.toIndentedString(this.partitionNum)).append("\n");
        sb.append("    catalog: ").append(this.toIndentedString(this.catalog)).append("\n");
        sb.append("    preserveTime: ").append(this.toIndentedString(this.preserveTime)).append("\n");
        sb.append("    dbName: ").append(this.toIndentedString(this.dbName)).append("\n");
        sb.append("    serializationLib: ").append(this.toIndentedString(this.serializationLib)).append("\n");
        sb.append("    owner: ").append(this.toIndentedString(this.owner)).append("\n");
        sb.append("    description: ").append(this.toIndentedString(this.description)).append("\n");
        sb.append("    partitionType: ").append(this.toIndentedString(this.partitionType)).append("\n");
        sb.append("    columnDtos: ").append(this.toIndentedString(this.columnDtos)).append("\n");
        sb.append("    storageType: ").append(this.toIndentedString(this.storageType)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    private String toIndentedString(Object o) {
        return o == null ? "null" : o.toString().replace("\n", "\n    ");
    }
}
