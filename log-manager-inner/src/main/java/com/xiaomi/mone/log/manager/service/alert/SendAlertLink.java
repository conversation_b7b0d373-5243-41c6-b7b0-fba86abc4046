package com.xiaomi.mone.log.manager.service.alert;

import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.common.Config;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/9/2 14:54
 */
@Slf4j
public abstract class SendAlertLink {
    /**
     * event 所属事件源 id
     */
    protected Integer id = Integer.parseInt(Config.ins().get("alert.source_id", "119"));

    private SendAlertLink next;

    public SendAlertLink() {
    }

    public SendAlertLink(Integer id) {
        this.id = id;
    }

    public SendAlertLink next() {
        return next;
    }

    public SendAlertLink appNext(SendAlertLink next) {
        this.next = next;
        return this;
    }

    public abstract boolean doSend();
}
