package com.xiaomi.mone.log.manager.service.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import com.xiaomi.mone.app.enums.PlatFormTypeInnerEnum;
import com.xiaomi.mone.enums.AccountTypeEnum;
import com.xiaomi.mone.enums.InnerProjectTypeEnum;
import com.xiaomi.mone.log.manager.dao.*;
import com.xiaomi.mone.log.manager.dao.alert.AlertDao;
import com.xiaomi.mone.log.manager.model.LogStorageDO;
import com.xiaomi.mone.log.manager.model.alert.Alert;
import com.xiaomi.mone.log.manager.model.dto.BillingAccount;
import com.xiaomi.mone.log.manager.model.dto.ResourceBillDetailDTO;
import com.xiaomi.mone.log.manager.model.dto.TraceAppInfoDTO;
import com.xiaomi.mone.log.manager.model.po.InnerMilogLogStoreDO;
import com.xiaomi.mone.log.manager.model.po.ResourceBillAccountRelDO;
import com.xiaomi.mone.log.manager.service.BillingManagementService;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.common.StringUtils;
import com.xiaomi.youpin.docean.plugin.es.EsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.ozhera.app.api.response.AppBaseInfo;
import org.apache.ozhera.log.common.Config;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.dao.MilogSpaceDao;
import org.apache.ozhera.log.manager.domain.EsCluster;
import org.apache.ozhera.log.manager.model.pojo.*;
import org.apache.ozhera.log.manager.service.extension.common.CommonExtensionService;
import org.apache.ozhera.log.manager.service.extension.common.CommonExtensionServiceFactory;
import org.apache.ozhera.log.manager.service.impl.HeraAppServiceImpl;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.Response;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.xiaomi.mone.log.manager.user.InnerMoneUtil.gson;
import static com.xiaomi.mone.log.manager.user.MoneUserDetailService.GSON;
import static org.apache.ozhera.log.common.Constant.DEFAULT_JOB_OPERATOR;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class BillingManagementServiceImpl implements BillingManagementService {

    @Resource
    private EsCluster esCluster;
    private CommonExtensionService commonExtensionService;
    @Resource
    private HeraAppServiceImpl heraAppService;
    @Resource
    private MatrixLogServiceImpl matrixLogServiceImpl;
    @Resource
    private AlertDao alertDao;
    @Resource
    private MilogSpaceDao spaceDao;
    @Resource
    private MilogLogTailDao milogLogtailDao;
    @Resource
    private InnerMilogLogStoreDao milogLogstoreDao;
    @Resource
    private InnerLogTailDao innerLogTailDao;
    @Resource
    private ResourceBillAccountRelDao resourceBillAccountRelDao;
    @Resource
    private LogStorageDao logStorageDao;

    @Resource
    private InnerMilogAppMiddlewareRelServiceImpl milogAppMiddlewareRelService;

    private static final String UNKNOWN_MACHINE_ROOM = "unknown";
    private static final String UNKNOWN = "unknown";
    private static final Cache<Long, BillingAccount> APP_ACCOUNT_CACHE = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .build();


    public void init() {
        commonExtensionService = CommonExtensionServiceFactory.getCommonExtensionService();
    }

    @Override
    public boolean isLogStorageCollectDone(String day) {
        return logStorageDao.getLogStorageCountByDay(day) > 0;
    }

    @Override
    public boolean clearLogStorageByDay(String day) {
        return logStorageDao.deleteLogStorageByDay(day, null);
    }

    @Override
    public boolean clearLogStorageBeforeDay(String day) {
        return logStorageDao.deleteLogStorageBeforeDay(day);
    }

    @Override
    public boolean collectLogStorage(List<MilogEsIndexDO> esIndexs, String day) {
        if (CollectionUtils.isEmpty(esIndexs) || StringUtils.isEmpty(day)) {
            return true;
        }

        List<LogStorageDO> logStorageDOs = new ArrayList<>();
        List<MilogEsIndexDO> skipEsIndexDOs = new ArrayList<>();
        for (MilogEsIndexDO esIndex : esIndexs) {
            try {
                LogStorageDO storage = processIndex(esIndex, day);
                if (storage == null) {
                    skipEsIndexDOs.add(esIndex);
                    continue;
                }
                logStorageDOs.add(storage);
            } catch (Exception e) {
                log.error("collectLogStorage error, skip this one for day:{}, es_cluster_id:{}, es_index:{}, e", day, esIndex.getClusterId(), esIndex.getIndexName(), e);
            }
        }
        if (skipEsIndexDOs.size() > 0) {
            log.info("collectLogStorage skip query due to no binding stores, day:{}, es_indexs:{}", day, gson.toJson(skipEsIndexDOs));
        }
        long res = 0;
        try {
            if (CollectionUtils.isNotEmpty(logStorageDOs)) {
                logStorageDao.deleteLogStorageByDay(day,
                        esIndexs.stream().filter(Objects::nonNull).map(MilogEsIndexDO::getIndexName).collect(Collectors.toList()));
                res = logStorageDao.insertBatch(logStorageDOs);
            }
        } catch (Exception e) {
            log.error("collectLogStorage error insert db failed, day:{}, failed_size:{}, e", day, logStorageDOs.size(), e);
            return false;
        }
        log.info("End of collectLogStorage, should be counted:{}，total statistics:{}", esIndexs.size(), res);
        return true;
    }

    private LogStorageDO processIndex(MilogEsIndexDO esIndex, String day) {
        List<InnerMilogLogStoreDO> stores = milogLogstoreDao.getStoresByESInfo(esIndex.getIndexName(), esIndex.getClusterId());
        if (CollectionUtils.isEmpty(stores)) {
            return null;
        }
        //有时间优化一下这个查询次数
        long esStorageBytesTotal = 0L, esStorageBytes = 0L;
        double esLineAvgBytes = 0.0, esLineAvgBytesTotal = 0.0;
        esStorageBytesTotal = queryESStorageBytes(stores.get(0), false, day);
        //no need to query more if total is 0
        if (esStorageBytesTotal != 0) {
            esStorageBytes = queryESStorageBytes(stores.get(0), true, day);
            if (esStorageBytes != 0) {
                esLineAvgBytes = queryESStorageAvgBytes(stores.get(0), true, day);
            }
            esLineAvgBytesTotal = queryESStorageAvgBytes(stores.get(0), false, day);
        }

        LogStorageDO storage = LogStorageDO.builder()
                .day(day)
                .esIndex(esIndex.getIndexName())
                .storeIds(stores.stream().map(InnerMilogLogStoreDO::getId).collect(Collectors.toList()))
                .machineRoom(queryMachineRoomByItemInfo(esIndex.getIndexName(), ResourceBillAccountRelDO.ItemTypeEnum.ES_TYPE, stores.get(0).getId()))
                .storageBytes(esStorageBytes)
                .storageBytesTotal(esStorageBytesTotal)
                .lineAvgSize(esLineAvgBytes)
                .lineAvgSizeTotal(esLineAvgBytesTotal)
                .build();
        storage.setCtime(Instant.now().toEpochMilli());
        storage.setUtime(Instant.now().toEpochMilli());
        return storage;
    }

    @Override
    public long queryESStorageBytes(MilogLogStoreDO store, boolean byDay, String day) {
        if (StringUtils.isEmpty(day)) {
            LocalDate date = LocalDate.now().plusDays(-1);
            day = date.format(DateTimeFormatter.ofPattern("yyyy.MM.dd"));
        }
        String esIndexName = commonExtensionService.getSearchIndex(store.getId(), store.getEsIndex());
        EsService esService = esCluster.getEsService(store.getEsClusterId());

        if (esService == null || StringUtils.isEmpty(esIndexName)) {
            log.error("queryESStat failed due to empty es service/index: store_id:{}, es_index:{}", store.getId(), store.getEsIndex());
            return 0L;
        }
        String queryIndex = esIndexName;
        if (byDay) {
            queryIndex = esIndexName + "-" + day;
        }
        Request request = new Request("GET", "/" + queryIndex + "/_stats");


        try {
            Response resp = esService.getEsClient().getEsOriginalClient().getLowLevelClient().performRequest(request);
            JsonElement respJson = new JsonParser().parse(IOUtils.toString(resp.getEntity().getContent()));
            if (null == respJson.getAsJsonObject().get("_all").getAsJsonObject()) {
                log.error("error queryESStat for store_id:{}, es_index:{}, resp:{} ", store.getId(), store.getEsIndex(), gson.toJson(respJson));
                return 0L;
            }
            return respJson.getAsJsonObject().get("_all")
                    .getAsJsonObject().get("total")
                    .getAsJsonObject().get("store")
                    .getAsJsonObject().get("size_in_bytes")
                    .getAsLong();

        } catch (Exception e) {
            log.error("exception queryESStat for store_id:{}, es_index:{}, error:{}", store.getId(), store.getEsIndex(), e.getMessage());
            return 0L;
        }
    }

    @Override
    public double queryESStorageAvgBytes(MilogLogStoreDO store, boolean byDay, String day) {
        if (StringUtils.isEmpty(day)) {
            LocalDate date = LocalDate.now().plusDays(-1);
            day = date.format(DateTimeFormatter.ofPattern("yyyy.MM.dd"));
        }
        String esIndexName = commonExtensionService.getSearchIndex(store.getId(), store.getEsIndex());
        EsService esService = esCluster.getEsService(store.getEsClusterId());

        if (esService == null || StringUtils.isEmpty(esIndexName)) {
            log.error("queryESStat failed due to empty es service/index: store_id:{}, es_index:{}", store.getId(), store.getEsIndex());
            return 0.0;
        }
        String queryIndex = esIndexName;
        if (byDay) {
            queryIndex = esIndexName + "-" +
                    day;
        }
        Request request = new Request("GET", "/" + queryIndex + "/_stats");


        try {
            Response resp = esService.getEsClient().getEsOriginalClient().getLowLevelClient().performRequest(request);
            JsonElement respJson = new JsonParser().parse(IOUtils.toString(resp.getEntity().getContent()));
            if (null == respJson.getAsJsonObject().get("_all").getAsJsonObject()) {
                log.error("error queryESStat for store_id:{}, es_index:{}, resp:{}", store.getId(), store.getEsIndex(), gson.toJson(respJson));
                return 0.0;
            }
            long totalCount = respJson.getAsJsonObject().get("_all")
                    .getAsJsonObject().get("primaries")
                    .getAsJsonObject().get("docs")
                    .getAsJsonObject().get("count")
                    .getAsLong();
            long totalSize = respJson.getAsJsonObject().get("_all")
                    .getAsJsonObject().get("total")
                    .getAsJsonObject().get("store")
                    .getAsJsonObject().get("size_in_bytes")
                    .getAsLong();
            if (totalCount == 0) {
                return 0.0;
            }
            return totalSize / (totalCount * 1.0);
        } catch (Exception e) {
            log.error("exception queryESStat for store_id:{}, es_index:{}, error:{}", store.getId(), store.getEsIndex(), e.getMessage());
            return 0.0;
        }
    }


    @Override
    public BillingAccount queryMilogAppAccount(Long milogAppId) {
        if (milogAppId == null) {
            return null;
        }
        BillingAccount result = APP_ACCOUNT_CACHE.getIfPresent(milogAppId);
        if (result != null && Strings.isNotEmpty(result.getAccountName())) {
            return result;
        }
        result = new BillingAccount();
        AppBaseInfo appBaseInfo = heraAppService.queryById(milogAppId);
        if (null == appBaseInfo || com.xiaomi.youpin.docean.common.StringUtils.isEmpty(appBaseInfo.getPlatformName())) {
            log.info("query hera app base info not exist, milogAppId:{}", milogAppId);
            return null;
        }
        PlatFormTypeInnerEnum platform = PlatFormTypeInnerEnum.getEnum(appBaseInfo.getPlatformName());
        if (platform == null) {
            log.info("query hera app base info got non-exist platform info, skip this one; milogAppId:{}", milogAppId);
            return null;
        }
        switch (platform) {
            case CHINA:
            case YOUPIN:
            case MIS:
                //todo 中国区应用暂时不收费，不计算 account, 统一用 unknown
                result.setAccountTypeEnum(AccountTypeEnum.IAMTREE_TYPE.toString());
                //result.setAccountName(appBaseInfo.getTreeIds().toString());
                result.setAccountName(UNKNOWN);
                break;
            case MICE:
            case OCEAN:
            case DEPLOYMENT:
            case MATRIX:
            case NEO:
            case MIFE:
            case JOB:
            case CLOUDML:
            default:
                long appId;
                try {
                    appId = Long.parseLong(appBaseInfo.getBindId());
                } catch (Exception e) {
                    log.error("parse appId failed when synchronizing bills, skip this milogAppId, milogAppId:{}, appBaseInfo:{}, error:", milogAppId, GSON.toJson(appBaseInfo), e);
                    break;
                }
                TraceAppInfoDTO traceInfo = matrixLogServiceImpl.getTraceBaseInfoByAppId(appId);
                if (null == traceInfo) {
                    log.error("query trace app base info not exist, appId:{}", appId);
                    break;
                }
                //计费账号优先用iam tree id
                if (traceInfo.getIamTreeId() != null && traceInfo.getIamTreeId() > 0) {
                    result.setAccountTypeEnum(AccountTypeEnum.IAMTREE_TYPE.toString());
                    result.setAccountName(String.valueOf(traceInfo.getIamTreeId()));
                } else {
                    result.setAccountTypeEnum(AccountTypeEnum.getAccountTypeEnumByPlatform(platform).toString());
                    result.setAccountName(traceInfo.getAccountId());
                }

        }
        APP_ACCOUNT_CACHE.put(milogAppId, result);
        return result;
    }

    @Override
    public BillingAccount queryMilogTailAccount(Long tailId) {
        BillingAccount result = new BillingAccount();
        MilogLogTailDo milogLogtailDo = milogLogtailDao.queryById(tailId);
        if (milogLogtailDo == null) {
            return result;
        }
        return queryMilogAppAccount(milogLogtailDo.getMilogAppId());
    }

    @Override
    public BillingAccount queryMilogSpaceAccount(Long spaceId) {
        BillingAccount result = new BillingAccount();
        if (spaceId == null) {
            return null;
        }
        ResourceBillAccountRelDO record = resourceBillAccountRelDao.getResourceBillAccountRelByItemName(Long.toString(spaceId),
                ResourceBillAccountRelDO.ItemTypeEnum.SPACE_TYPE.toString(),
                UNKNOWN_MACHINE_ROOM);
        if (record == null) {
            List<MilogLogTailDo> tails = innerLogTailDao.queryTailsBySpace(spaceId);
            ResourceBillAccountRelDO billAccountRelDO = buildSingleBaseRel(Long.toString(spaceId), tails, ResourceBillAccountRelDO.ItemTypeEnum.SPACE_TYPE);
            if (billAccountRelDO == null) {
                return null;
            }
            resourceBillAccountRelDao.newResourceBillAccountRel(billAccountRelDO);
            result.setAccountTypeEnum(billAccountRelDO.getAccountType());
            result.setAccountName(billAccountRelDO.getAccountName());
        } else {
            result.setAccountTypeEnum(record.getAccountType());
            result.setAccountName(record.getAccountName());
        }
        return result;
    }

    private ResourceBillAccountRelDO buildSingleBaseRel(String itemName, List<MilogLogTailDo> relTails, ResourceBillAccountRelDO.ItemTypeEnum itemTypeEnum) {
        if (CollectionUtils.isEmpty(relTails) || StringUtils.isEmpty(itemName)) {
            return null;
        }
        ResourceBillDetailDTO extraInfo = buildBaseBillDetail(relTails);
        BillingAccount account = queryMilogAppAccount(extraInfo.getChargeMilogAppId());
        if (null == account) {
            account = new BillingAccount();
        }
        String machineRoom = queryMachineRoomByItemInfo(itemName, itemTypeEnum, relTails.get(0).getStoreId());
        ResourceBillAccountRelDO relDO = ResourceBillAccountRelDO.builder().
                machineRoom(machineRoom).
                itemType(itemTypeEnum.toString()).
                itemName(itemName).
                accountType(account.getAccountTypeEnum()).
                accountName(account.getAccountName()).
                extraInfo(extraInfo).
                skipOverride(false).build();
        relDO.setCreator(DEFAULT_JOB_OPERATOR);
        relDO.setCtime(Instant.now().toEpochMilli());
        relDO.setUtime(Instant.now().toEpochMilli());
        return relDO;
    }

    @Override
    public List<ResourceBillAccountRelDO> buildBaseRel(Map<String, List<MilogLogTailDo>> keyTailMap, ResourceBillAccountRelDO.ItemTypeEnum itemTypeEnum) {
        if (null == keyTailMap) {
            return null;
        }
        //group by topic name
        List<ResourceBillAccountRelDO> relDOs = new CopyOnWriteArrayList<>();
        keyTailMap.forEach(
                (itemName, relTails) -> {
                    try {
                        ResourceBillAccountRelDO relDO = buildSingleBaseRel(itemName, relTails, itemTypeEnum);
                        if (relDO == null) {
                            return;
                        } else if (StringUtils.isEmpty(relDO.getAccountName())) {
                            log.warn("query app account failed when building base ResourceBillAccountRelDO, skip this item, item_name:{}, item_type:{}, charge_milog_app_id:{}, tail_ids:{}",
                                    itemName, itemTypeEnum.toString(), relDO.getExtraInfo().getChargeMilogAppId(),
                                    GSON.toJson(relTails.stream().map(MilogLogTailDo::getId).collect(Collectors.toList())));
                            return;
                        }
                        relDOs.add(relDO);
                    } catch (Exception e) {
                        log.error("buildSingleBaseRel failed during loop, skip this item, item_name:{}, item_type:{}, tail_ids:{}, error: ",
                                itemName, itemTypeEnum.toString(), GSON.toJson(relTails.stream().map(MilogLogTailDo::getId).collect(Collectors.toList())), e);
                    }

                }
        );
        return relDOs;
    }

    /**
     * @param tails 具有相同特性的 tail 列表。
     *              flink 作业：关联的 tail
     *              es 索引：使用同一个 es 索引的 store 下的所有 tail
     *              talos 表：关联的 tail
     * @return 账单关系的最小划分单位
     */
    public ResourceBillDetailDTO buildBaseBillDetail(List<MilogLogTailDo> tails) {
        HashSet<ResourceBillDetailDTO.TailDetail> tailDetails = new HashSet<>();
        if (CollectionUtils.isEmpty(tails)) {
            return ResourceBillDetailDTO.builder().build();
        }
        tails.sort(Comparator.comparingLong(MilogLogTailDo::getMilogAppId));
        Long chargeMilogAppId = tails.get(tails.size() - 1).getMilogAppId();
        String chargeAppName = tails.get(tails.size() - 1).getAppName();
        HashSet<Long> queriedMilogApps = new HashSet<>();
        String domain = Config.ins().get("hera.url", "http://hera.be.mi.com");
        for (int i = 0; i < tails.size(); i++) {
            MilogLogTailDo tail = tails.get(i);
            ResourceBillDetailDTO.TailDetail tailDetail = ResourceBillDetailDTO.TailDetail.builder().
                    storeId(tail.getStoreId()).
                    tailId(tail.getId()).
                    tailName(tail.getTail()).
                    milogAppId(tail.getMilogAppId()).
                    appId(tail.getAppId()).
                    appName(tail.getAppName()).
                    appType(InnerProjectTypeEnum.fromCode(tail.getAppType())).
                    storeDetailUrl(String.format("%s/project-milog/user/space-tree?spaceId=%d&inputV=&storeId=%d&type=storeDetail",
                            domain, tail.getSpaceId(), tail.getStoreId())).
                    tailDetailUrl(String.format("%s/project-milog/user/space-tree?spaceId=%d&inputV=&storeId=%d&type=tailDetail&tailId=%d&tailName=%s",
                            domain, tail.getSpaceId(), tail.getStoreId(), tail.getId(), tail.getTail())).
                    build();
            //计费账号默认取 最小且有计费账号的 milogAppId
            if (tail.getMilogAppId() < chargeMilogAppId &&
                    !queriedMilogApps.contains(tail.getMilogAppId())) {
                if (queryMilogAppAccount(tail.getMilogAppId()) != null) {
                    chargeMilogAppId = tail.getMilogAppId();
                    chargeAppName = tail.getAppName();
                }
                queriedMilogApps.add(tail.getMilogAppId());
            }
            tailDetails.add(tailDetail);
        }
        return ResourceBillDetailDTO.builder().
                chargeMilogAppId(chargeMilogAppId).
                chargeAppName(chargeAppName).
                tailDetails(tailDetails).
                build();
    }

    private String queryMachineRoomByItemInfo(String itemName, ResourceBillAccountRelDO.ItemTypeEnum itemTypeEnum, Long storeId) {
        String machineRoom = "";
        try {
            switch (itemTypeEnum) {
                case ES_TYPE:
                    InnerMilogLogStoreDO logStoreDO = milogLogstoreDao.queryById(storeId);
                    if (null == logStoreDO || null == logStoreDO.getEsClusterId()) {
                        return UNKNOWN_MACHINE_ROOM;
                    }
                    MilogEsClusterDO cluster = esCluster.getById(logStoreDO.getEsClusterId());
                    machineRoom = cluster.getDtCatalog();
                    break;
                case TALOS_TYPE:
                    logStoreDO = milogLogstoreDao.queryById(storeId);
                    if (null == logStoreDO) {
                        return UNKNOWN_MACHINE_ROOM;
                    }
                    MilogMiddlewareConfig middlewareConfig = milogAppMiddlewareRelService.queryMiddlewareConfig(logStoreDO.getMqResourceId());
                    machineRoom = middlewareConfig.getDtCatalog();
                    break;
                case FLINK_TYPE:
                    List<Alert> alerts = alertDao.queryByJobName(itemName);
                    if (null == alerts || alerts.size() == 0) {
                        break;
                    }
                    machineRoom = alerts.get(0).getFlinkCluster();
                    break;
                case SPACE_TYPE:
                    machineRoom = UNKNOWN_MACHINE_ROOM;
                    break;
                default:
            }
        } catch (Exception e) {
            log.error("failed to query machineRoom for middleware bills, itemName:{}, itemType:{}, storeId:{}, e", itemName, itemTypeEnum, storeId, e);
        }
        if (null == machineRoom || machineRoom.length() == 0) {
            machineRoom = milogLogstoreDao.queryById(storeId).getMachineRoom();
        }
        return machineRoom;
    }
}
