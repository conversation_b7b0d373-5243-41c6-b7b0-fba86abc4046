package com.xiaomi.mone.log.manager.service.alert;

import com.xiaomi.youpin.docean.common.StringUtils;
import com.xiaomi.youpin.feishu.FeiShu;
import lombok.extern.slf4j.Slf4j;

import static com.xiaomi.mone.log.manager.service.alert.SendAlertLinkFactory.arrayNotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @description p1 飞书
 * @date 2022/9/2 15:40
 */
@Slf4j
public class FeiShuSendAlertLink extends SendAlertLink {

    private String content;
    private String[] receivers;
    private String[] feiShuGroups;
    private boolean sendCard;
    private FeiShu feiShu;


    public FeiShuSendAlertLink(FeiShu feiShu, String content, String[] receivers,
                               String[] feiShuGroups, boolean sendCard) {
        this.feiShu = feiShu;
        this.content = content;
        this.receivers = receivers;
        this.feiShuGroups = feiShuGroups;
        this.sendCard = sendCard;
    }

    @Override
    public boolean doSend() {
        if (StringUtils.isEmpty(content)) {
            return false;
        }
        try {
            if (arrayNotEmpty(receivers)) {
                for (String receiver : receivers) {
                    if (receiver.contains("@xiaomi.com") == false) {
                        receiver = receiver + "@xiaomi.com";
                    }
                    if (sendCard) {
                        feiShu.sendCardByEmail(receiver, content);
                    } else {
                        feiShu.sendMsgByEmail(receiver, content);
                    }
                }
            }
            if (arrayNotEmpty(feiShuGroups)) {
                //content += feishuGroupsAtTags(receivers);
                for (String feishuGroup : feiShuGroups) {
                    if (sendCard) {
                        feiShu.sendCardByChatId(feishuGroup, content);
                    } else {
                        feiShu.sendMsgByChatId(feishuGroup, content);
                    }
                }
            }
        } catch (Exception e) {
            log.error("send feishu alert message error,content:{}", content, e);
            return false;
        }
        SendAlertLink next = super.next();
        if (null == next) {
            return true;
        }
        return next.doSend();
    }
}
