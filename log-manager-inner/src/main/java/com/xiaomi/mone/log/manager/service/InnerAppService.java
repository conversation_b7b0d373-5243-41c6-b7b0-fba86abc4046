package com.xiaomi.mone.log.manager.service;

import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.reflect.TypeToken;
import com.xiaomi.mone.enums.InnerProjectSourceEnum;
import com.xiaomi.mone.enums.InnerProjectTypeEnum;
import com.xiaomi.mone.log.manager.model.dto.MisAppInfoDTO;
import com.xiaomi.mone.log.manager.model.dto.MisResponseDTO;
import com.xiaomi.mone.log.manager.service.impl.ChinaMilogRpcConsumerServiceImpl;
import com.xiaomi.mone.log.manager.service.impl.YouPinMilogRpcConsumerServiceImpl;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.app.api.response.AppBaseInfo;
import org.apache.ozhera.log.common.Result;
import org.apache.ozhera.log.manager.dao.MilogAppTopicRelDao;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.dao.MilogLogstoreDao;
import org.apache.ozhera.log.manager.model.dto.RadarAppInfoDTO;
import org.apache.ozhera.log.manager.model.dto.RadarResponseDTO;
import org.apache.ozhera.log.manager.model.pojo.MilogAppTopicRelDO;
import org.apache.ozhera.log.manager.model.pojo.MilogLogStoreDO;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;
import org.apache.ozhera.log.manager.service.impl.HeraAppServiceImpl;
import org.jetbrains.annotations.NotNull;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.INNER_LOG_AGENT_SERVICE;
import static com.xiaomi.mone.log.manager.common.ManagerConfig.EXECUTOR_COMMON;
import static org.apache.ozhera.log.common.Constant.GSON;
import static org.apache.ozhera.log.common.Constant.SYMBOL_COMMA;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/4/11 19:18
 */
@Slf4j
@Service
public class InnerAppService {

    @Resource
    private ChinaMilogRpcConsumerServiceImpl chinaMilogRpcConsumerService;
    @Resource
    private YouPinMilogRpcConsumerServiceImpl youPinMilogRpcConsumerService;

    @Resource
    private MilogAppTopicRelDao milogAppTopicRelDao;

    @Resource
    private MilogLogTailDao milogLogtailDao;

    @Resource
    private MilogLogstoreDao milogLogstoreDao;

    @Resource
    private OkHttpClient okHttpClient;

    @Resource
    private HeraAppServiceImpl heraAppService;

    @Resource(name = INNER_LOG_AGENT_SERVICE)
    private InnerLogAgentService milogAgentService;

    @Value("$mis.url")
    private String misUrl;

    @Value("$radar.url")
    private String radarUrl;

    @Value("$mis.token")
    private String misToken;

    @Value("$app.env")
    private String env;

    public void delMoneApp() {
        CompletableFuture.runAsync(() -> {
            List<Long> projectIds = chinaMilogRpcConsumerService.queryDeleteProjectId();
            if (CollectionUtils.isNotEmpty(projectIds)) {
                delete(projectIds, InnerProjectSourceEnum.CHINA_SOURCE.getSource());
            }
        }, EXECUTOR_COMMON);
        CompletableFuture.runAsync(() -> {
            List<Long> projectIds = youPinMilogRpcConsumerService.queryDeleteProjectId();
            if (CollectionUtils.isNotEmpty(projectIds)) {
                delete(projectIds, InnerProjectSourceEnum.YOUPIN_SOURCE.getSource());
            }
        }, EXECUTOR_COMMON);
    }

    private void delete(List<Long> appIds, String source) {
        appIds.forEach(appId -> {
            milogAppTopicRelDao.deleteByAppIds(appId, source);
        });
    }

    @NotNull
    private MilogAppTopicRelDO getMilogAppTopicRel(MisAppInfoDTO misAppInfoDTO) {
        MilogAppTopicRelDO milogAppTopicRel = new MilogAppTopicRelDO();
        milogAppTopicRel.setAppId(misAppInfoDTO.getService_id());
        milogAppTopicRel.setAppName(misAppInfoDTO.getService());
        milogAppTopicRel.setCtime(Instant.now().toEpochMilli());
        milogAppTopicRel.setUtime(Instant.now().toEpochMilli());
        milogAppTopicRel.setOperator(misAppInfoDTO.getUser());
        milogAppTopicRel.setSource(InnerProjectSourceEnum.MIS_SOURCE.getSource());
        milogAppTopicRel.setType(InnerProjectTypeEnum.MIS_TYPE.getCode());
        milogAppTopicRel.setTreeIds(misAppInfoDTO.getTree_id());
        milogAppTopicRel.setNodeIPs(misAppInfoDTO.getCluster_info());
        return milogAppTopicRel;
    }

    public Result<String> synchronousRadarApp(String serviceName) {
        boolean isEnd = false;
        int pageNum = 1;
        int pageSize = 100;
        if ("prod".equals(env)) {
            env = "pro";
        }
        while (!isEnd) {
            String url = radarUrl + "/radar/getlist" + String.format("?page=%s&pageSize=%s&appName=%s&appType=1&user=&env=%s", pageNum, pageSize, serviceName, env);
            try {
                Request request = new Request.Builder()
                        .url(url).build();
                Response response = okHttpClient.newCall(request).execute();
                if (response.isSuccessful()) {
                    String rstJson = response.body().string();
                    RadarResponseDTO<List<RadarAppInfoDTO>> rst = GSON.fromJson(rstJson, new TypeToken<RadarResponseDTO<List<RadarAppInfoDTO>>>() {
                    }.getType());
                    RadarResponseDTO.RadarData<List<RadarAppInfoDTO>> radarData = rst.getData();
                    List<RadarAppInfoDTO> radarAppInfoDTOS = radarData.getList();
                    if (CollectionUtils.isNotEmpty(radarAppInfoDTOS)) {
                        synchronousRadarApp(radarAppInfoDTOS);
                    } else {
                        isEnd = true;
                    }
                    log.info("start sync radar app,current pageNum:{},total pageNum:{},remain num:{} ", pageNum, radarData.getPage(), radarData.getPage() - pageNum);
                }
                ++pageNum;
            } catch (Exception e) {
                log.error(String.format("http query from radar system error,url:%s", url), e);
            }
        }
        return Result.success();
    }

    public Boolean synchronousRadarApp(List<RadarAppInfoDTO> data) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("初始化radar项目开始,共有{}个", data.size());
        AtomicInteger count = new AtomicInteger();
        data.stream().forEach(radarAppInfoDTO -> {
            int increment = count.getAndIncrement();
            log.debug("radar应用同步,总有{}个应用，开始执行第{}个，还剩下{}个", data.size(), increment, data.size() - increment);
            MilogAppTopicRelDO milogAppTopicRel = getMilogAppTopicRel(radarAppInfoDTO);
            MilogAppTopicRelDO appTopicRel = milogAppTopicRelDao.queryIsExists(milogAppTopicRel);
            if (null == appTopicRel) {
                milogAppTopicRelDao.insert(milogAppTopicRel);
            } else {
                appTopicRel.setTreeIds(milogAppTopicRel.getTreeIds());
                milogAppTopicRelDao.update(appTopicRel);
            }
        });
        stopwatch.stop();
        log.info("初始化radar项目结束，花费时间：{} s", stopwatch.elapsed().getSeconds());
        return null;
    }

    private MilogAppTopicRelDO getMilogAppTopicRel(RadarAppInfoDTO radarAppInfoDTO) {
        MilogAppTopicRelDO milogAppTopicRel = new MilogAppTopicRelDO();
        milogAppTopicRel.setAppId(radarAppInfoDTO.getId());
        milogAppTopicRel.setAppName(radarAppInfoDTO.getName());
        milogAppTopicRel.setCtime(Instant.now().toEpochMilli());
        milogAppTopicRel.setUtime(Instant.now().toEpochMilli());
        milogAppTopicRel.setOperator(radarAppInfoDTO.getMembers().stream().map(RadarAppInfoDTO.Member::getUserId).collect(Collectors.joining(SYMBOL_COMMA)));
        milogAppTopicRel.setSource(InnerProjectSourceEnum.RADAR_SOURCE.getSource());
        milogAppTopicRel.setType(InnerProjectTypeEnum.RADAR_TYPE.getCode());
        milogAppTopicRel.setTreeIds(Lists.newArrayList(radarAppInfoDTO.getId()));
        milogAppTopicRel.setNodeIPs(Maps.newLinkedHashMap());
        return milogAppTopicRel;
    }

    public Result<String> synchronousMisApp(String serviceName) {
        String url = misUrl + "/api/service/allservice";
        try {
            Request request = new Request.Builder()
                    .url(url)
                    .post(new FormBody.Builder().add("token", misToken)
                            .add("service_name", serviceName).build())
                    .build();
            Response response = okHttpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                String rstJson = response.body().string();
                if (StringUtils.isNotEmpty(serviceName)) {
                    MisResponseDTO<MisAppInfoDTO> rst = GSON.fromJson(rstJson, new TypeToken<MisResponseDTO<MisAppInfoDTO>>() {
                    }.getType());
                    MisAppInfoDTO data = rst.getData();
                    synchronousMisApp(Arrays.asList(data));
                } else {
                    MisResponseDTO<List<MisAppInfoDTO>> rst = GSON.fromJson(rstJson, new TypeToken<MisResponseDTO<List<MisAppInfoDTO>>>() {
                    }.getType());
                    List<MisAppInfoDTO> data = rst.getData();
                    synchronousMisApp(data);
                }
            }
            return null;
        } catch (Exception e) {
            log.error(String.format("http query from mis system error,url:%s,token:%s", url, misToken), e);
        }
        return Result.success();
    }

    public Boolean synchronousMisApp(List<MisAppInfoDTO> data) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("初始化mis项目开始,共有{}个", data.size());
        AtomicInteger count = new AtomicInteger();
        data.forEach(misAppInfoDTO -> {
            int increment = count.getAndIncrement();
            log.debug("mis应用同步,总有{}个应用，开始执行第{}个，还剩下{}个", data.size(), increment, data.size() - increment);
            //查询mis应用
            List<AppBaseInfo> appBaseInfos = heraAppService.queryAppInfoWithLog(misAppInfoDTO.getService(), InnerProjectTypeEnum.MIS_TYPE.getCode());
            if (CollectionUtils.isEmpty(appBaseInfos)) {
                return;
            }
            for (AppBaseInfo appBaseInfo : appBaseInfos) {
                // 查询是否接入了日志
                List<MilogLogTailDo> milogLogtailDos = milogLogtailDao.queryAppTypeTailByAppId(misAppInfoDTO.getService_id(), InnerProjectTypeEnum.MIS_TYPE.getCode());
                if (CollectionUtils.isNotEmpty(milogLogtailDos) && null != appBaseInfo.getNodeIPs() && !appBaseInfo.getNodeIPs().isEmpty()) {
                    milogLogtailDos.forEach(logTailDo -> {
                        MilogLogStoreDO logStoreDO = milogLogstoreDao.queryById(logTailDo.getStoreId());
                        List<String> physicalIPs = appBaseInfo.getNodeIPs().get(logStoreDO.getMachineRoom());

                        List<String> ips = logTailDo.getIps();
                        if (CollectionUtils.isNotEmpty(physicalIPs) && CollectionUtils.isNotEmpty(ips)) {
                            if (!CollectionUtils.isEqualCollection(ips, physicalIPs)) {
                                log.info("mis info physical ip not equal,old:{},new:{}", GSON.toJson(ips), GSON.toJson(physicalIPs));
                                // 修改tail
                                logTailDo.setIps(physicalIPs);
                                milogLogtailDao.updateIps(logTailDo);
                                // 发送新增事件
                                milogAgentService.publishIncrementConfig(logTailDo.getId(), logTailDo.getMilogAppId(), physicalIPs);
                            }
                        }
                    });
                }
            }
        });
        stopwatch.stop();
        log.info("初始化mis项目结束，花费时间：{} s", stopwatch.elapsed().getSeconds());
        return true;
    }
}
