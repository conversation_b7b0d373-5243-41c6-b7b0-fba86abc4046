package com.xiaomi.mone.log.manager.service;

import com.xiaomi.mione.tesla.k8s.bo.LogAgentListBo;
import com.xiaomi.mone.log.manager.model.dto.MetaAppInfoDTO;

import java.util.List;

/**
 * CloudPlatformK8SLogService 云平台容器场景应用日志服务
 * 包含对 matrix、cloudml、miks 三个容器平台的应用基础信息查找
 */
public interface CloudPlatformK8sAppService {
    /**
     * 获取 daemonset agent 所在机器上的全量 pod 信息
     * 用 hostIP 查 trace 接口（/tracing/v1/app/log/agent/pods:hostIP）获取到 podList
     *
     * @param hostIP hostIP， 也就是 agentIP
     */
    List<LogAgentListBo> queryPodListByIp(String hostIP);

    MetaAppInfoDTO getMetaAppByAppId(Long appId, boolean withInstance);

    MetaAppInfoDTO.MiKSAppData getMiKSAppByAppInfo(Long appId, String machineRoom);

    MetaAppInfoDTO.Env getMiKSAppEnvIPsByAppInfo(Long appId, String appName, String envName);

    List<MetaAppInfoDTO.ServerInstance> getAppInstancesByLogGroup(Long appId, String platformType, String logGroupKey,String logGroupValue);
}
