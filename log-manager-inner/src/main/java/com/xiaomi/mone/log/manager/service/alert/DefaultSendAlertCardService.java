package com.xiaomi.mone.log.manager.service.alert;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import com.xiaomi.mone.enums.AlertTypeEnum;
import com.xiaomi.mone.enums.MatchOperatorEnum;
import com.xiaomi.mone.log.manager.model.alert.Alert;
import com.xiaomi.mone.log.manager.model.alert.AlertCondition;
import com.xiaomi.mone.log.manager.model.alert.AlertGroup;
import com.xiaomi.mone.log.manager.model.alert.AlertRule;
import com.xiaomi.mone.log.manager.model.bo.alert.AlertCallBackParam;
import com.xiaomi.mone.log.manager.model.bo.alert.CallBackArgument;
import com.xiaomi.mone.log.manager.service.IdmMoneUserDetailService;
import com.xiaomi.mone.model.res.AlertMatchRuleRes;
import com.xiaomi.mone.model.res.MatchContentRule;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.DEFAULT_RULE_EXPRESS_SIZE;
import static org.apache.ozhera.log.common.Constant.GSON;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/5/20 15:17
 */
@Slf4j
@Service
public class DefaultSendAlertCardService extends SendAlertCardService {

    @Value(value = "$server.type")
    private String serverType;

    @Value(value = "$hera.url")
    private String heraUrl;

    @Resource
    private IdmMoneUserDetailService moneUserDetailService;

    private static final Cache<String, Boolean> USER_STATE_CACHE_LOCAL = CacheBuilder.newBuilder()
            .maximumSize(200)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build();


    @Override
    protected String getServerType() {
        return serverType;
    }

    @Override
    protected String getHeraUrl() {
        return heraUrl;
    }

    @Override
    protected void sendMessage(Alert alert, AlertCondition matchedCondition, RemoteSendAlertLink.Meta meta, String[] receivers, String[] feishuGroups, String[] notifyGroups, AlertCallBackParam callBackParam) {
        SendAlertLinkFactory.getSendAlertLink(alert.getId(), matchedCondition.getAlertLevel(),
                meta, receivers, feishuGroups, notifyGroups, alert.getCallbackUrl(), callBackParam).doSend();
    }

    @Override
    protected AlertCallBackParam getCallBackParam(CallBackArgument callBackArgument) {
        AlertCallBackParam callBackParam = new AlertCallBackParam();
        if (StringUtils.isNotEmpty(callBackArgument.getAlert().getCallbackUrl())) {
            callBackParam.setAlertLogUrl(callBackArgument.getMeta().getLogUrl());
            callBackParam.setAlertTraceUrl(callBackArgument.getMeta().getTraceUrl());
            callBackParam.setTraceId(callBackArgument.getTraceId());
            callBackParam.setContent(callBackArgument.getMessage());
            callBackParam.setConditionName(callBackArgument.getRuleName());
            callBackParam.setConditionScript(callBackArgument.getRuleRegex());
            callBackParam.setStrategyName(callBackArgument.getAlert().getName());
            callBackParam.setAppName(callBackArgument.getAlert().getAppName());
            callBackParam.setLogPath(callBackArgument.getLogPath());

            callBackParam.setLevel(callBackArgument.getLevel());
            callBackParam.setAlarmValue(callBackArgument.getAlarmValue());
            callBackParam.setAlarmDesc(callBackArgument.getAlarmDesc());

            callBackParam.setServiceInfo(callBackArgument.getServiceInfo());
        }
        return callBackParam;
    }

    @Override
    protected String[] getNotifyGroups(Alert alert) {
        return extractNames(alert.getAlertGroups());
    }

    @Override
    protected String[] getFeishuGroups(Alert alert) {
        return splitString(alert.getFeishuGroups());
    }

    @Override
    protected String[] getReceiver(Alert alert) {
        if (alert == null) {
            return new String[0];
        }
        alert.setContacts(alert.getContacts());
        String[] receivers = splitString(alert.getContacts());
        if (receivers == null || receivers.length == 0) {
            return new String[0];
        }

        List<String> activeReceivers = filterActiveReceivers(Arrays.asList(receivers));
        return activeReceivers.toArray(new String[0]);
    }

    @Override
    protected String getAlertRule(Alert alert, AlertRule alertRule, Long tailId) {
        // 如果是正则计数类型，直接返回正则表达式
        if (StringUtils.equals(AlertTypeEnum.REGEX_COUNT.getType(), alert.getType())) {
            return alertRule.getRegex();
        }

        // 解析 JSON 字符串为 AlertMatchRuleRes 列表
        List<AlertMatchRuleRes> matchContentRules = parseMatchContentRules(alertRule.getRegex());

        // 查找匹配的规则并生成结果
        String ruleExpress = matchContentRules.stream()
                .filter(rule -> rule.getTailId().equals(tailId))
                .findFirst()
                .map(this::buildAlertRuleString)
                .orElse("");
        if (ruleExpress.length() > DEFAULT_RULE_EXPRESS_SIZE) {
            return STR."\{ruleExpress.substring(0, DEFAULT_RULE_EXPRESS_SIZE)}...";
        }
        return ruleExpress;
    }

    @Override
    protected String getAlertRuleName(Alert alert, AlertRule alertRule) {
        if (StringUtils.equals(AlertTypeEnum.REGEX_COUNT.getType(), alert.getType())) {
            return alertRule.getName();
        }
        return alert.getName();
    }

    /**
     * 解析 JSON 字符串为 AlertMatchRuleRes 列表
     */
    private List<AlertMatchRuleRes> parseMatchContentRules(String regex) {
        try {
            return GSON.fromJson(regex, new TypeToken<List<AlertMatchRuleRes>>() {
            }.getType());
        } catch (Exception e) {
            log.error("Failed to parse match content rules from JSON: {}", regex, e);
            return Lists.newArrayList();
        }
    }

    /**
     * 构建规则字符串
     */
    private String buildAlertRuleString(AlertMatchRuleRes matchRuleRes) {
        StringBuilder result = new StringBuilder();
        List<MatchContentRule> matchContentRules = matchRuleRes.getMatchContentRule();

        for (int i = 0; i < matchContentRules.size(); i++) {
            MatchContentRule rule = matchContentRules.get(i);
            List<String> expressions = rule.getExpressions();

            // 拼接表达式
            result.append(expressions.get(0))
                    .append(" ")
                    .append(MatchOperatorEnum.getNameByCode(Integer.parseInt(expressions.get(1))))
                    .append(" ")
                    .append(expressions.get(2));

            // 如果不是最后一个规则，拼接操作符
            if (i != matchContentRules.size() - 1) {
                result.append(" ")
                        .append(getRelByCode(rule.getOperator()));
            }
        }
        return result.toString();
    }

    public String getRelByCode(Integer code) {
        return switch (code) {
            case 1 -> " and ";
            case 2 -> " or ";
            default -> "";
        };
    }

    private List<String> filterActiveReceivers(List<String> receivers) {
        return receivers.stream()
                .map(String::trim)
                .filter(this::isUserActive)
                .collect(Collectors.toList());
    }

    private boolean isUserActive(String userName) {
        try {
            return USER_STATE_CACHE_LOCAL.get(userName, () -> {
                String userId = moneUserDetailService.queryActiveUserUIdByUserName(userName);
                return StringUtils.isNotEmpty(userId);
            });
        } catch (ExecutionException e) {
            log.error("查询用户活跃状态失败，用户名: {}", userName, e);
        }
        return false;
    }

    @Override
    protected RemoteSendAlertLink.Meta getMeta(int count, Alert alert, AlertRule alertRule, long ruleId, String traceId, String heraUrl, AlertCondition matchedCondition, String env, String ruleRegex, String ruleName, String timeStamp, String logParam, String message) {
        RemoteSendAlertLink.Meta meta = AlertMessageBuilder.buildEventMeta(count, alert,
                alertRule.getId(), traceId, heraUrl, matchedCondition, getServerType(), ruleRegex.replaceAll("\\\\", "\\\\\\\\\\\\\\\\").replaceAll("\"", "\\\\\""), ruleName, timeStamp, logParam, message);
        return meta;
    }

    private String[] splitString(String S) {
        if (StringUtils.isEmpty(S)) {
            return null;
        }
        return S.split("[,]");
    }

    private String[] extractNames(List<AlertGroup> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new String[]{};
        }
        String[] names = new String[list.size()];
        for (int i = 0; i < list.size(); i++) {
            names[i] = list.get(i).getName();
        }
        return names;
    }
}
