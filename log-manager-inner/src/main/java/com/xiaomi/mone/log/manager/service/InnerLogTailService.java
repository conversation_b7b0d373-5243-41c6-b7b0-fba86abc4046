package com.xiaomi.mone.log.manager.service;

import cn.hutool.core.collection.ListUtil;
import com.google.common.collect.Lists;
import com.xiaomi.mione.tesla.k8s.bo.LogAgentListBo;
import com.xiaomi.mone.enums.InnerLogTypeEnum;
import com.xiaomi.mone.enums.InnerMachineRegionEnum;
import com.xiaomi.mone.enums.InnerProjectSourceEnum;
import com.xiaomi.mone.enums.InnerProjectTypeEnum;
import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.INNER_LOG_AGENT_SERVICE;
import static com.xiaomi.mone.log.manager.common.utils.CommonUtil.encodeParam;
import com.xiaomi.mone.log.manager.common.utils.HeraTraceUtils;
import com.xiaomi.mone.log.manager.dao.InnerLogTailDao;
import com.xiaomi.mone.log.manager.model.IDMDept;
import com.xiaomi.mone.log.manager.model.Pair;
import com.xiaomi.mone.log.manager.model.cache.IDMDeptCache;
import com.xiaomi.mone.log.manager.model.convert.SpacePermTreeConvert;
import com.xiaomi.mone.log.manager.model.dto.DeploySystemAppsDTO.DeploySystemAppData;
import com.xiaomi.mone.log.manager.model.dto.DeploySystemAppsDTO.DeploySystemCluster;
import com.xiaomi.mone.log.manager.model.dto.MatrixAppsDTO;
import com.xiaomi.mone.log.manager.model.dto.MetaAppInfoDTO;
import com.xiaomi.mone.log.manager.model.dto.TraceAppInfoDTO;
import com.xiaomi.mone.log.manager.model.vo.K8sToAgentVo;
import static com.xiaomi.mone.log.manager.service.InnerTailExtensionService.LOG_AGENT_YP_SUFFIX;
import static org.apache.ozhera.log.common.Constant.DEFAULT_JOB_OPERATOR;
import static org.apache.ozhera.log.common.Constant.GSON;

import com.xiaomi.mone.log.manager.service.impl.ChinaMilogRpcConsumerServiceImpl;
import com.xiaomi.mone.log.manager.service.impl.CloudPlatformK8sAppServiceImpl;
import com.xiaomi.mone.log.manager.service.impl.DeploySystemLogServiceImpl;
import com.xiaomi.mone.log.manager.service.impl.K8sRpcComsumerServiceImpl;
import com.xiaomi.mone.log.manager.service.impl.MatrixLogServiceImpl;
import com.xiaomi.mone.log.manager.service.impl.MilineRpcConsumerServiceImpl;
import com.xiaomi.mone.log.manager.service.impl.NeoAppInfoServiceImpl;
import com.xiaomi.mone.log.manager.service.impl.YouPinMilogRpcConsumerServiceImpl;
import com.xiaomi.mone.log.manager.user.UseDetailInfo;
import com.xiaomi.mone.miline.api.bo.common.DeployTypeEnum;
import com.xiaomi.mone.miline.api.dto.PipelineDeployDto;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.common.StringUtils;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ozhera.app.api.response.AppBaseInfo;
import org.apache.ozhera.log.api.enums.AppTypeEnum;
import org.apache.ozhera.log.common.Config;
import org.apache.ozhera.log.common.Result;
import org.apache.ozhera.log.exception.CommonError;
import org.apache.ozhera.log.manager.dao.MilogAppMiddlewareRelDao;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.dao.MilogLogstoreDao;
import org.apache.ozhera.log.manager.dao.MilogSpaceDao;
import org.apache.ozhera.log.manager.model.dto.PodDTO;
import org.apache.ozhera.log.manager.model.dto.RegionDTO;
import org.apache.ozhera.log.manager.model.dto.SimpleAppEnvDTO;
import org.apache.ozhera.log.manager.model.dto.SpacePermTreeDTO;
import org.apache.ozhera.log.manager.model.pojo.MilogAppMiddlewareRel;
import org.apache.ozhera.log.manager.model.pojo.MilogLogStoreDO;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;
import org.apache.ozhera.log.manager.model.pojo.MilogSpaceDO;
import org.apache.ozhera.log.manager.service.impl.HeraAppServiceImpl;
import org.apache.ozhera.log.manager.service.impl.LogTailServiceImpl;
import org.nutz.lang.Strings;

import java.io.UnsupportedEncodingException;
import java.time.Clock;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/4/10 21:26
 */
@Slf4j
@Service
public class InnerLogTailService {

    @Resource
    private MilogLogTailDao milogLogtailDao;

    @Resource
    private MilogSpaceDao milogSpaceDao;

    @Resource
    private K8sRpcComsumerServiceImpl k8sRpcComsumerService;

    @Resource(name = INNER_LOG_AGENT_SERVICE)
    private InnerLogAgentService milogAgentService;

    @Resource
    private IdmMoneUserDetailService userService;

    @Resource
    private IDMDept idmDept;

    @Resource
    private MatrixLogServiceImpl matrixLogServiceImpl;

    @Resource
    private DeploySystemLogServiceImpl deploySystemLogServiceImpl;

    @Resource
    private ChinaMilogRpcConsumerServiceImpl chinaMilogRpcConsumerService;

    @Resource
    private YouPinMilogRpcConsumerServiceImpl youPinMilogRpcConsumerService;

    @Resource
    private MilineRpcConsumerServiceImpl milineRpcConsumerService;

    @Resource
    private MilogLogstoreDao milogLogstoreDao;

    @Resource
    private HeraAppServiceImpl heraAppService;

    @Resource
    private NeoAppInfoServiceImpl neoAppInfoService;

    @Resource
    private InnerLogTailDao innerLogTailDao;

    @Resource
    private LogTailServiceImpl logTailService;

    @Resource
    private MilogAppMiddlewareRelDao middlewareRelDao;
    @Resource
    private CloudPlatformK8sAppServiceImpl cloudPlatformK8sAppService;

    private static final Integer PER_BATCH_SIZE = 50;

    /**
     * ip如果超过50，则分批调用
     * @param podIps
     * @return
     */
    @Value(value = "$hera.url")
    private String heraUrl;

    public Map<String, List<LogAgentListBo>> queryAgentIpByPodIps(List<String> podIps) {
        ArrayList<String> newPodIps = new ArrayList<>(podIps);
        Map<String, List<LogAgentListBo>> agentIpMap = new HashMap<>();

        try {
            List<List<String>> partitionedPodIps = ListUtil.split(podIps, PER_BATCH_SIZE);
            for (List<String> batchPodIps : partitionedPodIps) {
                List<LogAgentListBo> logAgentList = k8sRpcComsumerService.getLogAgent(batchPodIps);
                for (LogAgentListBo bo : logAgentList) {
                    agentIpMap.putIfAbsent(bo.getAgentIP(), Lists.newArrayList());
                    agentIpMap.get(bo.getAgentIP()).add(bo);
                }
            }
        } catch (Exception e) {
            log.error(String.format("query k8s node by pod ip error,podIps:%s", GSON.toJson(newPodIps)), e);
        }
        return agentIpMap;
    }

    public void k8sPodIpsSend(Long tailId, List<String> podIps, K8sToAgentVo k8sToAgentVo) {
        Map<String, List<LogAgentListBo>> agentIpMap = queryAgentIpByPodIps(podIps);
        if (!agentIpMap.isEmpty()) {
            for (Map.Entry<String, List<LogAgentListBo>> agentIpEntry : agentIpMap.entrySet()) {
                milogAgentService.configIssueAgentK8s(tailId, agentIpEntry.getKey(), agentIpEntry.getValue(), k8sToAgentVo);
            }
        }
    }

    /**
     * 刷新sapce的部门ID字段
     */
    public void refreshSpaceDeptId() {
        try {
            List<MilogSpaceDO> spacelist = milogSpaceDao.getAll();
            String limitDeptId;
            for (MilogSpaceDO milogSpace : spacelist) {
                limitDeptId = getUserThreeDeptId(milogSpace.getCreator());
                if (!StringUtils.isEmpty(limitDeptId) && !limitDeptId.contains(milogSpace.getPermDeptId())) {
                    if (milogSpace.getPermDeptId().split("\\,").length > 1) {
                        limitDeptId = milogSpace.getPermDeptId() + "," + limitDeptId;
                    }
                    milogSpace.setPermDeptId(limitDeptId);
                    milogSpaceDao.update(milogSpace);
                    log.info("[MilogSpaceService.refreshSpaceDeptId] milogspace[{}]deptId已修改", milogSpace);
                }
            }
        } catch (Exception e) {
            log.error("[MilogSpaceService.refreshSpaceDeptId] has exception {}", e.getMessage());
        }
    }

    // 获取查看space权限的部门ID
    private String getUserThreeDeptId(String userName) {
        UseDetailInfo user = userService.queryUserByUserName(userName);
        if (user == null) {
            return null;
        }
        List<UseDetailInfo.DeptDescriptor> deptList = user.getFullDeptDescriptorList();

        List<UseDetailInfo.DeptDescriptor> collect = deptList.stream().filter(dpet -> "3".equals(dpet.getLevel())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            collect = deptList.stream().filter(dpet -> "2".equals(dpet.getLevel())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(collect)) {
            collect = deptList.stream().filter(dpet -> "1".equals(dpet.getLevel())).collect(Collectors.toList());
        }
        return collect.isEmpty() ? null : collect.get(0).getDeptId();
    }

    public Result<SpacePermTreeDTO> getSpacecPermission(Long spaceId) {
        if (spaceId == null) {
            return Result.fail(CommonError.ParamsError);
        }
        MilogSpaceDO space = milogSpaceDao.getMilogSpaceById(spaceId);
        SpacePermTreeDTO dto = new SpacePermTreeDTO();
        dto.setCheckId(space.getPermDeptId());
        dto.setCreateDeptId(space.getCreateDeptId());
        IDMDeptCache deptCache = idmDept.getDeptCache();
        dto.setTreeData(SpacePermTreeConvert.INSTANCE.fromCache(deptCache));
        return Result.success(dto);
    }

    public Result<String> setSpacePermission(Long spaceId, String permDeptIds) {
        if (spaceId == null || StringUtils.isEmpty(permDeptIds)) {
            return Result.fail(CommonError.ParamsError);
        }
        MilogSpaceDO space = milogSpaceDao.getMilogSpaceById(spaceId);
        if (!permDeptIds.contains(space.getCreateDeptId())) {
            return Result.fail(CommonError.ParamsError.getCode(), "创建部门权限不可被取消");
        }
        space.setPermDeptId(permDeptIds);
        boolean update = milogSpaceDao.update(space);
        return update ? Result.success() : Result.fail(CommonError.UnknownError);
    }


    /**
     * 比较机器列表并发送消息
     * 1.找到配置的log-agent的机器列表
     * 2.查询到最新的
     * 比较最新的是否比库中的多
     * 如果多，修改库，发送消息
     * 否则只修改库
     */
    public void casOttMachines(String source) {
        BaseMilogRpcConsumerService consumerService = queryConsumerService(source);
        List<MilogLogStoreDO> storeDOS = milogLogstoreDao.queryByLogType(InnerLogTypeEnum.OPENTELEMETRY.getType());
        if (CollectionUtils.isNotEmpty(storeDOS)) {
            try {
                List<String> liveMachines = consumerService.getLiveMachines();
                List<Long> storeIds = storeDOS.stream().map(MilogLogStoreDO::getId).collect(Collectors.toList());
                List<MilogLogTailDo> logTailDos = innerLogTailDao.queryTailsByAppTypeAndStores(InnerProjectTypeEnum.MIONE_TYPE.getCode(), storeIds);
                //过滤掉k8s的--miline调用的是线上环境，本地和线上不通，会报连接异常
                logTailDos = logTailDos.stream().filter(milogLogTailDo -> {
                    PipelineDeployDto pipelineDeployDto = milineRpcConsumerService.qryDeployInfo(milogLogTailDo.getAppId(), milogLogTailDo.getEnvId());
                    if (null == pipelineDeployDto) {
                        return true;
                    }
                    return DeployTypeEnum.K8S.getId() != pipelineDeployDto.getDeployType();
                }).filter(milogLogTailDo -> {
                    if (Objects.equals(InnerProjectSourceEnum.YOUPIN_SOURCE.getSource(), source)) {
                        return !Objects.equals(AppTypeEnum.LOG_AGENT.getName(), milogLogTailDo.getAppName()) &&
                                milogLogTailDo.getAppName().startsWith(AppTypeEnum.LOG_AGENT.getName()) &&
                                milogLogTailDo.getAppName().endsWith(LOG_AGENT_YP_SUFFIX);
                    }
                    return Objects.equals(AppTypeEnum.LOG_AGENT.getName(), milogLogTailDo.getAppName());
                }).collect(Collectors.toList());
                log.info("source:{},casOttMachines:{}", source, GSON.toJson(logTailDos));
                for (MilogLogTailDo logTailDo : logTailDos) {
                    List<String> ips = logTailDo.getIps();
                    if (!CollectionUtils.isEqualCollection(liveMachines, ips)) {
                        logTailDo.setIps(liveMachines);
                        logTailDo.setUtime(Instant.now().toEpochMilli());
                        logTailDo.setUpdater(DEFAULT_JOB_OPERATOR);
                        milogLogtailDao.updateIps(logTailDo);
                        logTailService.compareIpToHandle(logTailDo.getId(), logTailDo.getLogPath(), ips, liveMachines);
                    }
                }
            } catch (Exception e) {
                log.error("casOttMachines error,source:{}", source, e);
            }
        }
    }

    public static void main(String[] args) {
        List<String> test = Arrays.asList("/home/<USER>/log/server.log");
        List<String> list = Lists.newArrayList("1", "2", "3");
        List<List<String>> partition = Lists.partition(list, 10);
        for (List<String> strings : partition) {
            System.out.println(strings);
        }
        System.out.println(test.contains("*"));
    }

    private BaseMilogRpcConsumerService queryConsumerService(String source) {
        if (Objects.equals(InnerProjectSourceEnum.YOUPIN_SOURCE.getSource(), source)) {
            return youPinMilogRpcConsumerService;
        }
        return chinaMilogRpcConsumerService;
    }

    public Result<List<SimpleAppEnvDTO>> getRegionZonesByAppId(Long milogAppId, String machineRoom) {
        if (null == milogAppId) {
            return Result.failParam("appId不能为空");
        }
        if (org.apache.commons.lang3.StringUtils.isEmpty(machineRoom) || null == InnerMachineRegionEnum.queryCnByEn(machineRoom)) {
            return Result.failParam("machineRoom不能为空或者错误");
        }
        //印度的数据包含孟买和普纳，因为后边孟买要下线，所以普纳的可以直接用印度数据即可，兼容
        if (Objects.equals(InnerMachineRegionEnum.PN_MACHINE.getEn(), machineRoom)) {
            machineRoom = InnerMachineRegionEnum.IN_MACHINE.getEn();
        }
        List<SimpleAppEnvDTO> simpleAppEnvDTOs = Lists.newArrayList();
        AppBaseInfo appBaseInfo = heraAppService.queryById(milogAppId);
        SimpleAppEnvDTO simpleAppEnvDTO = new SimpleAppEnvDTO();
        simpleAppEnvDTO.setNameEn(machineRoom);
        simpleAppEnvDTO.setNameCn(InnerMachineRegionEnum.queryCnByEn(machineRoom));
        LinkedHashMap<String, List<String>> nodeIPs = appBaseInfo.getNodeIPs();
        if (null != nodeIPs && !nodeIPs.isEmpty()) {
            simpleAppEnvDTO.setNodeIps(nodeIPs.get(machineRoom));
        }
        if (CollectionUtils.isNotEmpty(appBaseInfo.getTreeIds())) {
            List<String> treeIds = appBaseInfo.getTreeIds().stream().map(String::valueOf).collect(Collectors.toList());
            List<RegionDTO> neoAppInfos = neoAppInfoService.getNeoAppInfo(treeIds);
            simpleAppEnvDTO.setPodDTOList(regionDTOTransferSimpleAppDTOs(neoAppInfos, InnerMachineRegionEnum.queryRegionByEn(machineRoom)));
        }
        simpleAppEnvDTOs.add(simpleAppEnvDTO);
        return Result.success(simpleAppEnvDTOs);
    }

    public List<PodDTO> regionDTOTransferSimpleAppDTOs(List<RegionDTO> neoAppInfos, InnerMachineRegionEnum
            machineRoom) {
        List<PodDTO> podDTOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(neoAppInfos)) {
            neoAppInfos.forEach(regionDTO ->
                    regionDTO.getZoneDTOList().forEach(zoneDTO -> {
                                String zoneNameEN = zoneDTO.getZoneNameEN();
                                InnerMachineRegionEnum machineInfoByZone = InnerMachineRegionEnum.queryMachineRegionByZone(zoneNameEN);
                                if (null != machineInfoByZone && machineInfoByZone == machineRoom) {
                                    podDTOList.addAll(zoneDTO.getPodDTOList());
                                }
                            }));
        }
        return podDTOList;
    }


    /**
     * 用 appId 查 trace 接口（/tracing/v1/app/:heraAppId）获取到 app_name, iamTreeId, matrixDeploySpaces
     *
     * @param appId
     * @return
     */
    public Result<MatrixAppsDTO.MatrixAppData> getMatrixDeploySpaceByAppId(Long appId, String machineRoom) {
        if (appId == 0) {
            return Result.failParam("appId 不能为空");
        }
        MatrixAppsDTO.MatrixAppData result = new MatrixAppsDTO.MatrixAppData();
        Set<MatrixAppsDTO.MatrixDeploySpace> deploySpaces = new HashSet<>();
        List<TraceAppInfoDTO> traceAppInfoDTOList = matrixLogServiceImpl.queryTraceAppInfos(new Long[]{appId});
        if (CollectionUtils.isEmpty(traceAppInfoDTOList)) {
            return Result.failParam("应用查询结果为空,请确认该服务是否已接入链路追踪");
        }
        traceAppInfoDTOList.forEach((current) -> {
            MatrixAppsDTO.MatrixDeploySpace space = new MatrixAppsDTO.MatrixDeploySpace();
            if (StringUtils.isNotEmpty(current.getMatrixDeploySpace())) {
                space.setName(current.getMatrixDeploySpace());
                if (StringUtils.isNotEmpty(machineRoom) &&
                        Strings.equals(Config.ins().get("app.env", "prod"), "prod") &&
                        !Objects.equals(HeraTraceUtils.getHeraTraceRegion(machineRoom), current.getTracingCluster())) {
                    return;
                }
                deploySpaces.add(space);
            }

        });
        result.setDeploySpaces(new ArrayList<>(deploySpaces));
        result.setName(traceAppInfoDTOList.get(0).getAppName());
        result.setTreeId(traceAppInfoDTOList.get(0).getIamTreeId());
        return Result.success(result);
    }

    /**
     * 用  appName, iamTreeId, deploySpace 查 Matrix 接口获取到 podIps
     *
     * @param appName
     * @param iamTreeId
     * @param deploySpace
     * @return
     */
    public Result<MatrixAppsDTO.MatrixDeploySpace> getMatrixPodIPsByAppInfo(Long iamTreeId, String appName, String deploySpace) {
        if (null == iamTreeId || iamTreeId == 0 || StringUtils.isEmpty(appName) || StringUtils.isEmpty(deploySpace)) {
            return Result.failParam("参数不能为空");
        }
        try {
            MatrixAppsDTO.MatrixDeploySpace result = matrixLogServiceImpl.queryMatrixDeploySpaceLevelPods(iamTreeId, appName, deploySpace);
            if (null == result) {
                return Result.failParam("empty result");
            }
            return Result.success(result);
        } catch (Exception e) {
            log.warn("[MilogLogtailService.getMatrixPodIPsByAppInfo] err,iamTreeId:{},appName:{},deploySpace:{}", iamTreeId, appName, deploySpace, e);
            return Result.failParam("fail to query matrix");
        }
    }

    /**
     * 用 appId 查 mitelemetry 元数据接口（/tracing/v1/app/:heraAppId）获取到 app_name, iamTreeId, matrixDeploySpaces
     *
     * @param appId
     * @return
     */
    public Result<DeploySystemAppData> getDeploySystemClusterByAppId(Long appId, String machineRoom) {
        if (appId == 0) {
            return Result.failParam("appId 不能为空");
        }
        String targetTracingCluster = HeraTraceUtils.getHeraTraceRegion(machineRoom);
        DeploySystemAppData result = deploySystemLogServiceImpl.getDeploySystemApp(appId,
                targetTracingCluster);
        if (result == null || CollectionUtils.isEmpty(result.getClusters())) {
            return Result.failParam("应用查询结果为空,请确认该服务是否已接入链路追踪");
        }

        return Result.success(result);
    }

    /**
     * 用 appId, appName, cluster 查 mitelemetry 元数据接口获取 ips
     *
     * @param appId
     * @param appName
     * @param cluster 部署系统集群名
     * @return
     */
    public Result<DeploySystemCluster> getDeploySystemClusterIPsByAppInfo(Long appId, String appName, String cluster) {
        if (null == appId || appId == 0 || StringUtils.isEmpty(appName) || StringUtils.isEmpty(cluster)) {
            return Result.failParam("参数不能为空");
        }
        try {
            DeploySystemCluster result = deploySystemLogServiceImpl.getDeploySystemIPs(appId,
                    appName, cluster);
            if (null == result) {
                return Result.failParam("empty result");
            }
            return Result.success(result);
        } catch (Exception e) {
            log.warn("[MilogLogtailService.getDeploySystemClusterIPsByAppInfo] err,appId:{},appName:{},cluster:{}", appId, appName, cluster, e);
            return Result.failParam("fail to query deploysystem");
        }
    }

    /**
     * 用 appId 查 mitelemetry 元数据接口（/tracing/v1/app/:heraAppId）获取到miks应用信息
     *
     * @param appId
     * @return
     */
    public Result<MetaAppInfoDTO.MiKSAppData> getMiKSAppByAppInfo(Long appId, String machineRoom) {
        if (appId == 0) {
            return Result.failParam("appId 不能为空");
        }
        MetaAppInfoDTO.MiKSAppData result = cloudPlatformK8sAppService.getMiKSAppByAppInfo(appId, machineRoom);
        if (result == null || CollectionUtils.isEmpty(result.getClusters())) {
            return Result.failParam("应用查询结果为空,请确认该服务是否在miks集群创建heraconfig");
        }

        return Result.success(result);
    }

    /**
     * 用 appId, appName, cluster 查 mitelemetry 元数据接口获取 ips
     *
     * @param appId
     * @param appName
     * @param envName miks集群名
     * @return
     */
    public Result<MetaAppInfoDTO.Env> getMiKSAppEnvIPsByAppInfo(Long appId, String appName, String envName) {
        if (null == appId || appId == 0 || StringUtils.isEmpty(appName) || StringUtils.isEmpty(envName)) {
            return Result.failParam("参数不能为空");
        }
        try {
            MetaAppInfoDTO.Env result = cloudPlatformK8sAppService.getMiKSAppEnvIPsByAppInfo(appId,
                    appName, envName);
            if (null == result) {
                return Result.failParam("empty result");
            }
            return Result.success(result);
        } catch (Exception e) {
            log.warn("[MilogLogtailService.getMiKSAppEnvIPsByAppInfo] err,appId:{},appName:{},env:{}", appId, appName, envName, e);
            return Result.failParam("fail to query miks env");
        }
    }

    public Result<Map<Long, List<Pair<Long, Long>>>> queryTailNoMq() {
        int pageSize = 500;
        int page = 1;
        Map<Long, List<Pair<Long, Long>>> data = new ConcurrentHashMap<>();

        ExecutorService executorService = Executors.newFixedThreadPool(20); // 设置适当的线程池大小
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        while (true) {
            List<MilogLogTailDo> logTailDos = innerLogTailDao.queryLogTailByPage(page, pageSize);
            if (CollectionUtils.isEmpty(logTailDos)) {
                break;
            }
            for (MilogLogTailDo logTailDo : logTailDos) {
                if (Objects.equals(InnerProjectTypeEnum.MATRIX_TYPE.getCode(), logTailDo.getAppType()) ||
                        Objects.equals(InnerProjectTypeEnum.RELEASE_TYPE.getCode(), logTailDo.getAppType()) ||
                        Objects.equals(InnerProjectTypeEnum.MIFE_TYPE.getCode(), logTailDo.getAppType()) ||
                        Objects.equals(InnerProjectTypeEnum.MICE_TYPE.getCode(), logTailDo.getAppType())) {
                    continue;
                }
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        List<MilogAppMiddlewareRel> middlewareRels = middlewareRelDao.queryByCondition(null, null, logTailDo.getId());
                        if (CollectionUtils.isEmpty(middlewareRels)) {
                            data.putIfAbsent(logTailDo.getSpaceId(), new ArrayList<>());
                            List<Pair<Long, Long>> pairs = data.get(logTailDo.getSpaceId());
                            pairs.add(Pair.of(logTailDo.getStoreId(), logTailDo.getId()));
                        }
                    } catch (Exception e) {
                        log.error("处理任务时出现异常", e);
                    }
                }, executorService);
                futures.add(future);
            }
            page++;
        }
        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

        try {
            allOf.whenComplete((result, throwable) -> executorService.shutdown()); // 显式关闭线程池
            allOf.get(10, TimeUnit.MINUTES);
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            throw new RuntimeException(e);
        }

        return Result.success(data);
    }

    public Result<List<String>> queryAccessLogList(String appName) {
        if (StringUtils.isEmpty(appName)) {
            return Result.failParam("appName not empty");
        }

        List<String> urlList = new ArrayList<>();

        List<AppBaseInfo> appBaseInfos = heraAppService.queryAppInfoWithLog(appName, InnerProjectTypeEnum.MIONE_TYPE.getCode());

        appBaseInfos.forEach(appBaseInfo -> {
            List<MilogLogTailDo> logTailDos = milogLogtailDao.getLogTailByMilogAppId(Long.valueOf(appBaseInfo.getId()));

            logTailDos.forEach(tailDo -> {
                try {
                    String commonParam = buildCommonParam(tailDo);
                    long curTimestamp = Clock.systemUTC().instant().toEpochMilli();
                    String timeParam = buildTimeParam(curTimestamp);

                    urlList.add(buildUrl(commonParam, timeParam));
                } catch (Exception e) {
                    log.info("queryAccessLogList build data error,tail:{}", GSON.toJson(tailDo), e);
                }
            });
        });

        return Result.success(urlList);
    }

    private String buildCommonParam(MilogLogTailDo tailDo) throws UnsupportedEncodingException {
        return String.format("spaceId=%s&storeId=%s&tailName=%s&",
                tailDo.getSpaceId(), tailDo.getStoreId(), encodeParam(tailDo.getTail()));
    }

    private String buildTimeParam(long curTimestamp) {
        long fiveMinutesInMillis = TimeUnit.MINUTES.toMillis(5);
        return String.format("&startTime=%s&endTime=%s", curTimestamp - fiveMinutesInMillis, curTimestamp + fiveMinutesInMillis);
    }

    private String buildUrl(String commonParam, String timeParam) {
        return new StringBuilder(heraUrl)
                .append("/project-milog/user/space-tree?")
                .append(commonParam)
                .append(timeParam)
                .toString();
    }
}
