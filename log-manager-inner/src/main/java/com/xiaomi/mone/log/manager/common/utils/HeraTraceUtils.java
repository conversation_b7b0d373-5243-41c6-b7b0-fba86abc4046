package com.xiaomi.mone.log.manager.common.utils;

import org.apache.ozhera.log.common.Config;
import org.nutz.lang.Strings;

import java.util.HashMap;
import java.util.Map;

public class HeraTraceUtils {
  // hera-oap-api staging
  public final static String MIOAP_DOMAIN_STAGING = "staging-mioap.api.xiaomi.net";
  // hera-oap-api production
  public final static String MIOAP_DOMAIN_ONLINE = "cnbj1-mioap.api.xiaomi.net";

  private final static Map<String, String> MAP_HERA_TRACE_REGION = new HashMap<>() {
    {
      put("cn", "c3");
      put("alsg", "ali-sgp");
      put("or", "aws-or");
      put("mos", "ksru-mos");
      put("fr", "aws-de");
      put("in", "aws-mb");
      put("ams", "az-ams");
      put("shgs1-nc4-303", "shgs1-nc4-303");
      put("az-pn", "az-pn");
    }
  };

  public static String getHeraTraceRegion(String logRegion) {
    boolean isProd = Strings.equals(Config.ins().get("app.env", "prod"), "prod");
    if (!isProd) {
      return "staging";
    }
    return MAP_HERA_TRACE_REGION.getOrDefault(logRegion, "staging");
  }

  public static String getHeraTraceDomainByTraceRegion(String tracingRegion) {
      return "staging".equalsIgnoreCase(tracingRegion) ? MIOAP_DOMAIN_STAGING : MIOAP_DOMAIN_ONLINE;
  }
}
