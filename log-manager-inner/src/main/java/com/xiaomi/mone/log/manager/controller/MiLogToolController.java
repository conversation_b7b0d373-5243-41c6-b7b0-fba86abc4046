package com.xiaomi.mone.log.manager.controller;

import cn.hutool.core.lang.Assert;
import com.xiaomi.mone.log.manager.model.MigrateDtResourceConfig;
import com.xiaomi.mone.log.manager.model.bo.ResourceIds;
import com.xiaomi.mone.log.manager.model.dto.BillingAccount;
import com.xiaomi.mone.log.manager.model.vo.ClearDtResourceParam;
import com.xiaomi.mone.log.manager.service.MiLogDataCleanToolService;
import com.xiaomi.mone.log.manager.service.impl.BillingManagementServiceImpl;
import com.xiaomi.mone.log.manager.service.impl.MiLogToolServiceImpl;
import com.xiaomi.youpin.docean.anno.Controller;
import com.xiaomi.youpin.docean.anno.RequestMapping;
import com.xiaomi.youpin.docean.anno.RequestParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ozhera.log.common.Result;
import org.apache.ozhera.log.manager.model.dto.MapDTO;
import org.apache.ozhera.log.manager.model.vo.LogQuery;

import javax.annotation.Resource;
import java.util.List;

import static org.apache.ozhera.log.common.Constant.SUCCESS_MESSAGE;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/1/5 10:54
 */
@Controller
public class MiLogToolController {

    @Resource
    private MiLogToolServiceImpl miLogToolService;
    @Resource
    private BillingManagementServiceImpl billingManagementService;
    @Resource
    private MiLogDataCleanToolService miLogDataCleanToolService;

    @RequestMapping(path = "/milog/tool/send/msg", method = "get")
    public Result<String> sendLokiMsg(@RequestParam(value = "tailId") Long tailId) {
        return miLogToolService.sendLokiMsg(tailId);
    }

    @RequestMapping(path = "/milog/tool/space/getbyuser", method = "get")
    public Result<List<MapDTO<String, Long>>> getSpacesByUser(@RequestParam(value = "user") String user,
                                                              @RequestParam(value = "isAdmin") Boolean isAdmin) {
        return miLogToolService.getSpacesByUser(user, isAdmin);
    }

    /**
     * 修复alert表中没有milogAppId的问题
     *
     * @return
     */
    @RequestMapping(path = "/milog/tool/fix/alert/milogAppId", method = "get")
    public Result<String> fixAlertAppId() {
        miLogToolService.fixAlertAppId();
        return Result.success("success fixAlertAppId ");
    }

    /**
     * 修复alert表中没有tailId的问题
     *
     * @return
     */
    @RequestMapping(path = "/milog/tool/fix/alert/tailId", method = "get")
    public Result<String> fixMilogAlertTailId() {
        miLogToolService.fixMilogAlertTailId();
        return Result.success("success fixMilogAlertTailId");
    }

    /**
     * 修复flink作业重启报警次数
     *
     * @return
     */
    @RequestMapping(path = "/milog/tool/fix/alert/notify", method = "get")
    public Result<String> fixFlinkAlertNotify(@RequestParam(value = "alertId") Long alertId) {
        miLogToolService.fixFlinkAlertNotify(alertId);
        return Result.success("success fixFlinkAlertNotify");
    }

    /**
     * 修复 es_index 表中没有记录的问题
     *
     * @return
     */
    @RequestMapping(path = "/milog/tool/fix/es/store", method = "get")
    public Result<String> fixEsIndexNotExistByStore(@RequestParam(value = "storeId") Long storeId) {
        miLogToolService.fixEsIndexNotExistByStore(storeId);
        return Result.success("success fixEsIndexNotExistByStore");
    }

    /**
     * 修复alert表中没有tailId的问题
     *
     * @return
     */
    @RequestMapping(path = "/milog/tool/fix/resource/label", method = "GET")
    public Result<String> fixResourceLabel() {
        return Result.success(miLogToolService.fixResourceLabel());
    }

    /**
     * 修复历史的store的mqResourceId
     */
    @RequestMapping(path = "/milog/tool/fix/store/mq_resource_id", method = "GET")
    public Result<String> fixLogStoreMqResourceId(@RequestParam(value = "storeId") Long storeId) {
        return Result.success(miLogToolService.fixLogStoreMqResourceId(storeId));
    }

    /**
     * 将工场资源迁移到另一个机房（es_cluster\talos信息表id不变）
     *
     * @param spaceId
     * @return
     */
    @RequestMapping(path = "/milog/tool/fix/migrate/resources", method = "POST")
    public Result<String> migrateCollectionResourceToAnotherDtSpace(
            @RequestParam(value = "config") MigrateDtResourceConfig config) {
        return Result.success(miLogToolService.migrateCollectionResourceToAnotherDtSpace(config));
    }

    /**
     * 将报警作业从一个机房迁移到拆分后的机房
     *
     * @return
     */
    @RequestMapping(path = "/milog/tool/fix/alert/rebuild", method = "GET")
    public Result<String> rebuildAlert(@RequestParam("alertId") Long alertId,
                                       @RequestParam("oldflinkCluster") String oldflinkCluster,
                                       @RequestParam("oldAlphaToken") String oldAlphaToken,
                                       @RequestParam("newAlphaToken") String newAlphaToken,
                                       @RequestParam("forceUpdate") Boolean forceUpdate) {
        return Result.success(miLogToolService.rebuildAlert(alertId, oldflinkCluster, oldAlphaToken, newAlphaToken, forceUpdate));
    }

    /**
     * 修复es的信息地址变化了重新写入nacos
     *
     * @param spaceId
     * @return
     */
    @RequestMapping(path = "/milog/tool/fix/es/nacos/address", method = "GET")
    public Result<String> fixNacosEsAddress(@RequestParam(value = "spaceId") Long spaceId, @RequestParam(value = "esClusterId") Long esClusterId,
                                            @RequestParam(value = "newEsAddr") String newEsAddr,
                                            @RequestParam(value = "catalog") String catalog) {
        if (null == esClusterId) {
            return Result.failParam("esClusterId is null");
        }
        return Result.success(miLogToolService.updateEsClusterAddress(spaceId, esClusterId, newEsAddr, catalog));
    }

    /**
     * 修复es的账号信息变化了重新写入nacos
     *
     * @param esInfoUpdate
     * @return
     */
    @RequestMapping(path = "/milog/tool/fix/es/nacos", method = "POST")
    public Result<String> fixNacosEsInfo(@RequestParam(value = "esClusterId") ResourceIds esInfoUpdate) {
        if (null == esInfoUpdate || CollectionUtils.isEmpty(esInfoUpdate.getEsIdList())) {
            return Result.failParam("esClusterId is null");
        }
        return Result.success(miLogToolService.updateEsClusterInfo(esInfoUpdate));
    }

    /**
     * 修复mq的账号信息后重新写入nacos
     *
     * @param resourceIds
     * @return
     */
    @RequestMapping(path = "/log/tool/fix/mq/nacos")
    public Result<String> fixNacosMqToken(@RequestParam(value = "mqIds") ResourceIds resourceIds) {
        if (null == resourceIds || CollectionUtils.isEmpty(resourceIds.getMqIdList())) {
            return Result.failParam("mqResourceIds is null");
        }
        return Result.success(miLogToolService.fixNacosMqToken(resourceIds.getMqIdList()));
    }


    /**
     * copy该store下的mis应用且部署方式为物理机的tail到新的store中
     *
     * @param targetStoreId
     * @param sourceStoreId
     * @return
     */
    @RequestMapping(path = "/milog/tool/multi/machine/tail/batch", method = "GET")
    public Result<String> batchCopyMultiMachineTail(@RequestParam("targetStoreId") Long targetStoreId,
                                                    @RequestParam("sourceStoreId") Long sourceStoreId) {
        return Result.success(miLogToolService.batchCopyMultiMachineTail(targetStoreId, sourceStoreId));
    }

    @RequestMapping(path = "/milog/tool/fix/tail/app", method = "GET")
    public Result<String> fixLogTailLogAppId(@RequestParam("appName") String appName) {
        miLogToolService.fixLogTailLogAppId(appName);
        return Result.success(SUCCESS_MESSAGE);
    }

    /**
     * 修复日志告警jar的路径为hdfs://zjyprc-hadoop/user/s_youpin-biz-arch/talos-flink-cycle.jar
     * 修复启动类名称
     *
     * @param alertId
     * @return
     */
    @RequestMapping(path = "/milog/tool/alert/jar/path", method = "GET")
    public Result<String> fixLogAlertJarPath(@RequestParam("alertId") Long alertId) {
        miLogToolService.fixLogAlertJarPath(alertId);
        return Result.success(SUCCESS_MESSAGE);
    }

    /**
     * 重启任务，重启先删除checkpoint后休眠2分钟
     *
     * @param alertId
     * @return
     */
    @RequestMapping(path = "/milog/tool/alert/restart", method = "GET")
    public Result<String> fixAlarmJobRestart(@RequestParam("alertId") Long alertId) {
        miLogToolService.fixAlarmJobRestart(alertId);
        return Result.success(SUCCESS_MESSAGE);
    }

    /**
     * 修复日志告警中的展示的日志路径
     *
     * @return
     */
    @RequestMapping(path = "/milog/tool/alert/log/path", method = "GET")
    public Result<String> fixAlarmLogPath(@RequestParam("alertId") Long alertId) {
        miLogToolService.fixAlarmLogPath(alertId);
        return Result.success(SUCCESS_MESSAGE);
    }

    /**
     * 根据参数获取关联的计费账号 BillingAccount，优先级 app>tail>space
     * 计费账号类型详见 AccountTypeEnum（包括 norns、org、iamTreeId、person）
     * 计费账号名称（nornsTag，org_id,tree_id,个人到最后一级的组织架构）
     *
     * @param milogAppId 应用中心的 app 表主键 id
     * @param tailId     logtail id
     * @param spaceId    logspace id
     * @return
     */
    @RequestMapping(path = "/milog/tool/bill/account", method = "GET")
    public Result<BillingAccount> queryMilogAppAccount(@RequestParam("milogAppId") Long milogAppId,
                                                       @RequestParam("tailId") Long tailId,
                                                       @RequestParam("spaceId") Long spaceId) {
        if (milogAppId != null && milogAppId > 0) {
            return Result.success(billingManagementService.queryMilogAppAccount(milogAppId));
        } else if (tailId != null && tailId > 0) {
            return Result.success(billingManagementService.queryMilogTailAccount(tailId));
        } else if (spaceId != null && spaceId > 0) {
            return Result.success(billingManagementService.queryMilogSpaceAccount(spaceId));
        }
        return Result.success(null);
    }

    /**
     * 修复日志告警表的alert的flink_cluster字段
     *
     * @param id
     * @return
     */
    @RequestMapping(path = "/milog/tool/update/flink/cluster/name", method = "GET")
    public Result<String> fixAlertFlinkCluster(@RequestParam("id") Long id) {
        miLogToolService.fixAlertFlinkCluster(id);
        return Result.success(SUCCESS_MESSAGE);
    }

    /**
     * 发送所有的store period信息到目前
     *
     * @param storeId
     * @return
     */
    @RequestMapping(path = "/milog/tool/store/period/mq", method = "GET")
    public Result<String> sendAllStorePeriodMq(@RequestParam("storeId") Long storeId) {
        miLogToolService.sendAllStorePeriodMq(storeId);
        return Result.success(SUCCESS_MESSAGE);
    }

    /**
     * 异步清理数据工场数据
     *
     * @param param
     * @return
     */
    @RequestMapping(path = "/milog/tool/fix/data/async", method = "POST")
    public Result<String> asyncClearData(@RequestParam(value = "param") ClearDtResourceParam param) {
        return miLogDataCleanToolService.asyncCleanInconsistentData(param);
    }

    /**
     * 获取es查询dsl
     *
     * @param query
     * @return
     */
    @RequestMapping(path = "/milog/tool/es/dsl")
    public Result<String> testQuery(LogQuery query) {
        return miLogToolService.queryEsSearchDSL(query);
    }

    @RequestMapping(path = "/milog/tool/config/push", method = "GET")
    public Result<String> pushConfigToNacos(@RequestParam(value = "machineRoom") String machineRoom) {
        Assert.notBlank(machineRoom, "machineRoom不能为空");
        return Result.success(miLogToolService.pushConfigToNacos(machineRoom));
    }
}
