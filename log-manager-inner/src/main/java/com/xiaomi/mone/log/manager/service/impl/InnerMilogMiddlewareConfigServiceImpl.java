package com.xiaomi.mone.log.manager.service.impl;

import cn.hutool.http.HttpStatus;
import com.google.common.collect.Lists;
import com.xiaomi.infra.galaxy.rpc.thrift.BaseService;
import com.xiaomi.mone.log.manager.service.InnerMilogMiddlewareConfigService;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.es.EsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.api.enums.LogStorageTypeEnum;
import org.apache.ozhera.log.api.enums.MiddlewareEnum;
import org.apache.ozhera.log.api.model.bo.MiLogResource;
import org.apache.ozhera.log.api.model.vo.EsIndexVo;
import org.apache.ozhera.log.common.Constant;
import org.apache.ozhera.log.common.Result;
import org.apache.ozhera.log.exception.CommonError;
import org.apache.ozhera.log.manager.model.pojo.MilogEsClusterDO;
import org.elasticsearch.ElasticsearchStatusException;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.rest.RestStatus;

import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: songyutong1
 * @date: 2024/08/22/10:57
 */
@Service
@Slf4j
public class InnerMilogMiddlewareConfigServiceImpl extends BaseService implements InnerMilogMiddlewareConfigService {

    @Override
    public Result<List> esIndexHealth(MiLogResource miLogResource) {
        String errInfos = checkEsParam(miLogResource);
        if (StringUtils.isNotBlank(errInfos)) {
            return Result.failParam(errInfos);
        }

        MilogEsClusterDO esClusterDO = MilogEsClusterDO.miLogEsResourceToConfig(miLogResource);
        EsService esService = createEsService(esClusterDO);
        try {
            if (esService.getClusterHealth() != HttpStatus.HTTP_OK) {
                return Result.fail(CommonError.UnknownError.getCode(), "es connect failed");
            }
        } catch (Exception e) {
            log.info("es connect failed: {}", e.getMessage(), e);
            return Result.fail(CommonError.UnknownError.getCode(), "es connect failed");
        }

        List<String> list = getErrorEsIndex(miLogResource.getMultipleEsIndex(), esService);
        if (CollectionUtils.isNotEmpty(list)) {
            return new Result<>(CommonError.Success.getCode(), "some index failed", list);
        }
        return Result.success(list);
    }

    private List<String> getErrorEsIndex(List<EsIndexVo> multipleEsIndex, EsService esService) {
        List<String> list = Lists.newArrayList();
        for (EsIndexVo esIndexVo : multipleEsIndex) {
            for (String indexName : esIndexVo.getEsIndexList()) {
                try {
                    SearchResponse searchResponse = esService.queryByIndex(indexName);
                    if (searchResponse.status() != RestStatus.OK) {
                        list.add(indexName);
                    }
                } catch (ElasticsearchStatusException | IOException exception) {
                    log.info("{} index connect failed: {}", indexName, exception.getMessage(), exception);
                    list.add(indexName);
                }
            }
        }
        return list;
    }

    private String checkEsParam(MiLogResource miLogResource) {
        List<String> errorInfos = Lists.newArrayList();
        if (MiddlewareEnum.ELASTICSEARCH.getCode().equals(miLogResource.getResourceCode())) {
            if (StringUtils.isBlank(miLogResource.getConWay())) {
                errorInfos.add("conWay cannot be empty");
            }

            if (StringUtils.isBlank(miLogResource.getServiceUrl())) {
                errorInfos.add("serviceUrl cannot be empty");
            } else {
                String serviceUrl = appendPortIfNecessary(miLogResource.getServiceUrl());
                miLogResource.setServiceUrl(serviceUrl);
            }

            if (!StringUtils.equalsIgnoreCase(LogStorageTypeEnum.ELASTICSEARCH.name(), miLogResource.getStorageType())) {
                errorInfos.add("storageType should be elasticsearch");
            }

            if (Objects.equals("pwd", miLogResource.getConWay()) && StringUtils.isBlank(miLogResource.getAk())) {
                errorInfos.add("If the connection mode is pwd, the username, password cannot be empty");
            }

            if (Objects.equals("token", miLogResource.getConWay()) && (StringUtils.isBlank(miLogResource.getEsToken()) || StringUtils.isBlank(miLogResource.getCatalog()) || StringUtils.isBlank(miLogResource.getDatabase()))) {
                errorInfos.add("If the connection mode is token, the token, catalog cluster, and database name cannot be empty");
            }

            if (CollectionUtils.isEmpty(miLogResource.getMultipleEsIndex())) {
                errorInfos.add("ES index information cannot be empty");
            }
        } else {
            errorInfos.add("resourceType should be esIndex");
        }
        return errorInfos.stream().collect(Collectors.joining(","));
    }

    private String appendPortIfNecessary(String url) {
        int index = url.lastIndexOf(":");
        if (index == -1) {
            url = String.format("%s:%s", url, "80");
        } else {
            String portStr = url.substring(index + 1);
            if (StringUtils.isBlank(portStr)) {
                url = String.format("%s%s", url, "80");
            }
        }
        return url;
    }

    private EsService createEsService(MilogEsClusterDO cluster) {
        switch (cluster.getConWay()) {
            case Constant.ES_CONWAY_PWD:
                return new EsService(cluster.getAddr(), cluster.getUser(), cluster.getPwd());
            case Constant.ES_CONWAY_TOKEN:
                return new EsService(cluster.getAddr(), cluster.getToken(), cluster.getDtCatalog(), cluster.getDtDatabase());
            default:
                log.warn("The ES cluster entered an exception: [{}]", cluster);
                throw new IllegalArgumentException("Invalid ES connection way");
        }
    }
}
