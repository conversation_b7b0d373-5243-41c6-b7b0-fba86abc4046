package com.xiaomi.mone.log.manager.service.cleanstrategy;

import com.xiaomi.mone.log.manager.dao.InnerLogMiddlewareConfigDao;
import com.xiaomi.mone.log.manager.model.vo.ClearDtResourceParam;
import com.xiaomi.youpin.docean.anno.Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.ozhera.log.manager.common.exception.MilogManageException;
import org.apache.ozhera.log.manager.dao.MilogAppMiddlewareRelDao;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.model.pojo.MilogAppMiddlewareRel;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;
import org.apache.ozhera.log.manager.model.pojo.MilogMiddlewareConfig;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;

/**
 * 清理数据库topic未被引用的情况
 *
 * @author: songyutong1
 * @date: 2024/09/13/16:44
 */
@Service
@Slf4j
public class DbTopicUnusedStrategy extends AbstractCleanStrategy{

    @Resource
    private InnerLogMiddlewareConfigDao innerLogMiddlewareConfigDao;

    @Resource
    private MilogAppMiddlewareRelDao milogAppMiddlewareRelDao;

    @Resource
    private MilogLogTailDao milogLogtailDao;

    @Override
    public void clean(ClearDtResourceParam param, String uuid) {
        log.info("uuid:{}, start fix topic not in used", uuid);
        MilogMiddlewareConfig milogMiddlewareConfig = innerLogMiddlewareConfigDao.queryPlatformTalosConfigByRegion(param.getMachineRoom());
        List<String> topicDeleteInDb = new ArrayList<>();
        if (ObjectUtils.isEmpty(milogMiddlewareConfig)) {
            log.info("uuid:{}, fix topic not in used failed, talosConfig not exist, please check the machineRoom:{}", uuid, param.getMachineRoom());
            throw new MilogManageException("fix topic not in used failed, talosConfig not exist, please check the machineRoom:" + param.getMachineRoom());
        }
        List<MilogAppMiddlewareRel> middlewareRelList = milogAppMiddlewareRelDao.queryByCondition(null, milogMiddlewareConfig.getId(), null);
        middlewareRelList.forEach(middlewareRel -> {
            MilogLogTailDo milogLogTailDo = milogLogtailDao.queryById(middlewareRel.getTailId());
            if (ObjectUtils.isEmpty(milogLogTailDo)) {
                topicDeleteInDb.add(middlewareRel.getConfig().getTopic());
                if (Boolean.FALSE.equals(param.getClearFlag())) {
                    return;
                }
                milogAppMiddlewareRelDao.delete(middlewareRel.getId());
            }
        });
        log.info("uuid:{}, fix topic not in used finished, topicDeleteInDb:{}", uuid, topicDeleteInDb);
    }
}
