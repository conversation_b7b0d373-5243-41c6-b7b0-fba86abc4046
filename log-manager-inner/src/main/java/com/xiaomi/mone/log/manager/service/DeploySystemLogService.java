package com.xiaomi.mone.log.manager.service;

import com.xiaomi.mone.log.manager.model.dto.DeploySystemAppsDTO.DeploySystemAppData;
import com.xiaomi.mone.log.manager.model.dto.DeploySystemAppsDTO.DeploySystemCluster;

/**
 * 部署系统应用日志服务
 */
public interface DeploySystemLogService {

  DeploySystemCluster getDeploySystemIPs(Long appId, String appName, String cluster);

  DeploySystemAppData getDeploySystemApp(Long appId, String targetTracingCluster);

  DeploySystemAppData getDeploySystemApp(Long appId);

}
