package com.xiaomi.mone.log.manager.common;

import com.google.common.collect.Lists;
import com.xiaomi.mone.enums.InnerMachineRegionEnum;
import com.xiaomi.mone.enums.InnerProjectTypeEnum;
import lombok.Getter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/4/10 10:20
 */
@Getter
public class InnerManagerConstant {

    public static final String TALOS_MARK = "talos";

    public static final String DEPT_LEVEL_PREFIX = "部门级别";

    public static final String DEPT_NAME_PREFIX = "部门名";

    public static final List<String> RESOURCE_DEFAULT_INITIALIZED_DEPT = Lists.newArrayList("技术发展与质量管理部");

    public static final String RESOURCE_NOT_INITIALIZED_MESSAGE = "当前用户部门资源没有初始化，请先去资源管理页初始化资源";

    public static final Integer USER_DEPT_MAX_DEFAULT_LEVEL = 1;

    public static final String ES_LABEL = "技术发展与质量管理部";

    public static final String INNER_LOG_AGENT_SERVICE = "innerLogAgentService";

    public static final String INNER_DICTIONARY_SERVICE = "innerDictionaryExtensionService";

    public static final String INNER_RESOURCE_SERVICE = "innerResourceExtensionService";

    public static final String INNER_STORE_SERVICE = "innerStoreExtensionService";

    public static final String INNER_TAIL_SERVICE = "innerTailExtensionService";

    public static final String INNER_COMMON_SERVICE = "innerCommonExtensionService";

    public static final String MIFE_LOG_PREFIX = "_代理日志";

    public static final String SYNC_LOG_PREFIX = "_应用日志";

    public static final String STORE_PERIOD_MQ_PREFIX = "HERALOG";

    public static final String LOG_TIME_KEY = "timestamp";

    public static final Integer DEFAULT_ALERT_WINDOW_SIZE = 60;

    public static final Integer DEFAULT_RULE_EXPRESS_SIZE = 300;

    public static final Map<String, String> MACHINE_LOKI_CLUSTERMQ_MAP = new HashMap<>() {
        {
            put("staging", "http://loki-staging.log.xiaomi.net");
            put(InnerMachineRegionEnum.CN_MACHINE.getEn(), "http://loki-c4.log.xiaomi.net");
            put(InnerMachineRegionEnum.IN_MACHINE.getEn(), "http://loki-mb.log.xiaomi.net");
            put(InnerMachineRegionEnum.MOS_MACHINE.getEn(), "http://loki-mos.log.xiaomi.net");
            put(InnerMachineRegionEnum.ALSG_MACHINE.getEn(), "http://loki-sgp.log.xiaomi.net");
            put(InnerMachineRegionEnum.AMS_MACHINE.getEn(), "http://loki-ams.log.xiaomi.net");
            put(InnerMachineRegionEnum.OR_MACHINE.getEn(), "http://loki-or.log.xiaomi.net");
            put(InnerMachineRegionEnum.FR_MACHINE.getEn(), "http://loki-fr.log.xiaomi.net");
            put(InnerMachineRegionEnum.SH_MACHINE.getEn(), "http://log.srv");
            put(InnerMachineRegionEnum.PN_MACHINE.getEn(), "http://loki-pn.log.xiaomi.net");
        }
    };

    public static final Map<String, String> SPACE_DATA_ID_MAP = new HashMap<>() {
        {
            put(InnerProjectTypeEnum.MIONE_TYPE.getType(), "log_manage_create_namespace_config");
            put(InnerProjectTypeEnum.MATRIX_TYPE.getType(), "log_manage_create_namespace_config_matrix");
            put(InnerProjectTypeEnum.MIFE_TYPE.getType(), "log_manage_create_namespace_config_mife");
        }
    };
}
