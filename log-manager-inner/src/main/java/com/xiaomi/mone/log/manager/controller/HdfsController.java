package com.xiaomi.mone.log.manager.controller;

import com.xiaomi.mone.log.manager.service.impl.HdfsServiceImpl;
import com.xiaomi.youpin.docean.anno.Controller;
import com.xiaomi.youpin.docean.anno.RequestMapping;
import com.xiaomi.youpin.docean.anno.RequestParam;
import org.apache.ozhera.log.common.Result;

import javax.annotation.Resource;

@Controller
public class HdfsController {


    @Resource
    private HdfsServiceImpl hdfsService;


    @RequestMapping(path = "/milog/hive/query", method = "get")
    public Result<String> hivequery(@RequestParam("date") String date,
                                    @RequestParam("traceId") String traceId){
        return hdfsService.queryHive(date,traceId);
    }



}
