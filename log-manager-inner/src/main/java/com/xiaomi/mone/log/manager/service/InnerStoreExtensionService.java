package com.xiaomi.mone.log.manager.service;

import com.xiaomi.mone.enums.InnerLogTypeEnum;
import com.xiaomi.mone.log.manager.common.InnerManagerConstant;
import com.xiaomi.mone.log.manager.dao.InnerLogMiddlewareConfigDao;
import com.xiaomi.mone.log.manager.dao.InnerLogTailDao;
import com.xiaomi.mone.log.manager.dao.InnerMilogLogStoreDao;
import com.xiaomi.mone.log.manager.model.po.InnerMilogLogStoreDO;
import com.xiaomi.mone.log.manager.service.listener.StoreOperateEvent;
import com.xiaomi.youpin.docean.Ioc;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.listener.event.EventType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.api.enums.LogStorageTypeEnum;
import org.apache.ozhera.log.api.enums.OperateEnum;
import org.apache.ozhera.log.api.model.vo.ResourceUserSimple;
import org.apache.ozhera.log.manager.dao.MilogLogstoreDao;
import org.apache.ozhera.log.manager.dao.MilogMiddlewareConfigDao;
import org.apache.ozhera.log.manager.domain.EsIndexTemplate;
import org.apache.ozhera.log.manager.mapper.MilogEsClusterMapper;
import org.apache.ozhera.log.manager.model.dto.EsInfoDTO;
import org.apache.ozhera.log.manager.model.pojo.MilogEsClusterDO;
import org.apache.ozhera.log.manager.model.pojo.MilogLogStoreDO;
import org.apache.ozhera.log.manager.model.pojo.MilogMiddlewareConfig;
import org.apache.ozhera.log.manager.model.vo.LogStoreParam;
import org.apache.ozhera.log.manager.service.extension.store.StoreExtensionService;
import org.apache.ozhera.log.manager.service.impl.MilogMiddlewareConfigServiceImpl;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.INNER_STORE_SERVICE;
import static com.xiaomi.mone.log.manager.common.ManagerConfig.EXECUTOR_COMMON;
import static com.xiaomi.mone.log.manager.service.MoneUserDetailService.GSON;
import static com.xiaomi.mone.log.manager.service.listener.StoreOperateListener.OPERATE_FLAG;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/4/10 16:29
 */
@Service(name = INNER_STORE_SERVICE)
@Slf4j
public class InnerStoreExtensionService implements StoreExtensionService {

    @Resource
    private MilogMiddlewareConfigServiceImpl resourceConfigService;
    @Resource
    private EsIndexTemplate esIndexTemplate;
    @Resource
    private InnerEsIndexTemplate innerEsIndexTemplate;

    @Resource
    private MilogMiddlewareConfigDao milogMiddlewareConfigDao;

    @Resource
    private InnerLogMiddlewareConfigDao innerLogMiddlewareConfigDao;

    @Resource
    private MilogLogstoreDao milogLogstoreDao;

    @Resource
    private InnerMilogLogStoreDao innerLogStoreDao;

    @Resource
    private InnerLogTailDao innerLogTailDao;

    @Resource
    private MilogEsClusterMapper milogEsClusterMapper;

    @Resource
    private LogDiscoverService logDiscoverService;

    @Override
    public boolean storeInfoCheck(LogStoreParam param) {
        return null != param.getLogType() &&
                (InnerLogTypeEnum.LOKI_APP_LOG.getType().equals(param.getLogType()) ||
                        InnerLogTypeEnum.MATRIX_ES_LOG.getType().equals(param.getLogType()) ||
                        param.isPlatformResourceStore());
    }

    @Override
    public void storeResourceBinding(MilogLogStoreDO ml, LogStoreParam command, OperateEnum operateEnum) {
        if (Objects.equals(InnerLogTypeEnum.LOKI_APP_LOG.getType(), command.getLogType()) ||
                Objects.equals(InnerLogTypeEnum.MATRIX_ES_LOG.getType(), command.getLogType()) ||
                command.isPlatformResourceStore()) {
            return;
        }
        if ((OperateEnum.ADD_OPERATE == operateEnum || null == command.getEsResourceId()) ||
                OperateEnum.UPDATE_OPERATE == operateEnum && null != command.getEsResourceId()) {
            chinaDeptStoreResourceBinding(ml, command);
        }
        // 要么，就去进行其他部门的绑定。如：是新增，但 es_resource_id 不为空的情况
        otherDeptStoreResourceBinding(ml, command);
        //自定义资源
        customResources(ml, command);
    }

    private void customResources(MilogLogStoreDO ml, LogStoreParam command) {
        if (null != command.getMqResourceId()) {
            ml.setMqResourceId(command.getMqResourceId());
        }
        if (null != command.getEsResourceId()) {
            ml.setEsClusterId(command.getEsResourceId());
        }
        if (StringUtils.isNotBlank(command.getEsIndex())) {
            ml.setEsIndex(command.getEsIndex());
        }
    }

    /**
     * 中国区es资源绑定
     */
    private void chinaDeptStoreResourceBinding(MilogLogStoreDO ml, LogStoreParam command) {
        ResourceUserSimple resourceUserConfig = resourceConfigService.userResourceList(command.getMachineRoom(), command.getLogType());
        if (resourceUserConfig.getInitializedFlag() && !resourceUserConfig.getShowFlag()) {
            // get esIndex
            if (StringUtils.isNotEmpty(command.getEsIndex()) && null != command.getEsResourceId()) {
                ml.setEsClusterId(command.getEsResourceId());
                ml.setEsIndex(command.getEsIndex());
            } else {
                EsInfoDTO esInfo = esIndexTemplate.getEsInfo(command.getMachineRoom(), command.getLogType());
                command.setEsIndex(esInfo.getIndex());
                ml.setEsClusterId(esInfo.getClusterId());
                ml.setEsIndex(esInfo.getIndex());
            }
            if (null != command.getMqResourceId()) {
                ml.setMqResourceId(command.getMqResourceId());
            } else {
                MilogMiddlewareConfig milogMiddlewareConfig = milogMiddlewareConfigDao.queryDefaultMqMiddlewareConfigMotorRoom(ml.getMachineRoom());
                ml.setMqResourceId(milogMiddlewareConfig.getId());
            }
        }
    }

    /**
     * 中国区以外的部门es资源绑定
     */
    private void otherDeptStoreResourceBinding(MilogLogStoreDO ml, LogStoreParam command) {
        ResourceUserSimple resourceUserConfig = resourceConfigService.userResourceList(command.getMachineRoom(), command.getLogType());
        if (resourceUserConfig.getInitializedFlag() && resourceUserConfig.getShowFlag()) {
            if (StringUtils.isNotEmpty(command.getEsIndex()) && null != command.getEsResourceId()) {
                ml.setEsClusterId(command.getEsResourceId());
                ml.setEsIndex(command.getEsIndex());
            } else {
                MilogEsClusterDO storageDo = milogEsClusterMapper.selectById(command.getEsResourceId());
                if (null != storageDo && (StringUtils.isEmpty(storageDo.getLogStorageType()) || StringUtils.equalsIgnoreCase(LogStorageTypeEnum.ELASTICSEARCH.name(), storageDo.getLogStorageType()))) {
                    EsInfoDTO esInfo = esIndexTemplate.getEsInfoOtherDept(command.getEsResourceId(), command.getLogType(), null);
                    command.setEsIndex(esInfo.getIndex());
                    ml.setEsClusterId(esInfo.getClusterId());
                    ml.setEsIndex(esInfo.getIndex());
                }
            }
        }
    }

    @Override
    public void postProcessing(MilogLogStoreDO storeDO, LogStoreParam storeParam, OperateEnum operateEnum) {
        // 只有平台创建的 logStore 需要进行资源绑定（资源信息是对用户隐藏的，无法修改）
        if (OperateEnum.ADD_OPERATE == operateEnum && storeParam.isPlatformResourceStore()) {
            try {
                platformStoreResourceBinding(storeDO, storeParam);
                updateLogStore(storeDO);
            } catch (Exception e) {
                log.error("isPlatformResourceStore create resource error,storeParam:{}", GSON.toJson(storeParam), e);
                //todo 增加retry替换直接删除
                if (storeDO.getId() > 0) {
                    int affectedRow = innerLogTailDao.deleteTailsByStoreId(storeDO.getId());
                    log.error("isPlatformResourceStore revert logtail, storeId:{}, total row:{}", storeDO.getId(), affectedRow);
                    milogLogstoreDao.deleteById(storeDO.getId());
                }
                throw e;
            }
        }
        //发送store操作事件
        this.sendStoreOperateEvent(storeDO, storeParam, operateEnum);
        logDiscoverService.removeCache(storeDO.getId());
    }

    public void sendStoreOperateEvent(MilogLogStoreDO storeDO, LogStoreParam storeParam, OperateEnum operateEnum) {
        StoreOperateEvent storeOperateEvent = new StoreOperateEvent(EventType.custom, operateEnum, storeDO, storeParam);
        storeOperateEvent.setAsync(true);
        storeOperateEvent.getAttachments().put(OPERATE_FLAG, OPERATE_FLAG);
        Ioc.ins().publishEvent(storeOperateEvent);
    }

    /**
     * 如果创建 logstore 选择了平台侧创建资源，则进行平台资源绑定操作
     */
    private void platformStoreResourceBinding(MilogLogStoreDO ml, LogStoreParam command) {
        EsInfoDTO esInfoDTO = innerEsIndexTemplate.createPlatformEsInfo(ml, command.getMachineRoom());
        if (null == esInfoDTO) {
            return;
        }

        // 查询使用平台侧资源的 logstore 对应集群的 talos 资源 id
        MilogMiddlewareConfig milogMiddlewareConfig = innerLogMiddlewareConfigDao.queryPlatformTalosConfigByRegion(ml.getMachineRoom());
        if (null == milogMiddlewareConfig) {
            return;
        }

        // 设置 ml 的：es_cluster_id、es_index、mq_resource_id
        ml.setEsClusterId(esInfoDTO.getClusterId());
        ml.setEsIndex(esInfoDTO.getIndex());
        ml.setMqResourceId(milogMiddlewareConfig.getId());
    }

    @Override
    public boolean sendConfigSwitch(LogStoreParam param) {
        return !Objects.equals(InnerLogTypeEnum.LOKI_APP_LOG.getType(), param.getLogType()) &&
                !Objects.equals(InnerLogTypeEnum.MATRIX_ES_LOG.getType(), param.getLogType());
    }

    @Override
    public void deleteStorePostProcessing(MilogLogStoreDO logStoreDO) {
        InnerMilogLogStoreDO innerLogStoreDO = innerLogStoreDao.queryById(logStoreDO.getId());
        if (null != innerLogStoreDO && innerLogStoreDO.isPlatformResourceStore()) {
            // 可能太慢，不能影响主流程，异步去删
            CompletableFuture.runAsync(() -> platformStoreResourceDelete(logStoreDO), EXECUTOR_COMMON);
        }
    }

    @Override
    public String getMangerEsLabel() {
        return InnerManagerConstant.ES_LABEL;
    }

    @Override
    public boolean updateLogStore(MilogLogStoreDO milogLogStoreDO) {
        InnerMilogLogStoreDO innerMilogLogStoreDO = new InnerMilogLogStoreDO();
        BeanUtils.copyProperties(milogLogStoreDO, innerMilogLogStoreDO);
        return innerLogStoreDao.updateLogStore(innerMilogLogStoreDO);
    }

    @Override
    public boolean isNeedSendMsgType(Integer logType) {
        return InnerLogTypeEnum.APP_LOG_MULTI.getType().equals(logType) ||
                InnerLogTypeEnum.APP_LOG_SIGNAL.getType().equals(logType) ||
                InnerLogTypeEnum.NGINX.getType().equals(logType) ||
                InnerLogTypeEnum.OPENTELEMETRY.getType().equals(logType) ||
                InnerLogTypeEnum.MATRIX_ES_LOG.getType().equals(logType) ||
                InnerLogTypeEnum.FREE.getType().equals(logType);
    }

    /**
     * 删除使用平台资源的 logstore 后删除对应 es 索引
     *
     * @param logStoreDO
     */
    private void platformStoreResourceDelete(MilogLogStoreDO logStoreDO) {
        innerEsIndexTemplate.deleteHeraPlatformEsInfo(logStoreDO);
    }
}
