package com.xiaomi.mone.log.manager.model.dto;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * DeploySystem 返回 app 信息
 */
@Data
public class DeploySystemAppsDTO {
    private int code;
    private String message;
    private String level;
    private List<DeploySystemAppData> data;

    public DeploySystemAppsDTO() {
        this.data = new ArrayList<>();
    }

    @Data
    public static class DeploySystemAppData {
        private Long id;
        private String name;
        private Long treeId;
        private List<DeploySystemCluster> clusters;

        public DeploySystemAppData() {
            this.clusters = new ArrayList<>();
        }
    }


    @Data
    public static class DeploySystemCluster {
        private Long id;
        private String name;
        private String tracingCluster;
        private String tracingService;
        private List<String> ips;
    }

    @Data
    public class ServiceInstance {
        private String name;
    }
}
