package com.xiaomi.mone.log.manager.service.alert;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.xiaomi.bigdata.workshop.api._Api;
import com.xiaomi.bigdata.workshop.model.*;
import com.xiaomi.mone.enums.InnerMachineRegionEnum;
import com.xiaomi.mone.enums.InnerMiddlewareEnum;
import com.xiaomi.mone.log.manager.common.ManagerConfig;
import com.xiaomi.mone.log.manager.common.utils.DtUtils;
import com.xiaomi.mone.log.manager.dao.InnerLogMiddlewareConfigDao;
import com.xiaomi.mone.log.manager.model.alert.Alert;
import com.xiaomi.mone.log.manager.model.alert.AlertJobDetail;
import com.xiaomi.mone.log.manager.model.bo.alert.AlertParam;
import com.xiaomi.mone.log.manager.model.bo.alert.AlertUpdateParam;
import com.xiaomi.mone.log.manager.model.bo.alert.MqInfoBo;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.config.anno.Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.common.Config;
import org.apache.ozhera.log.common.Constant;
import org.apache.ozhera.log.manager.common.exception.MilogManageException;
import org.apache.ozhera.log.manager.model.pojo.MilogMiddlewareConfig;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static org.apache.ozhera.log.common.Constant.SYMBOL_COMMA;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 日志告警迁移到数据工厂
 * @date 2022/9/8 15:10
 */
@Service
@Slf4j
public class FlinkAlphaService implements AlertJobService {

    @Value(value = "$derora_endpoint")
    public String endpoint;
    @Value(value = "$server.type")
    private String serverType;
    @Value(value = "$hdfs.file")
    private String hdfsJarFilePath;
    @Value("$alpha_base_url")
    private String alphaBaseUrl;
    @Value("$alpha_token")
    private String alphaToken;
    @Value(value = "$team.access_key")
    private String secretKeyId;

    @Value(value = "$team.secret_key")
    private String secretKey;

    @Value(value = "$producer.server")
    private String producerServer;

    @Value(value = "$producer.topic")
    private String producerTopic;

    @Value(value = "$alert.team.id")
    private String alertTeamId;

    @Value("$alert.team.name")
    private String alertTeamName;

    @Value("$alert.main.class")
    private String DEFAULT_MAIN_CLASS;

    private _Api alphaApi;

    private String baseArguments;

    @Resource
    private AlertService alertService;
    @Resource
    private InnerLogMiddlewareConfigDao middlewareConfigDao;
    @Resource
    private ManagerConfig managerConfig;
    private Gson gson = Constant.GSON;

    private static final Integer DEFAULT_RETRY_TIME = 10;
    private static final Integer DEFAULT_RETRY_INTERVALS = 5;
    private static final String DEFAULT_JOB_MANAGER_MEMORY = "2G";
    private static final Integer DEFAULT_PARALLELISM = 1;
    private static final String DEFAULT_FLINK_VERSION = "1.14";
    private static final String DEFAULT_DESCRIPTION = "日志任务告警";
    private static final String clusterName = "akprc-hadoop";
    private static final String stagingClusterName = "tjwqstaging-hdd";

    private Map<String, String> regionToken = new HashMap<>();

    public void init() {
        Properties prop = new Properties();
        prop.setProperty("galaxy.streaming.jobserver.endpoint", endpoint);
        prop.setProperty("galaxy.streaming.jobserver.api.version", "/v3/api");
        alphaApi = new _Api();
        baseArguments = "";
        // 正式环境网址是下面的，需要设置，测试环境不需要设置
        alphaApi.getApiClient().setBasePath(String.format("%s/%s", alphaBaseUrl, "openapi"));
        serializeBaseArguments();
        regionTokenFill();
    }

    private void regionTokenFill() {
        for (InnerMachineRegionEnum value : InnerMachineRegionEnum.values()) {
            if (null != middlewareConfigDao) {
                MilogMiddlewareConfig middlewareConfig = middlewareConfigDao.queryByTypeAndRegionEn(value.getEn(), InnerMiddlewareEnum.PLATFORM_DEFAULT_TALOS.getCode());
                if (null != middlewareConfig) {
                    regionToken.put(value.getEn(), middlewareConfig.getToken());
                }
            }
        }
        regionToken.put(InnerMachineRegionEnum.CN_MACHINE.getEn(), alphaToken);
    }

    private void serializeBaseArguments() {
        HashMap<String, String> map = Maps.newHashMap();
        map.put("producerServer", producerServer);
        map.put("producerTopic", producerTopic);
        map.put("producerAccessKey", secretKeyId);
        map.put("producerSecretKey", secretKey);
        baseArguments = serialize(map);
    }

    public String buildFlinkJobName(Long alertId) {
        return String.format("%s_%s_%s", "Mione", serverType, alertId);
    }

    /**
     * 提交任务到数据工厂,返回作业Id
     */
    @Override
    public Long submitFlinkJob(Alert alert, AlertParam param, MqInfoBo mqInfoBo) {
        long alertId = alert.getId();
        String flinkJobName = buildFlinkJobName(alertId);

        final CreateFlinkJarJobRequest request = buildCreateFlinkJarJobRequest(flinkJobName);

        List<String> arguments = new ArrayList<>();
        arguments.add(baseArguments + serializeFlinkArguments(alert, param, mqInfoBo));
        request.setArguments(arguments);
        List<Notify> notifyList = new ArrayList<>();
        notifyList.add(getUserNotify());
        request.setNoticeList(notifyList);
        // 进行调用
        String authorization = getAuthorization(param.getRegionEn());
        try {
            final Long id = alphaApi.createFlinkJarJobUsingPOST(authorization, request);
            log.info("submitFlinkJob in alpha succeed,job id:{}", id);
            return id;
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            log.error("submitFlinkJob to alpha error,request:{},reason:{}", gson.toJson(request), new String(e.getResponseBodyAsByteArray(), Charset.defaultCharset()), e);
            return null;
        }
    }

    @NotNull
    private CreateFlinkJarJobRequest buildCreateFlinkJarJobRequest(String flinkJobName) {
        final CreateFlinkJarJobRequest request = new CreateFlinkJarJobRequest();
        // 必填参数
        request.setJobType(UpdateFlinkJARJobRequest.JobTypeEnum.FLINKJAR_STREAMING.getValue());
        request.setFlinkVersion(DEFAULT_FLINK_VERSION);
        request.setJarPath(StringUtils.substringBeforeLast(hdfsJarFilePath, "/"));
        request.setJarName(StringUtils.substringAfterLast(hdfsJarFilePath, "/"));
        request.setMainClass(DEFAULT_MAIN_CLASS);
        request.setJobName(flinkJobName);
        request.setDescription(DEFAULT_DESCRIPTION);
        request.setRetryTimes(DEFAULT_RETRY_TIME);
        request.setRetryIntervals(DEFAULT_RETRY_INTERVALS);
        request.setJobManagerMemory(DEFAULT_JOB_MANAGER_MEMORY);
        request.setTaskManagerMemory(DEFAULT_JOB_MANAGER_MEMORY);
        request.setParallelism(DEFAULT_PARALLELISM);
        request.setFrameworkParams(Lists.newArrayList());
        request.setMode(CreateFlinkJarJobRequest.ModeEnum.SAVE_AND_RUN);
        request.setCluster(managerConfig.isProd() ? clusterName : stagingClusterName);
        return request;
    }

    @Override
    public boolean startJob(String regionEn, final Long jobId, final Long alertId) {
        String authorization = getAuthorization(regionEn);
        try {
            // 要启动的作业版本
            final Integer version = null;
            final Long taskId = alphaApi.startJobUsingPOST(authorization, jobId, version);
            log.info("start job success，jobId:{},alertId:{},taskId:{}", jobId, alertId, taskId);
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            log.error("startJob to alpha error,jobId:{},alertId:{},reason:{}", jobId, alertId, new String(e.getResponseBodyAsByteArray(), Charset.defaultCharset()), e);
            BaseJobDetailDto jobDetail = getJobDetail(regionEn, jobId);
            if (BaseJobDetailDto.JobStatusEnum.STARTED == jobDetail.getJobStatus()) {
                log.info("started:{}", jobId);
                return true;
            }
            return false;
        }
        return true;
    }

    private String getAuthorization(String regionEn) {
        if (!regionToken.containsKey(regionEn)) {
            MilogMiddlewareConfig middlewareConfig = middlewareConfigDao.queryByTypeAndRegionEn(regionEn, InnerMiddlewareEnum.PLATFORM_DEFAULT_TALOS.getCode());
            if (null != middlewareConfig) {
                regionToken.put(regionEn, middlewareConfig.getToken());
            }
        }
        return String.format("workspace-token/1.0 %s", regionToken.getOrDefault(regionEn, alphaToken));
    }

    @Override
    public boolean deleteJob(String regionEn, final Long jobId, final Long alertId) {
        String authorization = getAuthorization(regionEn);
        try {
            // 要删除的作业版本
            final Long id = alphaApi.deleteJobUsingPOST(authorization, jobId, null);
            log.info("delete job success，jobId:{},alertId:{}", jobId, alertId);
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            log.error("delete to alpha job error,jobId:{},alertId:{},error:{}", jobId, alertId, new String(e.getResponseBodyAsByteArray(), Charset.defaultCharset()), e);
            throw new MilogManageException(String.format("delete job to alpha job error:%s", new String(e.getResponseBodyAsByteArray(), Charset.defaultCharset())), e);
        }
        return true;
    }

    @Override
    public boolean stopJob(String regionEn, final Long jobId, final Long alertId) {
        String authorization = getAuthorization(regionEn);
        try {
            final Boolean status = alphaApi.stopJobUsingPOST(authorization, jobId, null, false);
            if (status) {
                log.info("stop job success，jobId:{},alertId:{}", jobId, alertId);
            }
            //删除checkpoint
            Map<String, String> headerMap = Maps.newHashMap();
            headerMap.put("Content-Type", "application/json");
            headerMap.put("Authorization", authorization);
            deleteCheckPoint(jobId, headerMap);

            log.info("stop job fail，jobId:{},alertId:{}", jobId, alertId);
            return true;
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            log.error("stop job in alpha error,jobId:{},alertId:{},reason:{}", jobId, alertId, new String(e.getResponseBodyAsByteArray(), Charset.defaultCharset()), e);
            BaseJobDetailDto jobDetail = getJobDetail(regionEn, jobId);

            if (BaseJobDetailDto.JobStatusEnum.STOPPED == jobDetail.getJobStatus()) {
                return true;
            }
            return false;
        }
    }

    public BaseJobDetailDto getJobDetail(String regionEn, Long jobId) {
        String authorization = getAuthorization(regionEn);
        BaseJobDetailDto jobDetail = alphaApi.getJobUsingGET(authorization, jobId, null);
        return jobDetail;
    }

    @Override
    public boolean updateJob(final Alert alert, final AlertParam param, MqInfoBo mqInfoBo) {
        if (null == alert.getJobId()) {
            createAlarmJob(alert, param, mqInfoBo);
            return true;
        }
        Long jobId = alert.getJobId();
        String flinkJobName = buildFlinkJobName(alert.getId());
        UpdateFlinkJARJobRequest request = buildUpdateFlinkJARJobRequest(jobId, flinkJobName);
        List<String> arguments = Lists.newArrayList(baseArguments + serializeFlinkArguments(alert, param, mqInfoBo));
        request.setArguments(arguments);
        List<Notify> notifyList = Lists.newArrayList(getUserNotify());
        request.setNoticeList(notifyList);

        String authorization = getAuthorization(param.getRegionEn());
        try {
            BaseJobDetailDto response = alphaApi.updateFlinkJARJobUsingPUT(authorization, jobId, request);
            log.info("update job success，response:{},alertId:{}", gson.toJson(response), alert.getId());
            return true;
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            log.error("update to alpha job error,jobId:{},alertId:{},reason:{}", jobId, alert.getId(), new String(e.getResponseBodyAsByteArray(), Charset.defaultCharset()), e);
            throw new MilogManageException(String.format("update Job in alpha error,jobId:%s,alertId:%s", jobId, alert.getId()), e);
        }
    }

    private void createAlarmJob(Alert alert, AlertParam params, MqInfoBo mqInfoBo) {
        Long jobId = submitFlinkJob(alert, params, mqInfoBo);
        alert.setJobId(jobId);
        alert.setUtime(Instant.now().toEpochMilli());
        alertService.updateAlert(alert);
    }

    /**
     * 在数据工厂根据作业名称获取jobId
     *
     * @param jobName
     * @return
     */
    public Long getFlinkJobIdByName(String jobName) {
        String authorization = getAuthorization(InnerMachineRegionEnum.CN_MACHINE.getEn());
        int page = 1;
        int pageSize = 100;
        Long jobId = null;
        String processingType = "STREAMING";
        try {
            String searchKey = URLEncoder.encode(jobName, Charset.defaultCharset().name());
            Long workflowId = null;
            GetJobListResponse response = alphaApi.listJobUsingGET(authorization, page, pageSize, jobId, null, null, false, processingType, null, searchKey, workflowId);
            log.info("query fink job data,size:{},jobName:{}", response.getJobList().size(), jobName);
            for (JobListDto1 jobListDto1 : response.getJobList()) {
                if (Objects.equals(jobName, jobListDto1.getJobName())) {
                    return jobListDto1.getJobId();
                }
            }
            log.info("query fink job no data,jobName:{}", jobName);
            return null;
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            log.info("getFlinkJobIdByName error,jobName:{},reason:{}", new String(e.getResponseBodyAsByteArray(), Charset.defaultCharset()), jobName, e);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return null;
    }

    @NotNull
    private UpdateFlinkJARJobRequest buildUpdateFlinkJARJobRequest(Long jobId, String flinkJobName) {
        AlertJobDetail jobDetail = getJobDetail(jobId);
        if (null == jobDetail) {
            throw new MilogManageException("flink告警任务不存在,任务Id:" + jobId);
        }
        UpdateFlinkJARJobRequest request = new UpdateFlinkJARJobRequest();

        request.setJobName(flinkJobName);
        request.setJarPath(StringUtils.substringBeforeLast(hdfsJarFilePath, "/"));
        request.setJobType(UpdateFlinkJARJobRequest.JobTypeEnum.FLINKJAR_STREAMING);
        request.setFlinkVersion(DEFAULT_FLINK_VERSION);
        request.setJarName(StringUtils.substringAfterLast(hdfsJarFilePath, "/"));
        request.setMainClass(DEFAULT_MAIN_CLASS);
        request.setDescription(DEFAULT_DESCRIPTION);
        request.setRetryTimes(jobDetail.getRetryTimes());
        request.setJobManagerMemory(jobDetail.getJobManagerMemory());
        request.setTaskManagerMemory(jobDetail.getTaskManagerMemory());
        request.setParallelism(jobDetail.getParallelism());
        request.setMode(UpdateFlinkJARJobRequest.ModeEnum.SAVE_AND_RUN);
        request.setFrameworkParams(jobDetail.getFrameworkParams());
        request.setOwner(Config.ins().get("flink_job_director", "wangtao29"));
        // 注意这里多一个updateMode
        request.setUpdateMode(UpdateFlinkJARJobRequest.UpdateModeEnum.ALL);
        request.setCluster(managerConfig.isProd() ? clusterName : stagingClusterName);
        return request;
    }

    public AlertJobDetail getJobDetail(Long jobId) {
        String url = String.format("/openapi/develop/jobs/%s", jobId);
        return DtUtils.get(url, alphaToken, AlertJobDetail.class);
    }


    private String serializeFlinkArguments(Alert alert, AlertParam params, MqInfoBo mqInfoBo) {
        HashMap<String, String> input = Maps.newHashMap((alert.getArguments()));
        input.put("accessKey", mqInfoBo.getConsumerAccessKey());
        input.put("secretKey", mqInfoBo.getConsumerSecretKey());
        input.put("alertId", String.valueOf(alert.getId()));
        input.put("consumerServer", mqInfoBo.getConsumerServer());
        input.put("consumerTopic", mqInfoBo.getTopics().stream().collect(Collectors.joining(SYMBOL_COMMA)));
        input.put("consumerTag", mqInfoBo.getConsumerTags().stream().collect(Collectors.joining(SYMBOL_COMMA)));
        input.put("mqType", params.getMqType());
        input.put("consumerGroup", mqInfoBo.getConsumerGroups().stream().collect(Collectors.joining(SYMBOL_COMMA)));
        input.put("windowSize", String.valueOf(params.getWindowSize()));
        return serialize(input);
    }

    private String serialize(Map<String, String> map) {
        StringBuilder builder = new StringBuilder();
        for (Map.Entry<String, String> current : map.entrySet()) {
            builder.append("--");
            builder.append(current.getKey());
            builder.append(" ");
            builder.append(current.getValue());
            builder.append(" ");
        }
        return builder.toString();
    }

    public AlertParam buildAlertParam(AlertUpdateParam param) {
        Alert alert = alertService.getAlert(param.getAlertId());
        AlertParam alertParam = param.toAlertParam();
        alertParam.setName(alert.getName());
        alertParam.setAppId(alert.getApp());
        alertParam.setAppName(alert.getAppName());
        alertParam.setTailIds(param.getTailIds());
        alertParam.setRegionEn(param.getRegionEn());
        return alertParam;
    }

    public Notify getUserNotify() {
        List<String> notifyIfArrays = new ArrayList<>();
        // 失败的时候告警
        notifyIfArrays.add("FAILED");
        // 任务被关闭的时候告警
//        notifyIfArrays.add("KILLED");

        final NotifyObject notifyAlert = getNotifyAlert(alertTeamId, alertTeamName);
        List<NotifyObject> notifyingReceiver = new ArrayList<>();
        notifyingReceiver.add(notifyAlert);

        Notify notify = new Notify();
        // 设置告警条件
        notify.setNotifyIf(notifyIfArrays);
        // P0 P1 P2
        notify.setNotifyLevel("P1");
        // 目前只能填这个
        notify.setNotifyProvider("Falcon");
        notify.setNotifyingReceiver(notifyingReceiver);
        //自定义告警
        notify.setCustomConditionThreshold(7);
        notify.setRetryTriggerCondition(NotifyTriggerCondition.custom);

        return notify;
    }

    private NotifyObject getNotifyAlert(String alertTeamIds, String alertTeamNames) {
        NotifyObject notifyObject = new NotifyObject();
        notifyObject.setNotifyObjectType(NotifyObject.NotifyObjectTypeEnum.ONCALL);
        List<NotifyReceiver> notifyReceivers = new ArrayList<>();
        String[] alertTeamIdsArray = alertTeamIds.split(SYMBOL_COMMA);
        String[] alertTeamNamesArray = alertTeamNames.split(SYMBOL_COMMA);
        for (int i = 0; i < alertTeamIdsArray.length; i++) {
            NotifyReceiver receiver = new NotifyReceiver();
            receiver.setId(alertTeamIdsArray[i].trim());
            try {
                receiver.setValue(alertTeamNamesArray[i].trim());
            } catch (Exception e) {
                log.error("get alertTeamName error,alertTeamIds:{},alertTeamNames:{}", alertTeamIds, alertTeamNames, e);
                receiver.setValue(alertTeamNamesArray[i - 1].trim());
            }
            notifyReceivers.add(receiver);
        }
        notifyObject.setReceivers(notifyReceivers);
        return notifyObject;
    }

    public void deleteCheckPoint(Long jobId, Map<String, String> headerMap) {
        String delCheckPointUrl = String.format("/openapi/develop/jobs/%s/op/checkpoint", jobId);
        String deleteResult = DtUtils.delete(delCheckPointUrl, headerMap);
        log.info("delResult:{}", deleteResult);
    }
}
