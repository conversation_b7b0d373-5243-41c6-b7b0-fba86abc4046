package com.xiaomi.mone.log.manager.controller;

import com.xiaomi.mone.log.manager.model.vo.MatrixTraceLogParam;
import com.xiaomi.mone.log.manager.service.impl.InnerEsDataServiceImpl;
import com.xiaomi.mone.log.manager.service.impl.MatrixLogServiceImpl;
import com.xiaomi.youpin.docean.anno.Controller;
import com.xiaomi.youpin.docean.anno.RequestMapping;
import com.xiaomi.youpin.docean.anno.RequestParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.common.Result;
import org.apache.ozhera.log.manager.common.context.MoneUserContext;
import org.apache.ozhera.log.manager.model.dto.LogDTO;
import org.apache.ozhera.log.manager.model.vo.LogQuery;

import java.io.IOException;
import java.util.Arrays;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/4/12 10:15
 */
@Controller
@Slf4j
public class InnerEsDataController {

    @Resource
    private MatrixLogServiceImpl matrixLogService;

    @Resource
    private InnerEsDataServiceImpl innerEsDataService;

    @RequestMapping(path = "/matrix/log/query")
    public Result<LogDTO> matrixLogQuery(LogQuery logQuery) throws Exception {
        return matrixLogService.logQuery(logQuery);
    }

    @RequestMapping(path = "/matrix/log/export", method = "get")
    public void matrixLogExport(@RequestParam(value = "appIds") String appIds,
                                @RequestParam(value = "logstore") String logstore,
                                @RequestParam(value = "tail") String tail,
                                @RequestParam(value = "startTime") Long startTime,
                                @RequestParam(value = "endTime") Long endTime,
                                @RequestParam(value = "fullTextSearch") String fullTextSearch) throws Exception {
        Long[] appIdArray = Arrays.stream(appIds.split(",")).map(Long::parseLong).toArray(Long[]::new);
        LogQuery logQuery = new LogQuery(logstore, tail, startTime, endTime, fullTextSearch, "timestamp", appIdArray);
        matrixLogService.logExport(logQuery);
    }

    @RequestMapping(path = "/log/queryMatrixTraceLog")
    public Result<LogDTO> queryMatrixTraceLog(MatrixTraceLogParam matrixTraceLogParam) throws Exception {
        return matrixLogService.queryMatrixTraceLog(matrixTraceLogParam);
    }

    @RequestMapping(path = "/milog/es/index/lifecycle", method = "get")
    public Result<String> esIndexLifeCycle(@RequestParam(value = "storeId") Long storeId) throws IOException {
        Long lifeCycle;
        try {
            lifeCycle = innerEsDataService.queryIndexLifeCycle(storeId);
        } catch (Throwable e) {
            log.error("esIndex LifeCycle query error, log search error, storeId:[{}],user:[{}]", storeId, MoneUserContext.getCurrentUser(), e);
            return Result.failParam(e.getMessage());
        }
        return Result.success(lifeCycle.toString());
    }
}
