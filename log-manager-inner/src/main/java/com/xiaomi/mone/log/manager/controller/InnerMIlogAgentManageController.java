package com.xiaomi.mone.log.manager.controller;

import com.xiaomi.mone.log.manager.service.InnerLogAgentService;
import com.xiaomi.youpin.docean.anno.Controller;
import com.xiaomi.youpin.docean.anno.RequestMapping;
import com.xiaomi.youpin.gwdash.bo.Page;
import com.xiaomi.youpin.gwdash.bo.ProjectDeployInfoDTO;
import org.apache.ozhera.log.common.Result;
import org.apache.ozhera.log.manager.model.vo.AgentListQuery;

import javax.annotation.Resource;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.INNER_LOG_AGENT_SERVICE;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/4/12 10:26
 */
@Controller
public class InnerMIlogAgentManageController {

    @Resource(name = INNER_LOG_AGENT_SERVICE)
    private InnerLogAgentService logAgentService;

    /**
     * agent list
     *
     * @return
     */
    @RequestMapping(path = "/agent/manage/list")
    public Result<Page<ProjectDeployInfoDTO>> agentList(AgentListQuery agentListQuery) {
        return logAgentService.getList(agentListQuery);
    }
}
