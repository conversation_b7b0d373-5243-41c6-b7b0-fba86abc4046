package com.xiaomi.mone.log.manager.service.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.xiami.mione.tesla.k8s.service.K8sProxyService;
import com.xiaomi.mione.tesla.k8s.bo.LogAgentListBo;
import com.xiaomi.youpin.docean.anno.Service;
import com.xiaomi.youpin.docean.plugin.dubbo.anno.Reference;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class K8sRpcComsumerServiceImpl {

    @Reference(interfaceClass = K8sProxyService.class, check = false,
            group = "$ref.k8sproxy.service.group", timeout = 20000)
    private K8sProxyService k8sProxyService;

    private static final Cache<String, List<LogAgentListBo>> CACHE_LOCAL_IPS = CacheBuilder.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build();

    private static final Cache<String, List<LogAgentListBo>> CACHE_LOCAL_AGENTS = CacheBuilder.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build();

    public List<LogAgentListBo> getLogAgent(final List<String> ips) {
        String key = ips.stream().sorted().collect(Collectors.joining(","));
        List<LogAgentListBo> logAgentListBos = CACHE_LOCAL_AGENTS.getIfPresent(key);
        if (CollectionUtils.isNotEmpty(logAgentListBos)) {
            return logAgentListBos;
        }
        Result<List<LogAgentListBo>> logAgent = k8sProxyService.getLogAgent(ips);
        CACHE_LOCAL_AGENTS.put(key, logAgent.getData());
        return logAgent.getData();
    }

    public List<LogAgentListBo> queryPodInfoByAgentIp(String agentIp) {
        List<LogAgentListBo> logAgentListBos = CACHE_LOCAL_IPS.getIfPresent(agentIp);
        if (CollectionUtils.isNotEmpty(logAgentListBos)) {
            return logAgentListBos;
        }
        try {
            Result<List<LogAgentListBo>> logPods = k8sProxyService.getLogPods(agentIp);
            logAgentListBos = logPods.getData();
            CACHE_LOCAL_IPS.put(agentIp, logAgentListBos);
        } catch (Exception e) {
            log.error(String.format("query Pod info by agentIp error,agentIp:%s", agentIp), e);
        }
        return logAgentListBos;
    }
}
