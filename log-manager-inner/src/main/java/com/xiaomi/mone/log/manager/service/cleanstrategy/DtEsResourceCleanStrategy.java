package com.xiaomi.mone.log.manager.service.cleanstrategy;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xiaomi.mone.log.manager.dao.InnerMilogLogStoreDao;
import com.xiaomi.mone.log.manager.model.dto.dt.DtTableInfoDTO;
import com.xiaomi.mone.log.manager.model.po.InnerMilogLogStoreDO;
import com.xiaomi.mone.log.manager.model.vo.ClearDtResourceParam;
import com.xiaomi.mone.log.manager.service.remoting.DtRemoteService;
import com.xiaomi.youpin.docean.anno.Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.ozhera.log.common.Config;
import org.apache.ozhera.log.manager.common.exception.MilogManageException;
import org.apache.ozhera.log.manager.mapper.MilogEsClusterMapper;
import org.apache.ozhera.log.manager.mapper.MilogEsIndexMapper;
import org.apache.ozhera.log.manager.model.pojo.MilogEsClusterDO;
import org.apache.ozhera.log.manager.model.pojo.MilogEsIndexDO;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 清理工场es资源和数据库不一致
 *
 * @author: songyutong1
 * @date: 2024/09/13/16:46
 */
@Service
@Slf4j
public class DtEsResourceCleanStrategy extends AbstractCleanStrategy {

    @Resource
    private MilogEsClusterMapper milogEsClusterMapper;

    @Resource
    private MilogEsIndexMapper esIndexMapper;

    @Resource
    private DtRemoteService dtRemoteService;

    @Resource
    private InnerMilogLogStoreDao innerMilogLogStoreDao;

    @Override
    public void clean(ClearDtResourceParam param, String uuid) {
        log.info("uuid:{}, start clean up esIndex diff in dt and db", uuid);
        MilogEsClusterDO milogEsClusterDO = milogEsClusterMapper.selectByTagAndArea("matrix", param.getMachineRoom());
        if (ObjectUtils.isEmpty(milogEsClusterDO)) {
            log.info("uuid:{}, clean up esIndex diff in dt and db failed, esCluster not exist, please check the machineRoom:{}", uuid, param.getMachineRoom());
            throw new MilogManageException("clean up esIndex diff in dt and db failed, esCluster not exist, please check the machineRoom" + param.getMachineRoom());
        }
        addEsIndexInDt(milogEsClusterDO, param, uuid);
        deleteEsIndexInDt(milogEsClusterDO, param, uuid);
        log.info("uuid:{}, clean up esIndex diff in dt and db finished", uuid);
    }

    /**
     * 补齐工场缺少的esIndex
     */
    public void addEsIndexInDt(MilogEsClusterDO cluster, ClearDtResourceParam param, String uuid) {
        QueryWrapper<MilogEsIndexDO> wrapper = new QueryWrapper<MilogEsIndexDO>().eq("cluster_id", cluster.getId());
        List<MilogEsIndexDO> indexList = esIndexMapper.selectList(wrapper);
        List<DtTableInfoDTO> dtTableList = dtRemoteService.queryDtTableList(cluster.getDtCatalog(), cluster.getDtDatabase(), false, true, "", "ES", param.getToken());

        HashSet<String> dtSet = dtTableList.stream().map(DtTableInfoDTO::getTableNameEn).collect(Collectors.toCollection(HashSet::new));
        List<String> esAddInDt = new ArrayList<>();
        indexList.forEach(esIndex -> {
            if (dtSet.contains(esIndex.getIndexName())) {
                return;
            }
            List<InnerMilogLogStoreDO> storeList = innerMilogLogStoreDao.getStoresByESInfo(esIndex.getIndexName(), cluster.getId());
            if (CollectionUtils.isEmpty(storeList)) {
                log.info("uuid:{}, logstore not exist in db, check esIndex:{}", uuid, esIndex.getIndexName());
                throw new MilogManageException("logstore not exist in db, check esIndex:" + esIndex.getIndexName());
            }
            esAddInDt.add(esIndex.getIndexName());
            if (Boolean.FALSE.equals(param.getClearFlag())) {
                return;
            }
            InnerMilogLogStoreDO logStore = storeList.getFirst();
            try {
                boolean success = dtRemoteService.createEsTable(
                        cluster.getRegion(),
                        cluster.getDtCatalog(),
                        cluster.getDtDatabase(),
                        esIndex.getIndexName(),
                        cluster.getToken(),
                        logStore.getKeyList(),
                        logStore.getColumnTypeList(),
                        logStore.getStorePeriod(),
                        "es index for hera logstore: " + logStore.getLogstoreName()
                );
                if (!success) {
                    log.info("uuid:{}, create esIndex into dt failed, esIndex:{}", uuid, esIndex.getIndexName());
                    throw new MilogManageException("create esIndex into dt failed , esIndex:" + esIndex.getIndexName());
                }
                dtSet.add(esIndex.getIndexName());
            } catch (Exception e) {
                log.info("uuid:{}, create esIndex into dt failed, esIndex:{}, error:{}", uuid, esIndex.getIndexName(), e.getMessage(), e);
                throw new MilogManageException(e);
            }
        });
        log.info("uuid:{}, add esIndex into dt:{}", uuid, esAddInDt);
    }

    /**
     * 删除工场多余esIndex
     */
    public void deleteEsIndexInDt(MilogEsClusterDO cluster, ClearDtResourceParam param, String uuid) {
        QueryWrapper<MilogEsIndexDO> wrapper = new QueryWrapper<MilogEsIndexDO>().eq("cluster_id", cluster.getId());
        List<MilogEsIndexDO> indexList = esIndexMapper.selectList(wrapper);
        List<DtTableInfoDTO> dtTableList = dtRemoteService.queryDtTableList(cluster.getDtCatalog(), cluster.getDtDatabase(), false, true, "", "ES", param.getToken());

        HashSet<String> indexSet = indexList.stream().map(MilogEsIndexDO::getIndexName).collect(Collectors.toCollection(HashSet::new));
        List<String> esDeleteInDt = new ArrayList<>();
        dtTableList.forEach(dtTable -> {
            String prefix = Config.ins().get("hera_es_prefix", "hera_index_");
            if (indexSet.contains(dtTable.getTableNameEn()) || !(dtTable.getTableNameEn().contains(prefix) || dtTable.getTableNameEn().contains("prod_matrix_hera_"))) {
                return;
            }
            esDeleteInDt.add(dtTable.getTableNameEn());
            if (Boolean.FALSE.equals(param.getClearFlag())) {
                return;
            }
            try {
                boolean success = dtRemoteService.deleteDtTable(dtTable.getCatalog(), dtTable.getDbName(), dtTable.getTableNameEn(), cluster.getToken());
                if (!success) {
                    log.error("uuid:{}, delete esIndex from dt failed, esIndex:{}", uuid, dtTable.getTableNameEn());
                    throw new MilogManageException("delete esIndex from dt failed , esIndex:" + dtTable.getTableNameEn());
                }
            } catch (Exception e) {
                log.error("uuid:{}, delete esIndex from dt failed, esIndex:{}, error:{}", uuid, dtTable.getTableNameEn(), e.getMessage(), e);
                throw new MilogManageException(e);
            }
        });
        log.info("uuid:{}, delete esIndex from dt:{}", uuid, esDeleteInDt);
    }

}
