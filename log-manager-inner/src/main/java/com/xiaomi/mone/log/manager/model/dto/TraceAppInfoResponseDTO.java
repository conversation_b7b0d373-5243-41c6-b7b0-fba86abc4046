package com.xiaomi.mone.log.manager.model.dto;

import com.google.gson.annotations.SerializedName;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 调 trace 接口查应用信息的返回
 * @date 2022-06-17
 */
@Data
public class TraceAppInfoResponseDTO {
    private int total;
    private TraceAppInfoData data;

    @Data
    public static class TraceAppInfoData {
        private Long appId;
        private String appName;
        private String orgId;
        private Long iamTreeId;
        private String deployPlatform;
        private List<TraceAppService> services;
    }

    @Data
    public static class TraceAppService {
        private Long iamTreeId;
        private String tracingCluster;
        private String serviceCluster;
        private Long serviceClusterId;
        private String serviceName;
        private String orgId;
        private String deployPlatform;
        private String extraConfigJson;
        // 从 extraConfigJson 解析出来
        private TraceAppExtra extraConfig;
    }

    @Data
    public static class TraceAppExtra {
        // 链路的接口 两个json都以string类型返回，需要额外的解析
        private String deployInfo;

        private String agentExtra;
        // 从 agentExtra 解析出来
        private ExtraDeployInfo deployInfoObject;
    }

    @Data
    public static class ExtraDeployInfo {
        @SerializedName("iamTreeId")
        private String iamTreeId;
        @SerializedName("matrix.app.deploy.space")
        private String matrixDeploySpace;
        @SerializedName("matrix.namespace")
        private String matrixNamespace;
        @SerializedName("cloudml.namespace")
        private String cloumlNamespace;
        @SerializedName("cloudml.instance.list")
        private String cloumlInstanceList;
    }
}
