<?xml version="1.0" encoding="UTF-8" ?>
<configuration scan="false" debug="false">
    <property resource="config.properties"></property>

    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>%d|%-5level|%X{trace_id}|%thread|%logger{40}|%L|%msg%n</pattern>
        </layout>
    </appender>

    <appender name="logfile"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${log.path}/server.log</File>
        <encoder>
            <pattern>%d|%-5level|%X{trace_id}|%thread|%logger{40}|%L|%msg%n</pattern>
        </encoder>

        <!-- 这里我们添加一个过滤器来排除ERROR级别的日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>DENY</onMatch>
            <onMismatch>ACCEPT</onMismatch>
        </filter>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${log.path}/server.log.%d{yyyy-MM-dd-HH}</FileNamePattern>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <maxHistory>168</maxHistory>
        </rollingPolicy>
    </appender>

    <appender name="error_file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${log.path}/error.log</File>
        <encoder>
            <pattern>%d|%-5level|%X{trace_id}|%thread|%logger{40}|%L|%msg%n</pattern>
        </encoder>

        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${log.path}/error.log.%d{yyyy-MM-dd-HH}</FileNamePattern>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <maxHistory>168</maxHistory>
        </rollingPolicy>
    </appender>

    <appender name="ASYNC_LOGFILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="logfile" />
        <queueSize>8192</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <neverBlocking>true</neverBlocking>
    </appender>

    <appender name="ASYNC_ERROR_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>8192</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <neverBlocking>true</neverBlocking>
        <appender-ref ref="error_file" />
    </appender>


    <logger name="org.springframework" level="ERROR"/>
    <logger name="ch.qos.logback" level="ERROR"/>
    <logger name="com.xiaomi.data.push.service.state" level="ERROR"/>
    <logger name="org.reflections.Reflections" level="ERROR"/>
    <logger name="com.xiaomi.infra.galaxy" level="ERROR"/>
    <logger name="com.*.*.mapper" level="debug" additivity="true"/>
    <logger name="com.alibaba.nacos" level="ERROR"/>
    <logger name="com.alibaba.nacos.client.naming" level="OFF"/>
    <logger name="org.apache.ozhera.log.manager.service.nacos.impl" level="WARN" additivity="false"/>

    <root level="INFO">
        <appender-ref ref="stdout"/>
        <appender-ref ref="ASYNC_LOGFILE" />
        <appender-ref ref="ASYNC_ERROR_FILE" />
    </root>


</configuration>