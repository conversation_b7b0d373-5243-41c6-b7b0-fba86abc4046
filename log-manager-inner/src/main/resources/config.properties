nacosAddr=${nacosAddr}
defaultNacosAddres=${defaultNacosAddres}
nacos_config_dataid=${nacos_config_dataid}
nacos_config_group=DEFAULT_GROUP
nacos_config_server_addr=${defaultNacosAddres}
close_nacos_plugin=false

serverName=${serverName}
serverNameHttp=${serverNameHttp}
serverPort=${serverPort}

db_pool_size=${db_pool_size}
es.app.index=${es.app.index}
es.nginx.index=${es.nginx.index}
es.free.index=${es.free.index}
es.opentelemetry.index=${es.opentelemetry.index}
es.docker.index=${es.docker.index}
app.env=${app.env}

driver.class=org.mariadb.jdbc.Driver

# ????
allow-cross-domain=true
# http????????(??????)
response-original-value=true
log.path=${log.path}
#rocketMQ http handle topic
rocketmq_authorization=${rocketmq_authorization}
rocketmq_x-ssl-client-verify=SUCCESS
rocketmq_x-ssl-client-dn=/C=CN/ST=BEIJING/L=BEIJING/O=XIAOMI/CN=cloud-manager-web
rocketmq_address=${rocketmq_address}
rocketmq_brokers=${rocketmq_brokers}
rocketmq_org_id=${rocketmq_org_id}
rocketmq_user_group_ids=${rocketmq_user_group_ids}
# rocketmq conf
rocketmq_consumer_on=${rocketmq_consumer_on}

rocketmq_consumer_group=${rocketmq_consumer_group}
rocketmq_consumer_topic=${rocketmq_consumer_topic}
rocketmq_consumer_tag=${rocketmq_consumer_tag}
rocketmq_consumer_scale_tag=${rocketmq_consumer_scale_tag}
k8s_rocketmq_consumer_topic=${k8s_rocketmq_consumer_topic}
k8s_rocketmq_consumer_tag=${k8s_rocketmq_consumer_tag}
k8s_rocketmq_consumer_group=${k8s_rocketmq_consumer_group}
miline_rocketmq_consumer_topic=${miline_rocketmq_consumer_topic}
miline_rocketmq_consumer_tag=${miline_rocketmq_consumer_tag}
miline_rocketmq_consumer_group=${miline_rocketmq_consumer_group}
deploysystem_rocketmq_consumer_topic=${deploysystem_rocketmq_consumer_topic}
deploysystem_rocketmq_consumer_tag=${deploysystem_rocketmq_consumer_tag}
deploysystem_rocketmq_consumer_group=${deploysystem_rocketmq_consumer_group}
mife_rocketmq_consumer_topic=${mife_rocketmq_consumer_topic}
mife_rocketmq_consumer_tag=${mife_rocketmq_consumer_tag}
mife_rocketmq_consumer_group=${mife_rocketmq_consumer_group}
miks_rocketmq_consumer_topic=${miks_rocketmq_consumer_topic}
miks_rocketmq_consumer_tag=${miks_rocketmq_consumer_tag}
miks_rocketmq_consumer_group=${miks_rocketmq_consumer_group}
# milog ???????mq??

# talos ??
talos_access_topic=${talos_access_topic}
#dubbo
dubbo_app_name=${dubbo_app_name}
dubbo.group=${dubbo.group}
dubbo.gwdash.china.group=${dubbo.gwdash.china.group}
dubbo.gwdash.youpin.group=${dubbo.gwdash.youpin.group}
dubbo.env.group=${dubbo.env.group}
dubbo_reg_check=${dubbo_reg_check}
dubbo_reg_address=${dubbo_reg_address}
dubbo_threads=${dubbo_threads}
nacos.config.addrs=${nacos.config.addrs}
dubbo.china.group=china_${dubbo.group}
dubbo.miline.group=${dubbo.miline.group}
dubbo.miline.rpc.env=${dubbo.miline.rpc.env}
ref.k8sproxy.service.group=${ref.k8sproxy.service.group}
dubbo.youpin.group=youpin_${dubbo.youpin.group}
db_open_transactional=true
db_print_sql=false

milogpattern=timestamp,level,traceId,threadName,className,line,message
mybatis_mapper_location=mapper/MilogEsClusterMapper.xml,mapper/MilogEsIndexMapper.xml,mapper/MilogLogProcessMapper.xml,mapper/MilogLogTemplateDetailMapper.xml,mapper/MilogLogTemplateMapper.xml,mapper/MilogLogCountMapper.xml,mapper/MilogLogstailMapper.xml,mapper/MilogLogSearchSaveMapper.xml,mapper/MilogEsClusterMapper.xml,mapper/MilogMatrixEsInfoMapper.xml,mapper/MilogLogNumAlertMapper.xml,mapper/MilogAnalyseDashboardMapper.xml,mapper/MilogAnalyseGraphMapper.xml,mapper/MilogAnalyseDashboardGraphRefMapper.xml,mapper/MilogAnalyseGraphTypeMapper.xml

#???mq?????????
log_type_mq_not_consume=${log_type_mq_not_consume}
#????????
default_login_user_emp_id=${default_login_user_emp_id}
## hdfs

alpha.catalog=${alpha.catalog}
alpha.db=${alpha.db}
alpha.engine=${alpha.engine}
alpha.tablename=${alpha.tablename}

europe.ip.key=europe.ip

kerberos=${kerberos}
yarn_queue=${yarn_queue}
flink_cluster=${flink_cluster}
derora_endpoint=${derora_endpoint}
hdfs.file=${hdfs.file}
alert.main.class=${alert.main.class}
producer.server=${producer.server}
producer.topic=${producer.topic}
hera.url=${hera.url}
server.type=${server.type}
operation.log.filtration=${operation.log.filtration}
flink_job_director=wangtao29
job_start_flag=${job_start_flag}
matrixapp_job_flag=${matrixapp_job_flag}
tpc_dubbo_group=${tpc_dubbo_group}
tpc_node_code=logger
mioap_domain=${mioap_domain}
matrix_domain=http://production-matrix.api.xiaomi.net
dt_domain=https://api-gateway.dp.pt.xiaomi.com

filter_urls=${filter_urls}
agent.heart.senders=${agent.heart.senders}
download_file_path=/tmp

log_stream_name=${log_stream_name}

hera_topic_prefix=${hera_topic_prefix}
hera_es_prefix=${hera_es_prefix}

#???
agent.extension.service=innerLogAgentService
directory.extension.service=innerDictionaryExtensionService
resource.extension.service=innerResourceExtensionService
store.extension.service=innerStoreExtensionService
tail.extension.service=innerTailExtensionService
common.extension.service=innerCommonExtensionService

store_operate_event_group=${store_operate_event_group}
store_operate_event_topic=${store_operate_event_topic}

## alert
alert_expire_duration=${alert_expire_duration}
alert_hash_expire_duration=${alert_hash_expire_duration}

## redis
redis.pool.max.total=${redis.pool.max.total}
redis.pool.max.idle=${redis.pool.max.idle}
redis.pool.min.idle=${redis.pool.min.idle}
redis.pool.max.wait=${redis.pool.max.wait}
redis.pool.min.evictable.idle.time=${redis.pool.min.evictable.idle.time}
redis.connection.timeout=${redis.connection.timeout}
redis.socket.timeout=${redis.socket.timeout}
redis.max.attempts=${redis.max.attempts}

## loki
loki.domain=${loki.domain}

## lifecycle
lifecycle.redis.expire.duration=${lifecycle.redis.expire.duration}
