nacosAddr=nacos.systech.b2c.srv:80$nacos.ams.pro.mi.com:80$nacos.sgp.pro.mi.com:80$nacos.mos.pro.mi.com:80$nacos.sh.pro.mi.com:80$nacos.india.pn.pro.mi.com:80$global.nacos.systech.b2c.srv:80
defaultNacosAddres=nacos.systech.b2c.srv:80

nacos_config_dataid=hera_log_manager
serverNameHttp=milog_manager_server_http
serverPort=7788

db_pool_size=20

es.tesla.index=youpin_insert_test-
es.app.index=zgq_common_milog_app_zjy_1
es.nginx.index=zgq_common_milog_nginx_zjy_1
es.free.index=zgq_common_milog_free_zjy_1
es.opentelemetry.index=zgq_common_milog_opentelemetry_zjy_1
allow-cross-domain=true
response-original-value=true
log.path=/home/<USER>/log/log-manager
#log.path=D:\\work\\log\\logmanager
#rocketMQ http handle topic
rocketmq_authorization=cloud-manager/1.0 {"identifier":{"name":"wangtao29","id":76391,"type":"user"},"roles":[{"old_team_id":33118,"org_id":80522,"create_resource_perm":true,"name":"_admin","id":102316,"org_name":"mione_zgq","type":"group","org_is_personal":false},{"old_team_id":33121,"org_id":80522,"create_resource_perm":true,"name":"rocket_mq","id":102322,"org_name":"mione_zgq","type":"group","org_is_personal":false},{"old_team_id":34613,"org_id":82062,"create_resource_perm":true,"name":"admin","id":105306,"org_name":"mione-staging","type":"group","org_is_personal":false},{"old_team_id":35028,"org_id":82407,"create_resource_perm":true,"name":"admin","id":106136,"org_name":"mione-c3","type":"group","org_is_personal":false},{"old_team_id":35591,"org_id":82901,"create_resource_perm":true,"name":"_admin","id":107262,"org_name":"wangtao29","type":"group","org_is_personal":true},{"old_team_id":35690,"org_id":82978,"create_resource_perm":true,"name":"milog_test_group","id":107460,"org_name":"milog_test_group","type":"group","org_is_personal":false},{"old_team_id":35694,"org_id":82981,"create_resource_perm":true,"name":"log_test","id":107468,"org_name":"staging_log_test","type":"group","org_is_personal":false},{"old_team_id":35902,"org_id":83158,"create_resource_perm":true,"name":"_admin","id":107884,"org_name":"BaseOrgMq","type":"group","org_is_personal":false},{"old_team_id":35903,"org_id":83158,"create_resource_perm":true,"name":"baseORgTeam","id":107886,"org_name":"BaseOrgMq","type":"group","org_is_personal":false},{"old_team_id":35971,"org_id":83215,"create_resource_perm":true,"name":"_admin","id":108022,"org_name":"myTestOrg","type":"group","org_is_personal":false},{"old_team_id":35973,"org_id":83215,"create_resource_perm":true,"name":"we","id":108026,"org_name":"myTestOrg","type":"group","org_is_personal":false},{"old_team_id":38457,"org_id":85382,"create_resource_perm":true,"name":"_admin","id":112994,"org_name":"mione_milog","type":"group","org_is_personal":false},{"old_team_id":38458,"org_id":85382,"create_resource_perm":true,"name":"milog","id":112996,"org_name":"mione_milog","type":"group","org_is_personal":false}]}
rocketmq_x-ssl-client-verify=SUCCESS
rocketmq_x-ssl-client-dn=/C=CN/ST=BEIJING/L=BEIJING/O=XIAOMI/CN=cloud-manager-web
rocketmq_address=http://shopapi-cnbj1-rmq-console.api.xiaomi.net
rocketmq_brokers=c3cloudsrv-shopapi-rocketmq-raft0
rocketmq_org_id=CL85382
rocketmq_user_group_ids=CI112996
# rocketmq conf
rocketmq_consumer_on=true

rocketmq_consumer_group=milog_topic_group_event
rocketmq_consumer_topic=gwdash_event_topic_online
rocketmq_consumer_tag=project_create,project_youpin_create
rocketmq_consumer_scale_tag=docker_scale,docker_youpin_scale
k8s_rocketmq_consumer_topic=k8s_machine_changed_intranet
k8s_rocketmq_consumer_tag=machine_change
k8s_rocketmq_consumer_group=k8s_machine_changed_intranet
miline_rocketmq_consumer_topic=miline_to_milog_topic_online
miline_rocketmq_consumer_tag=docker_scale_online
miline_rocketmq_consumer_group=miline_to_milog_topic_online
deploysystem_rocketmq_consumer_topic=deploysystem_to_milog_topic_online
deploysystem_rocketmq_consumer_tag=machine_change
deploysystem_rocketmq_consumer_group=deploysystem_to_milog_topic_online
mife_rocketmq_consumer_topic=mife_to_milog_topic_online
mife_rocketmq_consumer_tag=machine_change
mife_rocketmq_consumer_group=mife_to_milog_topic_online
miks_rocketmq_consumer_topic=miks_to_milog_topic_online
miks_rocketmq_consumer_tag=machine_change
miks_rocketmq_consumer_group=miks_to_milog_topic_online

# talos ??
talos_access_topic=mione_staging_jaeger_etl_sidecar
app.env=prod

#dubbo
dubbo_app_name=milog
dubbo.group=intranet
dubbo.gwdash.china.group=intranet
dubbo.gwdash.youpin.group=intranet-youpin
dubbo.miline.group=online
dubbo.miline.rpc.env=online
dubbo.env.group=online
ref.k8sproxy.service.group=online
dubbo.youpin.group=intranet-youpin
dubbo_reg_check=false
dubbo_reg_address=nacos://nacos.systech.b2c.srv:80
nacos.config.addrs=nacos.systech.b2c.srv:80
dubbo_threads=800

#???mq?????????
log_type_mq_not_consume=3

#????????
default_login_user_emp_id=52016

kerberos=<EMAIL>
yarn_queue=root.production.china_group.cnz_d.mione_cnz_alarm
flink_cluster=zjyprc-hadoop-flink1.12
derora_endpoint=https://cnbj4-fusion-derora.api.xiaomi.net
hdfs.file=hdfs://zjyprc-hadoop/user/s_youpin-biz-arch/talos-flink-cycle-content.jar
alert.main.class=com.xiaomi.mione.log.MilogAlarmBootstrap
producer.server=http://cnbj4-talos.api.xiaomi.net
producer.topic=mione_alarm
hera.url=https://hera.be.mi.com
server.type=intra

log_stream_name=milog-youpin

## hdfs
alpha.catalog=hive_zjyprc_hadoop
alpha.db=mi_log
alpha.engine=proxy.engine=presto
alpha.tablename=milog_talos_sink_data_prd

operation.log.filtration=statisticEs,logQuery,logOut,process,tailRateLimie

job_start_flag=true
matrixapp_job_flag=false

tpc_dubbo_group=online
tpc_pId=3

mioap_domain=http://cnbj1-mioap.api.xiaomi.net

filter_urls=queryAgentK8sIp,matrixLogQuery,queryMatrixTraceLog,getSpacesByUser,queryLogEnabled,queryLogBindTree

agent.heart.senders=wangtao29

hera_topic_prefix=prod_hera_topic_
hera_es_prefix=prod_hera_index_

store_operate_event_group=store_operate_event_group
store_operate_event_topic=store_operate_event_topic

## alert
alert_expire_duration=43200000
alert_hash_expire_duration=1314900000

## redis
redis.pool.max.total=50
redis.pool.max.idle=30
redis.pool.min.idle=10
redis.pool.max.wait=1000
redis.pool.min.evictable.idle.time=600000
redis.connection.timeout=3000
redis.socket.timeout=1000
redis.max.attempts=2

## loki
loki.domain=https://loki-milog.api.xiaomi.net

## lifecycle
lifecycle.redis.expire.duration=86400000
