package com.xiaomi.mone.log.manager.common;

import com.xiaomi.mone.cache.redis.RedisClientFactory;
import com.xiaomi.youpin.docean.Ioc;
import org.junit.Assert;
import org.junit.Test;
import redis.clients.jedis.JedisCluster;

import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;


/**
 * @author: songyutong1
 * @date: 2024/09/26/13:09
 */
public class RedisClientFactoryTest {

    @Test
    public void testGetJedisCluster() {
        getConfigFromNanos();
        RedisClientFactory.init();
        Ioc.ins().init("com.xiaomi");
        JedisCluster jedisCluster = RedisClientFactory.getJedisCluster();
        jedisCluster.set("test", "123");
        String result = jedisCluster.get("test");
        jedisCluster.del("test");
        Assert.assertEquals("123", result);
    }

}
