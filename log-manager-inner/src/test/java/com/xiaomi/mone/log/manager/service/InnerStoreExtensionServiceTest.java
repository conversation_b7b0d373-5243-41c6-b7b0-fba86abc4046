package com.xiaomi.mone.log.manager.service;

import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.api.enums.OperateEnum;
import org.apache.ozhera.log.manager.model.pojo.MilogLogStoreDO;
import org.apache.ozhera.log.manager.model.vo.LogStoreParam;
import org.junit.Before;
import org.junit.Test;

import java.io.IOException;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.INNER_STORE_SERVICE;
import static com.xiaomi.mone.log.manager.service.listener.StoreOperateListener.regStoreOperateListener;
import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/2/29 10:34
 */
@Slf4j
public class InnerStoreExtensionServiceTest {

    private InnerStoreExtensionService storeExtensionService;

    @Before
    public void buildBean() {
        getConfigFromNanos();
        Ioc.ins().init("com.xiaomi");
        regStoreOperateListener();
        storeExtensionService = Ioc.ins().getBean(INNER_STORE_SERVICE);
    }

    @Test
    public void postProcessingTest() throws IOException {
        MilogLogStoreDO storeDO = new MilogLogStoreDO();
        storeDO.setId(2L);
        storeDO.setSpaceId(1L);
        storeDO.setLogstoreName("milog_store");
        storeDO.setStorePeriod(100);

        LogStoreParam storeParam = new LogStoreParam();
        OperateEnum operateEnum = OperateEnum.UPDATE_OPERATE;
        storeExtensionService.postProcessing(storeDO, storeParam, operateEnum);
        System.in.read();

    }
}
