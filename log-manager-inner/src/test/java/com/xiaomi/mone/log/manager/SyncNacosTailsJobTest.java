package com.xiaomi.mone.log.manager;

import com.xiaomi.mone.log.manager.job.SyncNacosTailsJob;
import com.xiaomi.youpin.docean.Ioc;
import org.junit.Before;
import org.junit.Test;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.List;

import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;

public class SyncNacosTailsJobTest {
    @Resource
    private SyncNacosTailsJob job;

    @Before
    public void buildBean() {
        getConfigFromNanos();
        Ioc.ins().init("com.xiaomi", "org.apache.ozhera.log.manager");
        job = Ioc.ins().getBean(SyncNacosTailsJob.class);
    }

    @Test
    public void testHandleSpaces() {
        List<Long> spaceIds = Arrays.asList(344L);
        job.handleSpaces(spaceIds);
    }
}
