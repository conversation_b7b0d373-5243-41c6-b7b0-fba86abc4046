package com.xiaomi.mone.log.manager.common;

import junit.framework.TestCase;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.manager.common.Utils;
import org.apache.ozhera.log.utils.IndexUtils;
import org.junit.Test;

public class UtilsTest extends TestCase {

    public void testParse2KeyAndTypeList() {
        String keyList = "timestamp:1,level:1,traceId:1,threadName:1,className:1,line:1,methodName:1,message:1,podName:1,faas:2,logstore:3,logsource:3,mqtopic:3,mqtag:3,logip:3,tail:3,linenumber:3";
        String typeList = "timestamp,level,traceId,threadName,className,faas,podName,message";
        String newKeyList = Utils.parse2KeyAndTypeList(keyList, typeList);
        System.out.println(newKeyList);
    }

    public void testGetNumberValueList() {
        String keyList = "timestamp:1,level:1,traceId:1,threadName:1,className:1,line:1,methodName:1,message:1,podName:1,faas:2,logstore:3,logsource:3,mqtopic:3,mqtag:3,logip:3,tail:3,linenumber:3";
        String valueList = "timestamp,level,traceId,threadName,className,faas,podName,message";
        String newValueList = IndexUtils.getNumberValueList(keyList,valueList);
        System.out.println(newValueList);
    }


    @Test
    public void testWrap(){
//        String tailName  = "proretail-bi-online";
        String tailName  = "中国";
        String wrap = StringUtils.wrap(tailName, '"');
        System.out.println(wrap);
    }
}