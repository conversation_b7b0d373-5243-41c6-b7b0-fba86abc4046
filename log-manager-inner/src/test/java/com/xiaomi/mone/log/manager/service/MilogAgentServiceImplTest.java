package com.xiaomi.mone.log.manager.service;

import com.google.gson.Gson;
import com.xiaomi.mone.cache.redis.RedisClientFactory;
import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.api.model.meta.LogCollectMeta;
import org.apache.ozhera.log.api.service.AgentConfigService;
import org.apache.ozhera.log.manager.service.extension.agent.MilogAgentServiceImpl;
import org.apache.ozhera.log.manager.service.impl.AgentConfigServiceImpl;
import org.junit.Test;

import java.util.Arrays;

import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;


@Slf4j
public class MilogAgentServiceImplTest {
    @Test
    public void getList() {
        Ioc.ins().init("com.xiaomi");
        MilogAgentServiceImpl milogAgentService = Ioc.ins().getBean(MilogAgentServiceImpl.class);
    }

    @Test
    public void testConfigIssueAgent() {
        Ioc.ins().init("com.xiaomi");
        MilogAgentServiceImpl milogAgentService = Ioc.ins().getBean(MilogAgentServiceImpl.class);
        milogAgentService.configIssueAgent("1", "127.0.0.1", "etret");
    }

    @Test
    public void testMan() {
        Ioc.ins().init("com.xiaomi");
        MilogAgentServiceImpl milogAgentService = Ioc.ins().getBean(MilogAgentServiceImpl.class);
        milogAgentService.publishIncrementConfig(14L, 4L, Arrays.asList("127.0.0.1"));
    }


    /**
     * 测试删除配置-通知log-agent停止收集
     */
    @Test
    public void testDelConfigStopColl() {
        Ioc.ins().init("com.xiaomi");
        MilogAgentServiceImpl milogAgentService = Ioc.ins().getBean(MilogAgentServiceImpl.class);
        milogAgentService.publishIncrementDel(79L, 667L, null);
    }

    @Test
    public void process1() {
        getConfigFromNanos();
        RedisClientFactory.init();
        Ioc.ins().init("com.xiaomi.mone", "com.xiaomi.youpin", "org.apache.ozhera.log.manager");
        AgentConfigService agentConfigService = Ioc.ins().getBean(AgentConfigServiceImpl.class);
        long startTime = System.currentTimeMillis();
        LogCollectMeta logCollectMeta = agentConfigService.getLogCollectMetaFromManager("10.38.161.15");
        String responseInfo = new Gson().toJson(logCollectMeta);
        long costTime = (System.currentTimeMillis() - startTime) / 1000;
        log.info("agent启动获取配置,获取到的配置信息:{},costTime:{}", responseInfo, costTime);
    }

}