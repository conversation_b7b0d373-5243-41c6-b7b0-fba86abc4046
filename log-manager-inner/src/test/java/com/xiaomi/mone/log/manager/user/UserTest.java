package com.xiaomi.mone.log.manager.user;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.xiaomi.mone.cache.redis.RedisClientFactory;
import com.xiaomi.mone.log.manager.service.IdmMoneUserDetailService;
import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.common.Constant;
import org.apache.ozhera.log.manager.dao.MilogAppMiddlewareRelDao;
import org.apache.ozhera.log.manager.dao.MilogRegionAvailableZoneDao;
import org.apache.ozhera.log.manager.model.pojo.MilogAppMiddlewareRel;
import org.apache.ozhera.log.manager.model.pojo.MilogRegionAvailableZoneDO;
import org.junit.Before;
import org.junit.Test;

import java.time.Instant;
import java.util.List;

import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2021/9/9 17:44
 */
@Slf4j
public class UserTest {

    private Gson gson = new Gson();
    private IdmMoneUserDetailService userService;
    private MilogAppMiddlewareRelDao appMiddlewareRelDao;

    @Before
    public void init() {
        getConfigFromNanos();
        RedisClientFactory.init();
        Ioc.ins().init("com.xiaomi.mone", "com.xiaomi.youpin", "org.apache.ozhera.log.manager");
        userService = Ioc.ins().getBean(IdmMoneUserDetailService.class);
        appMiddlewareRelDao = Ioc.ins().getBean(MilogAppMiddlewareRelDao.class);
    }

    @Test
    public void queryUserByPhoneIdTest() {
        String phone = "13718112370";
//        String uId = userService.queryUserUIdByPhone(phone);
        String uId = "58423baab6334d07a9e81b4c603ad3ed";
        UseDetailInfo useDetailInfo = userService.queryUser(uId);
        log.info("UseDetailInfo:{},phone:{}", gson.toJson(useDetailInfo), phone);

        List<MilogAppMiddlewareRel> milogAppMiddlewareRels = appMiddlewareRelDao.queryByCondition(null, 1L, 304L);
        log.info("result:{}", milogAppMiddlewareRels);
    }

    @Test
    public void userIdByPhoneTest() {
        String phone = "543535435";
        String uId = userService.queryUserUIdByPhone(phone);
        log.info("userId:{},phone:{}", uId, phone);
    }

    @Test
    public void userIdByEmpIdTest() {
        String empId = "52016test";
        String uId = userService.queryUserUIdByEmpId(empId);
        log.info("userId:{},phone:{}", uId, empId);
    }

    @Test
    public void userIdByUserNameTest() {
        String userName = "zhangrong7";
        String uId = userService.queryUserUIdByUserName(userName);
        log.info("userId:{},userName:{}", uId, userName);
    }

    @Test
    public void testUser() {
        String empId = "52061";
        MoneUser moneUId = InnerMoneUtil.findMoneUserByUId(empId);
        System.out.println(moneUId);

    }

    @Test
    public void test1() {
        String headerData = "stsetest32432432";
        MoneUser moneUser = InnerMoneUtil.getUserInfo(headerData);
        log.info("登陆人的信息：{}", new Gson().toJson(moneUser));

    }

    @Test
    public void queryChildDept() {
        JsonArray dept = userService.queryChildDept("MI");
        log.info("dept:{}", dept);
    }

    @Test
    public void testInsert() {
        String jsonStr = "";
        RegionZoneBO regionZoneBO = gson.fromJson(jsonStr, RegionZoneBO.class);
        MilogRegionAvailableZoneDao regionAvailableZoneDao = Ioc.ins().getBean(MilogRegionAvailableZoneDao.class.getCanonicalName());
        regionZoneBO.getData().forEach(innerClass -> {
            if (innerClass.getIs_used()) {
                MilogRegionAvailableZoneDO milogRegionAvailableZoneDO = new MilogRegionAvailableZoneDO();
                milogRegionAvailableZoneDO.setRegionNameEN(innerClass.getRegion_en());
                milogRegionAvailableZoneDO.setRegionNameCN(innerClass.getRegion_cn());
                milogRegionAvailableZoneDO.setZoneNameCN(innerClass.getZone_name_cn());
                milogRegionAvailableZoneDO.setZoneNameEN(innerClass.getZone_name_en());
                milogRegionAvailableZoneDO.setCtime(Instant.now().toEpochMilli());
                milogRegionAvailableZoneDO.setUtime(Instant.now().toEpochMilli());
                milogRegionAvailableZoneDO.setCreator(Constant.DEFAULT_OPERATOR);
                milogRegionAvailableZoneDO.setUpdater(Constant.DEFAULT_OPERATOR);
                regionAvailableZoneDao.insert(milogRegionAvailableZoneDO);
            }
        });
        System.out.println(regionZoneBO);
    }
}
