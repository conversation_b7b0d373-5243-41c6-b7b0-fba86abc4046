package com.xiaomi.mone.log.manager;

import com.google.common.util.concurrent.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/9/28 10:05
 */
@Slf4j
public class CommonTest {

    @Test
    public void testListenableFuture() throws InterruptedException {
        ExecutorService executor = Executors.newFixedThreadPool(5);
        ListeningExecutorService guavaExecutor = MoreExecutors.listeningDecorator(executor);
        ListenableFuture<String> future1 = guavaExecutor.submit(() -> {
            Thread.sleep(100);
            //step 1
            System.out.println("执行step 1");
            return "step1 result";
        });
        ListenableFuture<String> future2 = guavaExecutor.submit(() -> {
            //step 2
            System.out.println("执行step 2");
            return "step2 result";
        });
        ListenableFuture<List<String>> future1And2 = Futures.allAsList(future1, future2);
        Futures.addCallback(future1And2, new FutureCallback<List<String>>() {
            @Override
            public void onSuccess(List<String> result) {
                System.out.println(result);
                ListenableFuture<String> future3 = guavaExecutor.submit(() -> {
                    System.out.println("执行step 3");
                    return "step3 result";
                });
                Futures.addCallback(future3, new FutureCallback<String>() {
                    @Override
                    public void onSuccess(String result) {
                        System.out.println(result);
                    }

                    @Override
                    public void onFailure(Throwable t) {
                    }
                }, guavaExecutor);
            }

            @Override
            public void onFailure(Throwable t) {
            }
        }, guavaExecutor);
        Thread.sleep(10000);
    }

    @Test
    public void testCompletable() {
        ExecutorService executor = Executors.newFixedThreadPool(5);
        CompletableFuture<String> cf1 = CompletableFuture.supplyAsync(() -> {
            System.out.println("执行step 1");
            return "step1 result";
        }, executor);
        CompletableFuture<String> cf2 = CompletableFuture.supplyAsync(() -> {
            System.out.println("执行step 2");
            return "step2 result";
        });
        cf1.thenCombine(cf2, (result1, result2) -> {
            System.out.println(result1 + " , " + result2);
            System.out.println("执行step 3");
            return "step3 result";
        }).thenAccept(result3 -> System.out.println(result3));
    }


    @Test
    public void test() {
        String str = "10.157.86.85, 10.159.51.119, 10.156.251.16, 10.159.186.93, 10.158.131.47, 10.159.251.69, 10.159.220.56, 10.158.152.40, 10.159.181.164, 10.159.74.117, 10.158.230.13, 10.156.166.88, 10.158.120.100, 10.159.137.123, 10.159.242.87, 10.156.133.69, 10.156.33.246, 10.158.147.91, 10.159.160.248, 10.156.174.102, 10.157.31.251, 10.159.172.71, 10.158.47.123, 10.158.157.14, 10.159.34.153, 10.157.81.203, 10.159.134.30, 10.158.161.236, 10.158.74.216, 10.156.144.191, 10.159.83.54, 10.158.228.31, 10.156.58.98, 10.158.213.251, 10.159.108.4, 10.156.74.217, 10.158.139.47, 10.158.78.21, 10.52.77.106, 10.52.89.12, 10.157.59.54, 10.52.42.40, 10.52.80.105, 10.52.81.76, 10.158.226.5, 10.52.83.21, 10.159.165.175, 10.52.78.35, 10.52.70.154, 10.158.62.192, 10.52.2.56, 10.52.83.217, 10.52.71.92, 10.52.70.12, 10.52.79.31, 10.52.80.61, 10.52.73.218, 10.159.218.16, 10.52.73.70, 10.52.81.170, 10.52.68.182, 10.52.3.172, 10.52.73.25, 10.52.89.189, 10.52.72.100, 10.52.70.227, 10.52.40.25, 10.52.78.51, 10.157.52.198, 10.52.69.202, 10.52.84.110, 10.52.70.174, 10.52.73.63, 10.52.80.206, 10.52.67.74, 10.52.84.194, 10.52.3.101, 10.52.76.19, 10.52.66.50, 10.158.231.71, 10.52.66.213, 10.52.77.50, 10.52.64.78, 10.52.3.61, 10.52.72.87, 10.52.0.174, 10.52.80.144, 10.52.75.213, 10.52.90.69, 10.52.2.212, 10.159.254.178, 10.52.49.223, 10.52.82.57, 10.52.4.35, 10.52.67.117, 10.52.66.218, 10.52.70.99, 10.52.71.86, 10.52.72.5, 10.52.87.103, 10.52.68.121, 10.159.142.151, 10.52.81.74, 10.52.83.97, 10.52.69.7, 10.52.83.247, 10.52.79.178, 10.52.77.51, 10.52.70.239, 10.52.72.199, 10.52.1.181, 10.52.83.201, 10.157.70.150, 10.157.50.154, 10.52.84.55, 10.156.157.69, 10.52.80.185, 10.52.71.9, 10.52.83.196, 10.52.67.199, 10.52.72.49, 10.52.66.170, 10.52.75.189, 10.52.73.109, 10.158.143.229, 10.52.77.11, 10.52.83.120, 10.52.3.188, 10.52.77.151, 10.156.135.235, 10.52.66.1, 10.159.105.191, 10.159.51.124, 10.157.86.88, 10.159.113.115, 10.158.195.142, 10.157.70.153, 10.159.254.180, 10.159.137.126, 10.157.16.37, 10.159.169.38, 10.158.195.144, 10.157.52.200, 10.157.50.156, 10.52.80.176, 10.156.74.220, 10.52.89.134, 10.158.171.108, 10.156.187.244, 10.156.48.221, 10.159.165.180, 10.158.143.231, 10.159.34.155, 10.159.183.77, 10.156.251.21, 10.158.81.220, 10.156.122.48, 10.159.23.246, 10.156.21.176, 10.156.116.137, 10.158.104.20, 10.156.43.223, 10.159.142.153, 10.159.23.249, 10.159.15.60, 10.159.56.224, 10.158.231.73, 10.159.112.172, 10.157.168.159, 10.156.77.32, 10.158.134.173, 10.156.83.184, 10.157.231.112, 10.159.141.162, 10.159.6.162, 10.159.186.94, 10.159.220.57, 10.159.147.235, 10.158.118.71, 10.156.122.46, 10.159.98.34, 10.159.8.85, 10.159.226.114, 10.157.36.196, 10.156.231.219, 10.157.168.162, 10.159.100.83, 10.158.251.223, 10.159.0.94, 10.156.91.171, 10.159.8.83, 10.159.47.163, 10.156.37.130, 10.52.72.47, 10.159.134.32, 10.156.174.103, 10.159.227.87, 10.158.147.93, 10.156.64.113, 10.157.57.20, 10.158.146.42, 10.156.116.136, 10.156.144.192, 10.158.187.74, 10.52.40.215, 10.159.162.5, 10.159.150.201, 10.158.215.147, 10.159.158.216, 10.158.77.201, 10.158.195.90, 10.157.156.56, 10.159.169.36, 10.159.110.165, 10.158.216.178, 10.159.190.145, 10.157.34.232, 10.159.74.118, 10.158.167.87, 10.156.135.40, 10.156.20.50, 10.158.163.51, 10.159.34.95, 10.52.80.120, 10.158.171.105, 10.159.246.164, 10.156.3.115, 10.158.160.212, 10.158.16.19, 10.156.114.214, 10.158.227.141, 10.159.9.50, 10.157.97.5, 10.156.182.167, 10.158.131.48, 10.159.98.33, 10.157.30.166, 10.157.8.5, 10.158.89.7, 10.158.101.182, 10.52.78.108, 10.159.251.71, 10.159.181.165, 10.159.234.89, 10.156.188.16, 10.156.133.72, 10.159.147.230, 10.156.34.190, 10.158.230.15, 10.159.105.28, 10.159.52.246, 10.158.223.29, 10.156.9.11, 10.159.135.84, 10.159.173.125, 10.157.31.252, 10.158.152.42, 10.159.6.161, 10.158.47.124, 10.159.172.72, 10.156.166.89, 10.157.81.204, 10.159.83.55, 10.159.242.88, 10.156.33.247, 10.158.78.30, 10.158.120.101, 10.159.160.250, 10.158.81.219, 10.158.157.15, 10.158.161.237, 10.158.228.33, 10.158.74.218, 10.156.58.99, 10.158.213.252, 10.159.108.5, 10.158.139.48, 10.158.62.193, 10.52.71.166, 10.158.104.18, 10.157.59.56, 10.158.226.8, 10.52.70.51, 10.52.72.231, 10.52.69.1, 10.52.67.113, 10.52.83.87, 10.52.84.48, 10.52.69.225, 10.52.84.180, 10.159.112.170, 10.52.65.205, 10.52.72.194, 10.52.84.191, 10.52.80.94, 10.52.69.29, 10.52.83.205, 10.52.81.33, 10.52.77.87, 10.52.71.218, 10.52.80.243, 10.157.156.54, 10.52.66.198, 10.52.82.1, 10.52.67.108, 10.52.72.222, 10.52.75.232, 10.52.80.203, 10.52.1.2, 10.52.71.248, 10.52.62.32, 10.52.72.110, 10.159.47.162, 10.52.71.103, 10.52.76.230, 10.52.70.252, 10.52.75.210, 10.52.67.41, 10.52.84.190, 10.52.76.251, 10.52.2.131, 10.52.66.215, 10.52.5.141, 10.158.118.70, 10.52.83.211, 10.52.75.238, 10.52.84.86, 10.52.73.186, 10.52.3.252, 10.52.4.248, 10.52.72.237, 10.52.1.163, 10.52.66.147, 10.52.1.1, 10.156.187.241, 10.156.21.175, 10.156.48.216, 10.159.226.112, 10.157.8.3, 10.159.141.161, 10.159.110.164, 10.159.100.82, 10.156.43.222, 10.159.150.200, 10.158.195.89, 10.156.135.232, 10.159.190.144, 10.156.135.39, 10.159.162.4, 10.156.231.217, 10.156.83.183, 10.158.187.72, 10.52.66.195, 10.156.188.15, 10.158.134.172, 10.158.215.146, 10.156.77.30, 10.158.167.86, 10.158.77.200, 10.158.146.40, 10.156.91.170, 10.157.231.111, 10.159.15.59, 10.159.227.86, 10.157.36.195, 10.159.56.223, 10.157.97.126, 10.159.105.189, 10.157.34.231, 10.159.183.76, 10.158.163.50, 10.157.57.15, 10.157.30.165, 10.159.246.162, 10.158.227.140, 10.159.0.93, 10.158.89.126, 10.156.3.114, 10.159.113.113, 10.156.64.111, 10.158.16.11, 10.159.52.242, 10.159.158.215, 10.159.234.88, 10.159.135.83, 10.156.182.166, 10.156.9.2, 10.156.37.253, 10.159.9.49, 10.157.16.35, 10.159.105.25, 10.158.223.28, 10.156.34.189, 10.159.34.91, 10.158.216.176, 10.158.251.222, 10.159.173.124, 10.156.20.47, 10.158.160.210, 10.156.114.213";
        String[] split = str.split(",");
        String newStr = "";
        for (String s : split) {
            String temp = "\"" + s + "\"";
            newStr += temp + ",";
        }
        log.info("result:{}", newStr);
    }
}
