package com.xiaomi.mone.log.manager;

import java.io.*;
import java.nio.channels.FileChannel;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/8/29 16:53
 */
public class FileUtilTest {
    public static void main(String[] args) {
        String[] filePaths = {
                "file1.txt", "file2.txt", "file3.txt", "file4.txt", "file5.txt",
                "file6.txt", "file7.txt", "file8.txt", "file9.txt", "file10.txt"
        };

        ExecutorService executor = Executors.newFixedThreadPool(10);
        for (String filePath : filePaths) {
            executor.submit(() -> writeFile("/home/<USER>/log/test/" + filePath));
        }
        executor.shutdown();
    }

    private static void writeFile(String filePath) {
        File file = new File(filePath);
        File progressFile = new File(filePath + ".progress");
        long startPosition = 0;

        // 读取进度文件，确定开始写入的位置
        if (progressFile.exists()) {
            try (BufferedReader reader = new BufferedReader(new FileReader(progressFile))) {
                startPosition = Long.parseLong(reader.readLine());
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        try (RandomAccessFile raf = new RandomAccessFile(file, "rw");
             FileChannel channel = raf.getChannel();
             BufferedWriter progressWriter = new BufferedWriter(new FileWriter(progressFile, false))) {

            raf.seek(startPosition);
            String content = "";
            for (long i = 0; i < 100; i++) {
                content += "Line " + i + "\n";

            }
            raf.write(content.getBytes());
            // 更新进度文件
            progressWriter.write(raf.getFilePointer() + "");
            progressWriter.newLine();
            progressWriter.flush();

        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
