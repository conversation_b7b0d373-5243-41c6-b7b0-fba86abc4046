package com.xiaomi.mone.log.manager.service;

import com.google.gson.Gson;
import com.xiaomi.mione.tesla.k8s.bo.LogAgentListBo;
import com.xiaomi.mone.cache.redis.RedisClientFactory;
import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.api.model.meta.LogCollectMeta;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

import static com.xiaomi.mone.log.manager.common.InnerManagerConstant.INNER_LOG_AGENT_SERVICE;
import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/5/5 21:59
 */
@Slf4j
public class InnerLogAgentServiceTest {

    private InnerLogAgentService agentConfigService;

    @Before
    public void buildBean() {
        getConfigFromNanos();
        RedisClientFactory.init();
        Ioc.ins().init("com.xiaomi.mone", "com.xiaomi.youpin", "org.apache.ozhera.log.manager");
        agentConfigService = Ioc.ins().getBean(INNER_LOG_AGENT_SERVICE);
    }

    @Test
    public void test() {
        LogCollectMeta logCollectMeta = agentConfigService.getLogCollectMetaFromManager("*************:1");
        String responseInfo = new Gson().toJson(logCollectMeta);
        log.info("agent启动获取配置,获取到的配置信息:{}", responseInfo);
    }


    @Test
    public void testGenerateK8sLikeLogPath() {
        List<LogAgentListBo> podList = new ArrayList<>();
        List<LogAgentListBo> empty = new ArrayList<>();
        LogAgentListBo bo = new LogAgentListBo();
        bo.setPodIP("127.0.0.1");
        bo.setPodName("pod_name");
        podList.add(bo);
        LogAgentListBo bo1 = new LogAgentListBo();
        bo1.setPodIP("*********");
        bo1.setPodName("pod_name2");
        podList.add(bo1);
        String logdir = agentConfigService.generateK8sLikeLogPath("", empty, "/home/<USER>/log/app.log");
        log.info("测试生产日志采集路径:{}", logdir);
        String logdir1 = agentConfigService.generateK8sLikeLogPath("namespace", podList, "/home/<USER>/log/app.log");
        log.info("测试生产日志采集路径:{}", logdir1);
        String logdir2 = agentConfigService.generateK8sLikeLogPath(null, podList, "/home/<USER>/log/app.log");
        log.info("测试生产日志采集路径:{}", logdir2);
    }
}
