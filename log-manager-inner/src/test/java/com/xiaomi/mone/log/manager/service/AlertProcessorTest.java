package com.xiaomi.mone.log.manager.service;

import cn.hutool.json.JSONUtil;
import com.google.gson.Gson;
import com.xiaomi.mone.cache.redis.RedisClientFactory;
import com.xiaomi.mone.log.manager.model.alert.Alert;
import com.xiaomi.mone.log.manager.model.alert.AlertCondition;
import com.xiaomi.mone.log.manager.model.alert.AlertGroup;
import com.xiaomi.mone.log.manager.model.bo.alert.AlarmTailRelationship;
import com.xiaomi.mone.log.manager.service.alert.*;
import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ozhera.log.api.model.bo.AlertInfo;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.dao.MilogLogstoreDao;
import org.apache.ozhera.log.manager.dao.MilogSpaceDao;
import org.apache.ozhera.log.manager.model.pojo.MilogLogStoreDO;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;
import org.apache.ozhera.log.manager.model.pojo.MilogSpaceDO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import redis.clients.jedis.JedisCluster;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

import static org.apache.ozhera.log.common.Constant.TAILID_KEY;
import static org.apache.ozhera.log.common.Constant.TRACE_ID_KEY;
import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/4/22 15:25
 */
@Slf4j
public class AlertProcessorTest {

    public Gson gson = new Gson();

    private AlertProcessor alertProcessor;
    private MilogLogTailDao milogLogtailDao;
    private MilogLogstoreDao milogLogstoreDao;
    private MilogSpaceDao milogSpaceDao;
    private FeishuService feishuService;
    private AlertService alertService;
    private JedisCluster jedisCluster;

    @Before
    public void init() {
        getConfigFromNanos();
        RedisClientFactory.init();
        Ioc.ins().init("com.xiaomi.mone", "com.xiaomi.youpin", "org.apache.ozhera.log.manager");

        alertProcessor = Ioc.ins().getBean(AlertProcessor.class);
        milogLogtailDao = Ioc.ins().getBean(MilogLogTailDao.class);
        milogLogstoreDao = Ioc.ins().getBean(MilogLogstoreDao.class);
        milogSpaceDao = Ioc.ins().getBean(MilogSpaceDao.class);
        feishuService = Ioc.ins().getBean(FeishuService.class);
        alertService = Ioc.ins().getBean(AlertService.class);
        jedisCluster = RedisClientFactory.getJedisCluster();
    }

    @Test
    public void sendCardMessageTest() throws IOException, ExecutionException {

        // String message =
        // "{\"alertId\":93,\"ruleId\":151,\"info\":{\"timeStamp\":\"1650612660333\",\"regex\":\".*(?i)error.*\",\"log\":\"2022-04-22
        // 15:30:55,008|ERROR||com.alibaba.nacos.client.naming.updater|c.a.nacos.client.security.SecurityProxy|154|[SecurityProxy]
        // login http request failed url:
        // http://nacos.test.b2c.srv:80/nacos/v1/auth/users/login, params:
        // {username\\u003dnacos}, bodyMap: {password\\u003dnacos}, errorMsg: errCode:
        // 100, errMsg: Nacos serialize for class
        // [com.alibaba.nacos.common.http.HttpRestResult] failed.
        // \",\"ip\":\"*************\",\"count\":\"12\"}}";
        // String content =
        // "{\"alertId\":90003,\"ruleId\":90029,\"info\":{\"timeStamp\":\"1671076382506\",\"regex\":\"Call
        // server
        // done\",\"fileName\":\"/home/<USER>/log/trace-demo-client/app.log\",\"log\":\"2022-12-15T11:52:51.251+0800\\tINFO\\ttrace-demo-client/main.go:37\\t[traceId\\u003db24e35ed31cb51e5516e25453228f36d]Call
        // server
        // done.\",\"ip\":\"************\",\"count\":\"12\",\"tag\":\"tags_393_90074_90159\",\"lineNumber\":\"3889\"}}";
        String content = "{\"alertId\":94102,\"ruleId\":98362,\"info\":{\"timeStamp\":\"1748210458692\",\"regex\":\"[{\\\"tailId\\\":924811,\\\"matchContentRule\\\":[{\\\"expressions\\\":[\\\"message\\\",\\\"7\\\",\\\"失败\\\"],\\\"operator\\\":2},{\\\"expressions\\\":[\\\"message\\\",\\\"7\\\",\\\"为空\\\"],\\\"operator\\\":2},{\\\"expressions\\\":[\\\"message\\\",\\\"7\\\",\\\"ERROR\\\"]}]}]\",\"fileName\":\"/home/<USER>/log/material-worker-433404-material-worker-video-offline1-public/material-worker-video-offline1-public-d6g9n/log/all.log\",\"log\":\"2025-05-26 06:00:58 - ERROR - video_handler - 456 - operator 任务结果反馈: car/eng/高精地图视频文件/bhd0023/2025-5-25 数据为空，跳过处理\",\"ip\":\"**************\",\"count\":\"44\",\"tag\":\"tags_312739_121869_924811\",\"lineNumber\":\"344\"}}";
        alertProcessor.handleAlertMsg(content);
        // System.in.read();
        AlertInfo alertInfo = gson.fromJson(content, AlertInfo.class);
        Alert alert = alertService.getAlert(alertInfo.getAlertId());
        String tailId = alert.getArgument(TAILID_KEY);
        MilogLogTailDo milogLogtailDo = milogLogtailDao.queryById(Long.valueOf(tailId));
        MilogLogStoreDO logStoreDO = milogLogstoreDao.queryById(milogLogtailDo.getStoreId());
        MilogSpaceDO spaceDO = milogSpaceDao.queryById(milogLogtailDo.getSpaceId());
        Map<String, Object> analysisLogMap = alertProcessor.analysisLog(alertInfo, milogLogtailDo, logStoreDO);
        String logParam = alertProcessor.generateLogParam(spaceDO.getId(), logStoreDO,
                milogLogtailDo.getTail(), analysisLogMap, alertInfo);
        int count = 10;
        String traceId = analysisLogMap.getOrDefault(TRACE_ID_KEY, "").toString();
        String heraUrl = "http://hera.be.test.mi.com";
        String serverType = "local";
        String ruleRegex = "/.*\"EW10001*^()+?{\\\\}|'~/";
        ruleRegex = ruleRegex.replaceAll("\\\\", "\\\\\\\\\\\\\\\\").replaceAll("\"", "\\\\\"");
        String ruleName = "upstream_http_custom_status报警策略P0";
        String timeStamp = "";
        String message = "[2022-12-13 11:55:59] [xmstore_api] [api-pro-srv-stable-68f447f9cf-cv97k] [INFO] [314759342825] {\"message\":\"GRPC-XMSTORE-COMMON :  Services\\\\Organization\\\\Rpc\\\\OrganizationCacheService->GetOrgCache : result \",\"result\":{\"response\":\"{\\\"data\\\":{\\\"id\\\":\\\"SQ57099\\\",\\\"siteId\\\":\\\"5\\\",\\\"mihomeId\\\":\\\"34522\\\",\\\"thirdId\\\":\\\"57099\\\",\\\"type\\\":\\\"3\\\",\\\"name\\\":\\\"小米之家河南郑州登封市万佳中心城授权店\\\",\\\"areaId\\\":\\\"618\\\",\\\"address\\\":\\\"河南,郑州市,登封市,嵩阳街道,河南,郑州市,登封市,嵩阳街道,中岳大街大禹城\\\",\\\"country\\\":\\\"1\\\",\\\"province\\\":\\\"17\\\",\\\"city\\\":\\\"187\\\",\\\"district\\\":\\\"1816\\\",\\\"street\\\":\\\"1816010\\\",\\\"companyId\\\":\\\"10935\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"shortName\\\":\\\"小米之家河南郑州登封市万佳中心城授权店\\\",\\\"orgCode\\\":\\\"JMD293805\\\",\\\"tel\\\":\\\"0371-62860789\\\"}}\",\"status\":{\"metadata\":[],\"code\":0,\"details\":\"\"},\"cost\":0.0022351741790771484375},\"extra\":{\"uri\":\"\\/order\\/status\",\"method\":\"POST\",\"query\":\"\",\"cookie\":\"\",\"usersAddr\":\"*************\",\"frontendAddr\":\"************\",\"referer\":\"\",\"function\":\"Services\\\\Organization\\\\Rpc\\\\OrganizationCacheService::GetOrgCache\",\"line\":3123,\"file\":\"\\/home\\/<USER>\\/data\\/www\\/xmstore-api\\/app\\/biz\\/Organization.php\",\"userAgent\":\"Mozilla\\/5.0 (Windows NT 10.0; WOW64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/75.0.3770.100 Safari\\/537.36\",\"timeUsed\":56,\"requestId\":\"6abc84676a6e7b9f0bdcfac2e7a303f8\"}}";
        List<AlertCondition> alertConditions = alertService.getAllConditions(alertInfo.getRuleId());
        AlertCondition alertCondition = alertConditions.get(0);

        RemoteSendAlertLink.Meta meta = AlertMessageBuilder.buildEventMeta(count, alert, alertInfo.getRuleId(), traceId,
                heraUrl, alertCondition, serverType, ruleRegex, ruleName, timeStamp, logParam, message);
        // InputStream inputStream = ClassLoaderUtil.getClassLoader().getResourceAsStream("alertCard.json");
        // String template = IoUtil.readUtf8(inputStream);
        // String result = String.format(template,
        //         "P1", meta.getRuleName(), meta.getAppName(), meta.getAlertTime(), meta.getWindowSize(),
        //         meta.getRuleRegex(), meta.getOperation(),
        //         meta.getThreshold(), meta.getMessage(), meta.getEnv(), meta.getTraceId(), meta.getMessagePrefix(),
        //         heraUrl, traceId.trim(), heraUrl, logParam);
        // feishuService.sendFeishu(result, splitString(alert.getContacts()), splitString(alert.getFeishuGroups()), true);

        // log.info("result:{}", result);
        String[] receivers = splitString(alert.getContacts());
        String[] feishuGroups = splitString(alert.getFeishuGroups());
        String[] notifyGroups = extractNames(alert.getAlertGroups());
        SendAlertLinkFactory
                .getSendAlertLink(alert.getId(), alertCondition.getAlertLevel(), meta, receivers, feishuGroups,
                        notifyGroups,
                        alert.getCallbackUrl(), null)
                .doSend();

    }

    private String[] splitString(String S) {
        if (StringUtils.isEmpty(S)) {
            return null;
        }
        return S.split("[,]");
    }

    private String[] extractNames(List<AlertGroup> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new String[]{};
        }
        String[] names = new String[list.size()];
        for (int i = 0; i < list.size(); i++) {
            names[i] = list.get(i).getName();
        }
        return names;
    }

    @Test
    public void quote() {
        String raw = "2023-07-28 15:16:44.363 [dk-search-service-c3] [10.158.124.58] ERROR [1.perfcounter] [com.xiaomi.common.perfcounter.PerfCounterPushTask#run:41] [] Failed to push perf counter data to 127.0.0.1:1988, result:['msg':'Metric char('/') in \\'hbase/duokan:miui_yuedu_content_stats\\' has been replaced with (':'), show first warning only.']";
        String quoteFalse = JSONUtil.quote(raw, false);
        String quote = JSONUtil.quote(raw, true);
        log.info("raw:{}", raw);
        log.info("quoteFalse:{}", quoteFalse);
        log.info("quote:{}", quote);

    }

    @Test
    public void testRegexMatchTimestamp() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        String log1 = "[ERROR] 2024-09-25 09:09:14.740 [3bc402763f0c51dbab05fdf8fa39e33a] [0a7e6d8b17272265517359318] [http-nio-8091-exec-4][] [com.xiaomi.assistant.http.client.api.handler.AbstractHttpHandler.exceptionHandler(AbstractHttpHandler.java:206)] [com.xiaomi.assistant.http.client.okhttp.handler.OkHttpPostFormHandler execute error,req\u003d{\"url\":\"https://api.xmpush.xiaomi.com/v2/message/user_account\",\"headers\":{\"Authorization\":\"key\u003dEloIjKI7cwCkphjQ1sHIeg\u003d\u003d\"},\"params\":{\"restricted_package_name\":\"com.miui.personalassistant\",\"user_account\":\"2.0:h2/8JQHaDls3b/9CsZs3/jVaeHc\u003d\",\"extra.is_system_app_check\":true,\"payload\":\"{\\\"cmd\\\":\\\"travel\\\",\\\"data\\\":{\\\"cpCode\\\":\\\"ccrgt\\\",\\\"openId\\\":\\\"2.0:h2/8JQHaDls3b/9CsZs3/jVaeHc\u003d\\\",\\\"action\\\":11},\\\"topic\\\":\\\"online_schedule\\\"}\",\"pass_through\":1},\"groupKey\":\"travel\",\"businessKey\":\"schedule\",\"httpConfig\":{\"retryTimes\":2,\"connectTimeout\":2000,\"readTimeout\":3000,\"writeTimeout\":3000,\"maxIdleConnections\":5,\"keepAliveDuration\":5},\"isolationConnectionPool\":false},respCodeMsg.code\u003d1005,resCodeMsg.message\u003d请求超时异常]\r\njava.net.SocketTimeoutException: timeout\r\n\tat okhttp3.internal.http2.Http2Stream$StreamTimeout.newTimeoutException(Http2Stream.kt:675) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http2.Http2Stream$StreamTimeout.exitAndThrowIfTimedOut(Http2Stream.kt:684) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http2.Http2Stream.takeHeaders(Http2Stream.kt:143) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http2.Http2ExchangeCodec.readResponseHeaders(Http2ExchangeCodec.kt:96) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.kt:106) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.kt:79) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:34) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.9.3.jar!/:?]\r\n\tat io.opentelemetry.javaagent.shaded.instrumentation.okhttp.v3_0.TracingInterceptor.intercept(TracingInterceptor.java:44) ~[opentelemetry-javaagent-all.jar:?]\r\n\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.connection.RealCall.execute(RealCall.kt:154) ~[okhttp-4.9.3.jar!/:?]\r\n\tat com.xiaomi.assistant.http.client.okhttp.handler.OkHttpHandler.sendRequest(OkHttpHandler.java:160) ~[http-client-okhttp-1.3.5.jar!/:1.3.5]\r\n\tat com.xiaomi.assistant.http.client.api.handler.AbstractHttpHandler.execute(AbstractHttpHandler.java:173) ~[http-client-api-1.3.5.jar!/:1.3.5]\r\n\tat com.xiaomi.assistant.http.client.manager.MiHttpClientManager.postForm(MiHttpClientManager.java:189) ~[http-client-okhttp-1.3.5.jar!/:1.3.5]\r\n\tat com.xiaomi.assistant.http.client.manager.MiHttpClientManager.postForm(MiHttpClientManager.java:195) ~[http-client-okhttp-1.3.5.jar!/:1.3.5]\r\n\tat com.xiaomi.xpa.push.service.PushService.push(PushService.java:138) ~[xpa-push-3.8.2.jar!/:?]\r\n\tat com.xiaomi.xpa.push.service.PushService.pushByAccount(PushService.java:178) ~[xpa-push-3.8.2.jar!/:?]\r\n\tat com.xiaomi.cpa.travel.app.service.PushScheduleService.sendPushByAccount(PushScheduleService.java:162) ~[cpa-travel-application-1.0.1-SNAPSHOT.jar!/:?]\r\n\tat com.xiaomi.cpa.travel.app.service.PushScheduleService.push(PushScheduleService.java:146) ~[cpa-travel-application-1.0.1-SNAPSHOT.jar!/:?]\r\n\tat com.xiaomi.cpa.travel.app.service.PushScheduleService.push(PushScheduleService.java:110) ~[cpa-travel-application-1.0.1-SNAPSHOT.jar!/:?]\r\n\tat com.xiaomi.cpa.travel.partner.api.controller.PartnerController.accountBind(PartnerController.java:53) ~[classes!/:?]\r\n\tat com.xiaomi.cpa.travel.partner.api.controller.PartnerController$$FastClassBySpringCGLIB$$40985f20.invoke(\u003cgenerated\u003e) ~[classes!/:?]\r\n\tat org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat com.xiaomi.xpa.tool.interceptor.advice.ControllerAdvice.invoke(ControllerAdvice.java:63) ~[xpa-interceptor-starter-3.8.2.jar!/:?]\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat com.xiaomi.cpa.travel.partner.api.aop.TraceIdAopHandler.addTraceId(TraceIdAopHandler.java:22) ~[classes!/:?]\r\n\tat jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]\r\n\tat java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]\r\n\tat org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat com.xiaomi.cpa.travel.partner.api.controller.PartnerController$$EnhancerBySpringCGLIB$$d186d4d1.accountBind(\u003cgenerated\u003e) ~[classes!/:?]\r\n\tat jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]\r\n\tat java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]\r\n\tat org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar!/:5.3.31]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar!/:5.3.31]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.springframework.web.servlet.v3_1.OpenTelemetryHandlerMappingFilter.doFilter(OpenTelemetryHandlerMappingFilter.java:83) ~[opentelemetry-javaagent-all.jar:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar!/:5.3.31]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.valves.RemoteIpValve.invoke(RemoteIpValve.java:765) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat java.lang.Thread.run(Thread.java:1583) ~[?:?]";
        Method method = AlertProcessor.class.getDeclaredMethod("regexMatchTimestamp", String.class);
        method.setAccessible(true);
        Long timestamp = (Long) method.invoke(alertProcessor, log1);
        Assert.assertEquals(Long.valueOf(1727226554000L), timestamp);
    }

    @Test
    public void testGetLogHash() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        String log1 = "[ERROR] 2024-09-25 09:09:14.740 [3bc402763f0c51dbab05fdf8fa39e33a] [0a7e6d8b17272265517359318] [http-nio-8091-exec-4][] [com.xiaomi.assistant.http.client.api.handler.AbstractHttpHandler.exceptionHandler(AbstractHttpHandler.java:206)] [com.xiaomi.assistant.http.client.okhttp.handler.OkHttpPostFormHandler execute error,req\u003d{\"url\":\"https://api.xmpush.xiaomi.com/v2/message/user_account\",\"headers\":{\"Authorization\":\"key\u003dEloIjKI7cwCkphjQ1sHIeg\u003d\u003d\"},\"params\":{\"restricted_package_name\":\"com.miui.personalassistant\",\"user_account\":\"2.0:h2/8JQHaDls3b/9CsZs3/jVaeHc\u003d\",\"extra.is_system_app_check\":true,\"payload\":\"{\\\"cmd\\\":\\\"travel\\\",\\\"data\\\":{\\\"cpCode\\\":\\\"ccrgt\\\",\\\"openId\\\":\\\"2.0:h2/8JQHaDls3b/9CsZs3/jVaeHc\u003d\\\",\\\"action\\\":11},\\\"topic\\\":\\\"online_schedule\\\"}\",\"pass_through\":1},\"groupKey\":\"travel\",\"businessKey\":\"schedule\",\"httpConfig\":{\"retryTimes\":2,\"connectTimeout\":2000,\"readTimeout\":3000,\"writeTimeout\":3000,\"maxIdleConnections\":5,\"keepAliveDuration\":5},\"isolationConnectionPool\":false},respCodeMsg.code\u003d1005,resCodeMsg.message\u003d请求超时异常]\r\njava.net.SocketTimeoutException: timeout\r\n\tat okhttp3.internal.http2.Http2Stream$StreamTimeout.newTimeoutException(Http2Stream.kt:675) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http2.Http2Stream$StreamTimeout.exitAndThrowIfTimedOut(Http2Stream.kt:684) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http2.Http2Stream.takeHeaders(Http2Stream.kt:143) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http2.Http2ExchangeCodec.readResponseHeaders(Http2ExchangeCodec.kt:96) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.kt:106) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.kt:79) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:34) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.9.3.jar!/:?]\r\n\tat io.opentelemetry.javaagent.shaded.instrumentation.okhttp.v3_0.TracingInterceptor.intercept(TracingInterceptor.java:44) ~[opentelemetry-javaagent-all.jar:?]\r\n\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.connection.RealCall.execute(RealCall.kt:154) ~[okhttp-4.9.3.jar!/:?]\r\n\tat com.xiaomi.assistant.http.client.okhttp.handler.OkHttpHandler.sendRequest(OkHttpHandler.java:160) ~[http-client-okhttp-1.3.5.jar!/:1.3.5]\r\n\tat com.xiaomi.assistant.http.client.api.handler.AbstractHttpHandler.execute(AbstractHttpHandler.java:173) ~[http-client-api-1.3.5.jar!/:1.3.5]\r\n\tat com.xiaomi.assistant.http.client.manager.MiHttpClientManager.postForm(MiHttpClientManager.java:189) ~[http-client-okhttp-1.3.5.jar!/:1.3.5]\r\n\tat com.xiaomi.assistant.http.client.manager.MiHttpClientManager.postForm(MiHttpClientManager.java:195) ~[http-client-okhttp-1.3.5.jar!/:1.3.5]\r\n\tat com.xiaomi.xpa.push.service.PushService.push(PushService.java:138) ~[xpa-push-3.8.2.jar!/:?]\r\n\tat com.xiaomi.xpa.push.service.PushService.pushByAccount(PushService.java:178) ~[xpa-push-3.8.2.jar!/:?]\r\n\tat com.xiaomi.cpa.travel.app.service.PushScheduleService.sendPushByAccount(PushScheduleService.java:162) ~[cpa-travel-application-1.0.1-SNAPSHOT.jar!/:?]\r\n\tat com.xiaomi.cpa.travel.app.service.PushScheduleService.push(PushScheduleService.java:146) ~[cpa-travel-application-1.0.1-SNAPSHOT.jar!/:?]\r\n\tat com.xiaomi.cpa.travel.app.service.PushScheduleService.push(PushScheduleService.java:110) ~[cpa-travel-application-1.0.1-SNAPSHOT.jar!/:?]\r\n\tat com.xiaomi.cpa.travel.partner.api.controller.PartnerController.accountBind(PartnerController.java:53) ~[classes!/:?]\r\n\tat com.xiaomi.cpa.travel.partner.api.controller.PartnerController$$FastClassBySpringCGLIB$$40985f20.invoke(\u003cgenerated\u003e) ~[classes!/:?]\r\n\tat org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat com.xiaomi.xpa.tool.interceptor.advice.ControllerAdvice.invoke(ControllerAdvice.java:63) ~[xpa-interceptor-starter-3.8.2.jar!/:?]\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat com.xiaomi.cpa.travel.partner.api.aop.TraceIdAopHandler.addTraceId(TraceIdAopHandler.java:22) ~[classes!/:?]\r\n\tat jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]\r\n\tat java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]\r\n\tat org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat com.xiaomi.cpa.travel.partner.api.controller.PartnerController$$EnhancerBySpringCGLIB$$d186d4d1.accountBind(\u003cgenerated\u003e) ~[classes!/:?]\r\n\tat jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]\r\n\tat java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]\r\n\tat org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar!/:5.3.31]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar!/:5.3.31]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.springframework.web.servlet.v3_1.OpenTelemetryHandlerMappingFilter.doFilter(OpenTelemetryHandlerMappingFilter.java:83) ~[opentelemetry-javaagent-all.jar:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar!/:5.3.31]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.valves.RemoteIpValve.invoke(RemoteIpValve.java:765) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat java.lang.Thread.run(Thread.java:1583) ~[?:?]";
        String log2 = "[ERROR] 2024-09-25 09:15:15.185 [4f583ca3dde6afaa6ee38ef2caa21cfa] [0a7e6d8b172722691218152] [http-nio-8091-exec-2][] [com.xiaomi.assistant.http.client.api.handler.AbstractHttpHandler.exceptionHandler(AbstractHttpHandler.java:206)] [com.xiaomi.assistant.http.client.okhttp.handler.OkHttpPostFormHandler execute error,req\u003d{\"url\":\"https://api.xmpush.xiaomi.com/v3/message/alias\",\"headers\":{\"Authorization\":\"key\u003dEloIjKI7cwCkphjQ1sHIeg\u003d\u003d\"},\"params\":{\"alias\":\"a61f969b76ce0281\",\"restricted_package_name\":\"com.miui.personalassistant\",\"extra.is_system_app_check\":true,\"payload\":\"{\\\"cmd\\\":\\\"travel\\\",\\\"data\\\":{\\\"cpCode\\\":\\\"umetrip\\\",\\\"androidId\\\":\\\"a61f969b76ce0281\\\",\\\"actionTime\\\":1727226912109,\\\"action\\\":0},\\\"topic\\\":\\\"online_schedule\\\"}\",\"pass_through\":1},\"groupKey\":\"travel\",\"businessKey\":\"schedule\",\"httpConfig\":{\"retryTimes\":2,\"connectTimeout\":2000,\"readTimeout\":3000,\"writeTimeout\":3000,\"maxIdleConnections\":5,\"keepAliveDuration\":5},\"isolationConnectionPool\":false},respCodeMsg.code\u003d1005,resCodeMsg.message\u003d请求超时异常]\r\njava.net.SocketTimeoutException: timeout\r\n\tat okhttp3.internal.http2.Http2Stream$StreamTimeout.newTimeoutException(Http2Stream.kt:675) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http2.Http2Stream$StreamTimeout.exitAndThrowIfTimedOut(Http2Stream.kt:684) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http2.Http2Stream.takeHeaders(Http2Stream.kt:143) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http2.Http2ExchangeCodec.readResponseHeaders(Http2ExchangeCodec.kt:96) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.kt:106) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.kt:79) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:34) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.9.3.jar!/:?]\r\n\tat io.opentelemetry.javaagent.shaded.instrumentation.okhttp.v3_0.TracingInterceptor.intercept(TracingInterceptor.java:44) ~[opentelemetry-javaagent-all.jar:?]\r\n\tat okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.9.3.jar!/:?]\r\n\tat okhttp3.internal.connection.RealCall.execute(RealCall.kt:154) ~[okhttp-4.9.3.jar!/:?]\r\n\tat com.xiaomi.assistant.http.client.okhttp.handler.OkHttpHandler.sendRequest(OkHttpHandler.java:160) ~[http-client-okhttp-1.3.5.jar!/:1.3.5]\r\n\tat com.xiaomi.assistant.http.client.api.handler.AbstractHttpHandler.execute(AbstractHttpHandler.java:173) ~[http-client-api-1.3.5.jar!/:1.3.5]\r\n\tat com.xiaomi.assistant.http.client.manager.MiHttpClientManager.postForm(MiHttpClientManager.java:189) ~[http-client-okhttp-1.3.5.jar!/:1.3.5]\r\n\tat com.xiaomi.assistant.http.client.manager.MiHttpClientManager.postForm(MiHttpClientManager.java:195) ~[http-client-okhttp-1.3.5.jar!/:1.3.5]\r\n\tat com.xiaomi.xpa.push.service.PushService.push(PushService.java:138) ~[xpa-push-3.8.2.jar!/:?]\r\n\tat com.xiaomi.xpa.push.service.PushService.pushByAlias(PushService.java:191) ~[xpa-push-3.8.2.jar!/:?]\r\n\tat com.xiaomi.cpa.travel.app.service.PushScheduleService.sendPushByAlias(PushScheduleService.java:152) ~[cpa-travel-application-1.0.1-SNAPSHOT.jar!/:?]\r\n\tat com.xiaomi.cpa.travel.app.service.PushScheduleService.push(PushScheduleService.java:142) ~[cpa-travel-application-1.0.1-SNAPSHOT.jar!/:?]\r\n\tat com.xiaomi.cpa.travel.app.service.PushScheduleService.pushByUmetrip(PushScheduleService.java:80) ~[cpa-travel-application-1.0.1-SNAPSHOT.jar!/:?]\r\n\tat com.xiaomi.cpa.travel.partner.api.controller.PartnerController.schedulePush(PartnerController.java:118) ~[classes!/:?]\r\n\tat com.xiaomi.cpa.travel.partner.api.controller.PartnerController.schedulePush(PartnerController.java:96) ~[classes!/:?]\r\n\tat com.xiaomi.cpa.travel.partner.api.controller.PartnerController$$FastClassBySpringCGLIB$$40985f20.invoke(\u003cgenerated\u003e) ~[classes!/:?]\r\n\tat org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat com.xiaomi.xpa.tool.interceptor.advice.ControllerAdvice.invoke(ControllerAdvice.java:63) ~[xpa-interceptor-starter-3.8.2.jar!/:?]\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat com.xiaomi.cpa.travel.partner.api.aop.TraceIdAopHandler.addTraceId(TraceIdAopHandler.java:22) ~[classes!/:?]\r\n\tat jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]\r\n\tat java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]\r\n\tat org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.31.jar!/:5.3.31]\r\n\tat com.xiaomi.cpa.travel.partner.api.controller.PartnerController$$EnhancerBySpringCGLIB$$d186d4d1.schedulePush(\u003cgenerated\u003e) ~[classes!/:?]\r\n\tat jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]\r\n\tat java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]\r\n\tat org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.31.jar!/:5.3.31]\r\n\tat javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar!/:5.3.31]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar!/:5.3.31]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.springframework.web.servlet.v3_1.OpenTelemetryHandlerMappingFilter.doFilter(OpenTelemetryHandlerMappingFilter.java:83) ~[opentelemetry-javaagent-all.jar:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.31.jar!/:5.3.31]\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar!/:5.3.31]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.valves.RemoteIpValve.invoke(RemoteIpValve.java:765) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.83.jar!/:?]\r\n\tat java.lang.Thread.run(Thread.java:1583) ~[?:?]";
        Method method = AlertProcessor.class.getDeclaredMethod("getLogHash", String.class);
        method.setAccessible(true);
        String log1HashValue = (String) method.invoke(alertProcessor, log1);
        String log1HashValueDuplicate = (String) method.invoke(alertProcessor, log1);
        String log2HashValue = (String) method.invoke(alertProcessor, log2);
        String empty = (String) method.invoke(alertProcessor, "");
        log.info("emptyValue:{}", empty);
        log.info("log1Value:{}", log1HashValue);
        log.info("log1ValueDuplicate:{}", log1HashValueDuplicate);
        log.info("log2Value:{}", log2HashValue);
        Assert.assertEquals(empty, "d41d8cd98f00b204e9800998ecf8427e");
        Assert.assertEquals(log1HashValue, log1HashValueDuplicate);
        Assert.assertNotEquals(log1HashValue, log2HashValue);
    }

    @Test
    public void testScriptMatchTimestamp() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        AlertInfo alertInfo = new AlertInfo();
        alertInfo.setAlertId(90121L);
        alertInfo.setRuleId(90156L);
        HashMap<String, String> info = new HashMap<>();
        info.put("timeStamp", "1727343728776");
        info.put("regex", "TransferProvider.approve.bizException|TransferProvider.approve.exception");
        info.put("fileName", "/home/<USER>/log/mit-new-retail-pss-pss-process-center-960172-5c5fb454c-nqbpn/pss-process-center/pss-process-center.log");
        info.put("log", "\"2024-09-26 17:42:08,727|WARN |ffff2c33fe5e1115a7cab3736a015180|DubboServerHandler-*************:20880-thread-1497|c.x.m.p.p.c.a.p.i.TransferProviderImpl|357|approve|TransferProvider.approve.bizException: 调用库存网络返回错误信息: {业务异常{[库存网络]同步SOA库存异常{\\\"调拨修改SOA库存异常:[库存网络]同步SOA库存异常{\\\\\\\"调用SOA库存服务错误：errNo:【500】，errMsg【stock_network#TRANSFER_ADD#102451085#21671407#batch#a6482ed0a35ba19b6d078770c8769cd4, stock_network#TRANSFER_ADD#102451085#21671407#batch#a6482ed0a35ba19b6d078770c8769cd4, , transfer execute fail, stock not enough, need 100, exist sale -200 reserved 0, {52916 30431 1 3 2 CN-IN 100 0}】\\\\\\\"}\\\"}}}");
        info.put("ip", "*************");
        info.put("count", "18");
        info.put("tag", "tags_363_262_597");
        info.put("lineNumber", "134092");
        alertInfo.setInfo(info);
        Method method = AlertProcessor.class.getDeclaredMethod("scriptMatchTimestamp", AlertInfo.class, String.class, Long.class);
        method.setAccessible(true);
        Long timestamp = (Long) method.invoke(alertProcessor, alertInfo, alertInfo.getInfo("log"), Long.valueOf("1727339704173"));
        log.info("timestamp:{}", timestamp);
    }

    @Test
    public void testAlertExpire() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
//        AlertInfo alertInfo = new AlertInfo();
//        alertInfo.setAlertId(120053L);
//        alertInfo.setRuleId(120113L);
//        HashMap<String, String> info = new HashMap<>();
//        info.put("timeStamp", "1727339704173");
//        info.put("regex", "#ALERT#");
//        info.put("fileName", "/home/<USER>/log/isc-price-375228-isc-server-price-test/isc-server-price-test-s4p87/logs/isc-server-price/info.log");
//        info.put("log", "2024-09-26 16:35:04.103 isc-server-price-test-s4p87 ERROR [traceId\\u003d8161deea4c34c2fe2b2f126d486b1586] - [spanId\\u003dd507407bc6837332] c.x.m.p.s.x.a.impl.GetPriceServiceImpl - #ALERT#获取价格接口，SAP价格条件配置全部未找到：[{\\\"msg\\\":\\\"请求价格信息未集成到价格中心，请使用其他路径确定价格\\\",\\\"objectCode\\\":\\\"22BM1HA225AA\\\",\\\"code\\\":3007,\\\"priceType\\\":\\\"materialFee\\\",\\\"vendorCode\\\":\\\"0000101131\\\",\\\"queryDate\\\":\\\"20240926\\\",\\\"appid\\\":\\\"xm_srm\\\",\\\"purchaseOrg\\\":\\\"1110\\\"}]");
//        info.put("ip", "**************");
//        info.put("count", "2");
//        info.put("tag", "tags_186385_120318_120407");
//        info.put("lineNumber", "175537");
//        alertInfo.setInfo(info);
//        Method method = AlertProcessor.class.getDeclaredMethod("alertExpire", AlertInfo.class);
//        method.setAccessible(true);
//        Boolean expire = (Boolean) method.invoke(alertProcessor, alertInfo);
//        Assert.assertFalse(expire);
    }

    @Test
    public void testAlertAlreadySent() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        AlertInfo alertInfo = new AlertInfo();
        alertInfo.setAlertId(91715L);
        alertInfo.setRuleId(93630L);
        HashMap<String, String> info = new HashMap<>();
        info.put("timeStamp", "1727337540653");
        info.put("regex", "\\\"ResponseLevel\\\":\\\"W\\\"");
        info.put("fileName", "/home/<USER>/logs/applogs/mi-i18n.log");
        info.put("log", "[2024-09-26 15:59:00] [mi-i18n] [pn1-info-i18n-web-go01.azind] [NOTICE] [************] {\\\"ActionName\\\":\\\"paygo\\\",\\\"CheckLogin_result\\\":**********,\\\"ControllerName\\\":\\\"buy\\\",\\\"DJobSize\\\":0,\\\"Debug-ClientIp\\\":\\\"*************\\\",\\\"Debug-RemoteAddr\\\":\\\"************:58439\\\",\\\"Debug-X-Forwarded-For\\\":\\\"\\\",\\\"Debug-X-Real-IP\\\":\\\"*************\\\",\\\"Debug-Xm-Ip\\\":\\\"\\\",\\\"ErrNo\\\":20017,\\\"Hostname\\\":\\\"pn1-info-i18n-web-go01.azind\\\",\\\"InputData\\\":{\\\"BeginTimeSec\\\":1727337540,\\\"LoginVerifyMode\\\":1,\\\"IsNeedLogin\\\":true,\\\"OutputType\\\":1,\\\"MustHttps\\\":false,\\\"IsNeedSecurity\\\":false,\\\"IsCheckEtag\\\":false,\\\"IsNeedFilterReffer\\\":false,\\\"AppLocal\\\":\\\"in\\\",\\\"Uri\\\":\\\"/in/buy/paygo\\\",\\\"ClientType\\\":\\\"mobile\\\",\\\"IsApp\\\":true,\\\"AppInfo\\\":\\\"version\\u003d40912\\\\u0026phone_model\\u003dM2003J15SC\\\\u0026networkType\\u003dMOBILE\\\\u0026appname\\u003dshop\\\\u0026android_sdk_version\\u003d30\\\\u0026android_version\\u003d11\\\\u0026ratio\\u003d2.03\\\\u0026DEVICEID\\u003d325eafbe-33ac-40f1-a878-92c819e0414e\\\\u0026device_width\\u003d1080\\\\u0026device_height\\u003d2196\\\\u0026\\\",\\\"AppVersion\\\":40912,\\\"DeviceID\\\":\\\"325eafbe-33ac-40f1-a878-92c819e0414e\\\",\\\"PhoneModel\\\":\\\"M2003J15SC\\\",\\\"AppType\\\":\\\"ANDROID\\\",\\\"RequestFrom\\\":\\\"store\\\",\\\"GrpcClientType\\\":3,\\\"SdkVersion\\\":0,\\\"RnInfo\\\":{\\\"product\\\":\\\"1\\\"},\\\"RnVersion\\\":\\\"0.59.15\\\",\\\"RnAppVersion\\\":0,\\\"ClientIp\\\":\\\"*************\\\",\\\"ServerType\\\":\\\"APP\\\",\\\"ProxyServerType\\\":\\\"\\\",\\\"LogId\\\":\\\"************\\\",\\\"IsHttps\\\":true,\\\"Warehouse\\\":\\\"520\\\",\\\"GlobalProvince\\\":\\\"\\\",\\\"GlobalCity\\\":\\\"\\\",\\\"GlobalDistrict\\\":\\\"\\\",\\\"QuickOrder\\\":\\\"0\\\",\\\"Protocol\\\":\\\"https\\\",\\\"LoginStatus\\\":1,\\\"UserId\\\":**********,\\\"LoginTime\\\":**********,\\\"GuestId\\\":\\\"XMGUEST-8675EE20-F49C-3E0F-B036-CA3A2F2DA014\\\",\\\"ImgQuality\\\":90,\\\"IsPrintTrace\\\":false,\\\"TokenInvalid\\\":false,\\\"MstUid\\\":\\\"325eafbe-33ac-40f1-a878-92c819e0414e\\\",\\\"Pincode\\\":\\\"641004\\\",\\\"IsSdk\\\":false,\\\"IsPoco\\\":false,\\\"IsB2b\\\":false,\\\"IsSupportV4\\\":true,\\\"CtxWithMetaData\\\":{\\\"Context\\\":0},\\\"UserAccountType\\\":0,\\\"IsCompanyBuyV2\\\":false,\\\"IsCompanyBuyV1\\\":false},\\\"Login_LoginVerifyMode\\\":1,\\\"Login_checkLoginSession_after\\\":\\\"LoginStatus:1, UserId:**********, isPass:true\\\",\\\"Login_checkLoginSession_before\\\":\\\"LoginStatus:2, UserId:**********, isPass:false\\\",\\\"OrderStatusErr\\\":\\\"Order not wait payment, Errno 2018003\\\",\\\"ResponseLevel\\\":\\\"W\\\",\\\"Server\\\":\\\"in.shopapi.b2c.srv\\\",\\\"TagName\\\":\\\"notice_uniq\\\",\\\"TimeUsed\\\":121,\\\"TimeUsedFormat\\\":\\\"121.177266ms\\\",\\\"VJobSize\\\":0,\\\"checkChangePassResult_isChangedPass\\\":false,\\\"decrypt_suc\\\":**********,\\\"payGoRiskIntercept\\\":\\\"****************\\\",\\\"paygoBank\\\":\\\"juspay_in\\\",\\\"paygoOrderId\\\":\\\"****************\\\",\\\"rpcStat\\\":{\\\"shopApi\\\":\\\"[/order/viewv2|1|62053],[/pay/banklist|1|54143]\\\"}}");
        info.put("ip", "***********");
        info.put("count", "294");
        info.put("tag", "tags_14_95208_99336");
        info.put("lineNumber", "805872");
        alertInfo.setInfo(info);
        Method method = AlertProcessor.class.getDeclaredMethod("alertAlreadySent", AlertInfo.class);
        method.setAccessible(true);
        Boolean result = (Boolean) method.invoke(alertProcessor, alertInfo);
        Assert.assertFalse(result);
        result = (Boolean) method.invoke(alertProcessor, alertInfo);
        Assert.assertTrue(result);
        jedisCluster.del("hera_log:alert:03a147fa85222605c7e6e5a3445a6138");
    }

    @Test
    public void testParseTimestamp() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Object logTimeStr = "*************";
        Object logTimeErrStr = "woshisongyutong";
        Object logTimeLong = *************L;
        Object logTimeErrorType = new Alert();
        Method method = AlertProcessor.class.getDeclaredMethod("parseTimestamp", Object.class);
        method.setAccessible(true);
        Long logTime1 = (Long) method.invoke(alertProcessor, logTimeStr);
        Long logTime2 = (Long) method.invoke(alertProcessor, logTimeErrStr);
        Long logTime3 = (Long) method.invoke(alertProcessor, logTimeLong);
        Long logTime4 = (Long) method.invoke(alertProcessor, logTimeErrorType);
        Assert.assertEquals(Long.valueOf(*************L), logTime1);
        Assert.assertNull(logTime2);
        Assert.assertEquals(Long.valueOf(*************L), logTime3);
        Assert.assertNull(logTime4);
    }


    @Test
    public void queryByTailIdsTest() {
        List<AlarmTailRelationship> relationships = alertService.alarmWithTailIds(Arrays.asList(197L, 198L, 611L), "");
        log.info("relationships:{}", gson.toJson(relationships));
        Assert.assertNotNull(relationships);
    }

}
