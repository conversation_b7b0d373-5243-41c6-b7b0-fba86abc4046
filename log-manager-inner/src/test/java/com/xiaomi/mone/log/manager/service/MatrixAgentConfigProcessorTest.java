package com.xiaomi.mone.log.manager.service;

import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.api.model.meta.LogCollectMeta;
import org.junit.Before;
import org.junit.Test;

import static com.xiaomi.mone.log.manager.service.MoneUserDetailService.GSON;
import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;


@Slf4j
public class MatrixAgentConfigProcessorTest {
    private MatrixAgentConfigProcessor matrixAgentConfigProcessor;
    private CloudMLAgentConfigProcessor cloudmlAgentConfigProcessor;

    @Before
    public void buildBean() {
        getConfigFromNanos();
        Ioc.ins().init("com.xiaomi");
        matrixAgentConfigProcessor = Ioc.ins().getBean(MatrixAgentConfigProcessor.class);
        cloudmlAgentConfigProcessor = Ioc.ins().getBean(CloudMLAgentConfigProcessor.class);
    }

    @Test
    public void testQueryLogCollectMeta() {
        LogCollectMeta meta = matrixAgentConfigProcessor.queryLogCollectMeta("10.38.201.240");
        log.info("testQueryLogCollectMeta result:{}", GSON.toJson(meta));
    }

    @Test
    public void testQueryLogCollectMetaForCloudML() {
        LogCollectMeta meta = cloudmlAgentConfigProcessor.queryLogCollectMeta("10.38.211.188");
        log.info("testQueryLogCollectMetaForCLoudML result:{}", GSON.toJson(meta));
    }

}
