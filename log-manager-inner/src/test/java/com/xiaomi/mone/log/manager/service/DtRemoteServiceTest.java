package com.xiaomi.mone.log.manager.service;

import com.xiaomi.mone.log.manager.model.dto.dt.DtJobListResponse;
import com.xiaomi.mone.log.manager.model.dto.dt.DtTableDetailDTO;
import com.xiaomi.mone.log.manager.model.dto.dt.DtTableInfoDTO;
import com.xiaomi.mone.log.manager.model.dto.dt.DtTokenDetail;
import com.xiaomi.mone.log.manager.service.remoting.DtRemoteService;
import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;

/**
 * @author: songyutong1
 * @date: 2024/09/04/20:19
 */
@Slf4j
public class DtRemoteServiceTest {

    private DtRemoteService dtRemoteService;

    @Before
    public void buildBean() {
        getConfigFromNanos();
        Ioc.ins().init("com.xiaomi");
        dtRemoteService = Ioc.ins().getBean(DtRemoteService.class);
    }

    @Test
    public void testQueryTableList() {
        List<DtTableInfoDTO> dtTableList = dtRemoteService.queryDtTableList("es_tj_staging", "default", false, true, "", "ES", "ecd4fcf210004296945800466e263de5");
        System.out.println(dtTableList);
        Assert.assertNotNull(dtTableList);
    }

    @Test
    public void testQueryTableDetail() {
        DtTableDetailDTO dtTableDetailDTO = dtRemoteService.queryDtTableDetail("talos_staging_cnbj2_talos", "default", "staging_hera_topic_121189_134760", "ecd4fcf210004296945800466e263de5");
        System.out.println(dtTableDetailDTO);
        Assert.assertNotNull(dtTableDetailDTO.getService());
    }

    @Test
    public void testCreateEsTable() {
        boolean success = dtRemoteService.createEsTable("","es_tj_staging", "default", "local_hera_index_songyutong_test", "ecd4fcf210004296945800466e263de5", "timestamp:1,level:1,msg:1,logstore:3,logsource:3,mqtopic:3,mqtag:3,logip:3,tail:3,linenumber:3,message:1,tailId:3", "date,text,text,keyword,keyword,keyword,keyword,keyword,keyword,long,text,integer", 7, "test_table");
        Assert.assertTrue(success);
    }

    @Test
    public void testCreateTalosTable() {
        boolean success = dtRemoteService.createTalosTable("talos_staging_cnbj2_talos", "default", "local_hera_topic_songyutong_test", "ecd4fcf210004296945800466e263de5", "test_table");
        Assert.assertTrue(success);
    }

    @Test
    public void testDeleteDtTable() {
        boolean esSuccess = dtRemoteService.deleteDtTable("es_tj_staging", "default", "local_hera_index_songyutong_test", "ecd4fcf210004296945800466e263de5");
        boolean topicSuccess = dtRemoteService.deleteDtTable("talos_staging_cnbj2_talos", "default", "local_hera_topic_songyutong_test", "ecd4fcf210004296945800466e263de5");
        Assert.assertTrue(esSuccess && topicSuccess);
    }

    @Test
    public void testQueryJobList() {
        DtJobListResponse dtJobListResponse = dtRemoteService.queryJobList(1L, 300L, null, false, "", "FLINKJAR_STREAMING", "0d529701f8354e55a2b058c42f7773b0");
        System.out.println(dtJobListResponse);
        Assert.assertNotNull(dtJobListResponse.getPaging());
    }

    @Test
    public void testQueryTokenDetail() {
        DtTokenDetail tokenDetail = dtRemoteService.queryTokenDetail("ecd4fcf210004296945800466e263de5");
        Assert.assertEquals(Long.valueOf(13529L), tokenDetail.getWorkspaceId());
    }

    @Test
    public void testQueryJobDetail() {
//        AlertJobDetail alertJobDetail = dtRemoteService.queryJobDetail(2472074L, "0d529701f8354e55a2b058c42f7773b0");
//        Assert.assertNotNull(alertJobDetail.getJobStatus());
    }

    @Test
    public void testStartJob() {
//        Integer startCount = dtRemoteService.startJob(2472074L, "0d529701f8354e55a2b058c42f7773b0");
//        Assert.assertNotNull(startCount);
    }

    @Test
    public void testStopJob() {
//        Boolean success = dtRemoteService.stopJob(2472074L, "0d529701f8354e55a2b058c42f7773b0");
//        Assert.assertTrue(success);
    }

    @Test
    public void testDeleteJobForce() {
//        Integer deleteCount = dtRemoteService.deleteJobForce(2472074L, "0d529701f8354e55a2b058c42f7773b0");
//        Assert.assertNotNull(deleteCount);
    }

}
