package com.xiaomi.mone.log.manager.service;

import com.xiaomi.mone.log.manager.dao.InnerMilogLogStoreDao;
import com.xiaomi.mone.log.manager.dao.LogStorageDao;
import com.xiaomi.mone.log.manager.model.dto.BillingAccount;
import com.xiaomi.mone.log.manager.model.dto.ResourceBillDetailDTO;
import com.xiaomi.mone.log.manager.service.impl.BillingManagementServiceImpl;
import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.dao.MilogLogstoreDao;
import org.apache.ozhera.log.manager.model.pojo.MilogEsIndexDO;
import org.apache.ozhera.log.manager.model.pojo.MilogLogStoreDO;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;
import org.junit.Before;
import org.junit.Test;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import static com.xiaomi.mone.log.manager.user.InnerMoneUtil.gson;
import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;

@Slf4j
public class BillingManagementServiceImplTest {

    private BillingManagementServiceImpl billingManagementService;
    private MilogLogTailDao milogLogtailDao;
    private MilogLogstoreDao milogLogstoreDao;
    private LogStorageDao logStorageDao;
    @Resource
    private InnerMilogLogStoreDao innerMilogLogStoreDao;

    @Before
    public void buildBean() {
        getConfigFromNanos();
        Ioc.ins().init("com.xiaomi");
        billingManagementService = Ioc.ins().getBean(BillingManagementServiceImpl.class);
        milogLogtailDao = Ioc.ins().getBean(MilogLogTailDao.class);
        milogLogstoreDao = Ioc.ins().getBean(MilogLogstoreDao.class);
        logStorageDao = Ioc.ins().getBean(LogStorageDao.class);
        innerMilogLogStoreDao = Ioc.ins().getBean(InnerMilogLogStoreDao.class);

    }

    @Test
    public void testBuildBaseBillDetail() {
        try {
            List<Long> storeIds = new ArrayList<>(3);
            storeIds.add(91767L);
            List<MilogLogTailDo> milogLogtailDos = milogLogtailDao.queryTailsByStores(storeIds);
            ResourceBillDetailDTO detail = billingManagementService.buildBaseBillDetail(milogLogtailDos);
            log.info("buildBaseBillDetail finished, detail:\n{}", gson.toJson(detail));
        } catch (Exception e) {
            log.error("buildBaseBillDetail test failed: error", e);
        }
    }

    @Test
    public void testQueryES() {
        try {
            MilogLogStoreDO store = milogLogstoreDao.queryById(90420L);
            double result = billingManagementService.queryESStorageBytes(store, true, "");

            log.info("queryESStorageBytes finished, totalSize:{}", result);
        } catch (Exception e) {
            log.error("queryESStorageBytes test failed: error", e);
        }
    }

    @Test
    public void testQueryESAvg() {
        try {
            MilogLogStoreDO store = milogLogstoreDao.queryById(90420L);
            double result = billingManagementService.queryESStorageAvgBytes(store, true, "");
            log.info("queryESStorageAvgBytes finished, totalSize:{}", result);
        } catch (Exception e) {
            log.error("queryESStorageAvgBytes test failed: error", e);
        }
    }

    @Test
    public void testQueryMilogAppAccount() {
        try {
            Long milogAppId = 86916L;
            BillingAccount result = billingManagementService.queryMilogAppAccount(milogAppId);
            log.info("queryMilogAppAccount finished, account:\n{}", gson.toJson(result));
        } catch (Exception e) {
            log.error("queryMilogAppAccount test failed: error", e);
        }
    }

    @Test
    public void testCollectLogStorage() {
        try {
            String thisDay = LocalDate.now().plusDays(-1).format(DateTimeFormatter.ofPattern("yyyy.MM.dd"));
            if (!billingManagementService.isLogStorageCollectDone(thisDay)) {
                List<MilogEsIndexDO> indexs = new ArrayList<>(innerMilogLogStoreDao.getPaginatedESIndex(10, 10));
                boolean result = billingManagementService.collectLogStorage(indexs, thisDay);
                log.info("collectLogStorage test finished: {}", result);
                return;
            }
            log.info("collectLogStorage test has been done for day: {}", thisDay);
            List<MilogEsIndexDO> indexs = innerMilogLogStoreDao.getPaginatedESIndex(10, 10);
            boolean result = billingManagementService.collectLogStorage(indexs, thisDay);
            log.info("collectLogStorage test finished: {}", result);
        } catch (Exception e) {
            log.error("collectLogStorage test failed: error", e);
        }
    }

}
