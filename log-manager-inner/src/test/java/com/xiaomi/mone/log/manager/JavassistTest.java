package com.xiaomi.mone.log.manager;

import javassist.*;
import org.junit.Test;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @version 1.0
 * @description Javassist 代理测试
 * @date 2024/2/1 10:02
 */
public class JavassistTest {
    @Test
    public void test_Javassist() throws CannotCompileException, NotFoundException, IOException, ClassNotFoundException, NoSuchMethodException, InvocationTargetException, IllegalAccessException, InstantiationException, InvocationTargetException {
        ClassPool pool = ClassPool.getDefault();
        CtClass cls = pool.makeClass("com.xiaomi.mone.log.manager.proxyClass");
        // 添加私有成员name及其getter、setter方法
        CtField param = new CtField(pool.get("java.lang.String"), "name", cls);
        param.setModifiers(Modifier.PRIVATE);
        cls.addMethod(CtNewMethod.setter("setName", param));
        cls.addMethod(CtNewMethod.getter("getName", param));
        cls.addField(param, CtField.Initializer.constant(""));

        // 添加无参的构造体
        CtConstructor cons = new CtConstructor(new CtClass[]{}, cls);
        cons.setBody("{name = \"ARong\";}");
        cls.addConstructor(cons);

        // 添加有参的构造体
        cons = new CtConstructor(new CtClass[]{pool.get("java.lang.String")}, cls);
        cons.setBody("{$0.name = $1;}");
        cls.addConstructor(cons);

        // 打印创建类的类名
        System.out.println(cls.toClass());
        // 输出字节码到class中
        byte[] bytes = cls.toBytecode();
        FileOutputStream fos = new FileOutputStream(new File("/code/xiaomi/hera-log/log-manager-inner/src/test/java/com/xiaomi/mone/log/manager/proxyClass.class"));
        fos.write(bytes);
        fos.flush();
        Object o = Class.forName("com.xiaomi.mone.log.manager.proxyClass").newInstance();
        Method getName = o.getClass().getMethod("getName");
        Object res = getName.invoke(o);
        System.out.println(o);
        System.out.println(res);
    }

}
