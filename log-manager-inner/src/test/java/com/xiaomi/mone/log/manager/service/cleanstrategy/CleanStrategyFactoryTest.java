package com.xiaomi.mone.log.manager.service.cleanstrategy;

import com.xiaomi.mone.log.manager.enums.ClearStageEnum;
import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;

/**
 * @author: songyutong1
 * @date: 2024/09/14/10:57
 */
@Slf4j
public class CleanStrategyFactoryTest {

    @Before
    public void buildUp() {
        getConfigFromNanos();
        Ioc.ins().init("com.xiaomi");
    }

    @Test
    public void testGetCleanStrategy() {
        AbstractCleanStrategy dbEsIndexNullStrategy = CleanStrategyFactory.getCleanStrategy(ClearStageEnum.STAGE_1);
        Assert.assertTrue(dbEsIndexNullStrategy instanceof DbEsIndexNullStrategy);

        AbstractCleanStrategy dbEsIndexUnusedStrategy = CleanStrategyFactory.getCleanStrategy(ClearStageEnum.STAGE_2);
        Assert.assertTrue(dbEsIndexUnusedStrategy instanceof DbEsIndexUnusedStrategy);

        AbstractCleanStrategy dbTopicNullStrategy = CleanStrategyFactory.getCleanStrategy(ClearStageEnum.STAGE_3);
        Assert.assertTrue(dbTopicNullStrategy instanceof DbTopicNullStrategy);

        AbstractCleanStrategy dbTopicUnusedStrategy = CleanStrategyFactory.getCleanStrategy(ClearStageEnum.STAGE_4);
        Assert.assertTrue(dbTopicUnusedStrategy instanceof DbTopicUnusedStrategy);

        AbstractCleanStrategy dtEsResourceCleanStrategy = CleanStrategyFactory.getCleanStrategy(ClearStageEnum.STAGE_5);
        Assert.assertTrue(dtEsResourceCleanStrategy instanceof DtEsResourceCleanStrategy);

        AbstractCleanStrategy dtTopicResourceCleanStrategy = CleanStrategyFactory.getCleanStrategy(ClearStageEnum.STAGE_6);
        Assert.assertTrue(dtTopicResourceCleanStrategy instanceof DtTopicResourceCleanStrategy);

        AbstractCleanStrategy dtFlinkJobCleanStrategy = CleanStrategyFactory.getCleanStrategy(ClearStageEnum.STAGE_7);
        Assert.assertTrue(dtFlinkJobCleanStrategy instanceof DtFlinkJobCleanStrategy);
    }

}
