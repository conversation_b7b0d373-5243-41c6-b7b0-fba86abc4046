package com.xiaomi.mone.log.manager.common;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025/4/21 18:28
 */
public class SimpleJsonParser {
    private int index;
    private String json;

    public Object parse(String jsonString) {
        this.json = jsonString.trim();
        this.index = 0;
        return parseValue();
    }

    private Object parseValue() {
        skipWhitespace();
        char c = currentChar();

        if (c == '{') {
            return parseObject();
        } else if (c == '[') {
            return parseArray();
        } else if (c == '"') {
            return parseString();
        } else if (c == '-' || Character.isDigit(c)) {
            return parseNumber();
        } else if (json.startsWith("true", index)) {
            index += 4;
            return true;
        } else if (json.startsWith("false", index)) {
            index += 5;
            return false;
        } else if (json.startsWith("null", index)) {
            index += 4;
            return null;
        } else {
            throw new RuntimeException("Unexpected character: " + c);
        }
    }

    private Map<String, Object> parseObject() {
        Map<String, Object> map = new HashMap<>();
        index++; // skip '{'

        while (true) {
            skipWhitespace();
            if (currentChar() == '}') {
                index++;
                break;
            }

            String key = parseString();
            skipWhitespace();
            expect(':');
            Object value = parseValue();
            map.put(key, value);

            skipWhitespace();
            if (currentChar() == ',') {
                index++;
            } else if (currentChar() == '}') {
                index++;
                break;
            } else {
                throw new RuntimeException("Expected ',' or '}'");
            }
        }

        return map;
    }

    private List<Object> parseArray() {
        List<Object> list = new ArrayList<>();
        index++; // skip '['

        while (true) {
            skipWhitespace();
            if (currentChar() == ']') {
                index++;
                break;
            }

            Object value = parseValue();
            list.add(value);

            skipWhitespace();
            if (currentChar() == ',') {
                index++;
            } else if (currentChar() == ']') {
                index++;
                break;
            } else {
                throw new RuntimeException("Expected ',' or ']'");
            }
        }

        return list;
    }

    private String parseString() {
        index++; // skip '"'
        StringBuilder sb = new StringBuilder();

        while (index < json.length()) {
            char c = currentChar();

            if (c == '"') {
                index++;
                return sb.toString();
            } else if (c == '\\') {
                index++;
                c = currentChar();
                switch (c) {
                    case '"':
                        sb.append('"');
                        break;
                    case '\\':
                        sb.append('\\');
                        break;
                    case '/':
                        sb.append('/');
                        break;
                    case 'b':
                        sb.append('\b');
                        break;
                    case 'f':
                        sb.append('\f');
                        break;
                    case 'n':
                        sb.append('\n');
                        break;
                    case 'r':
                        sb.append('\r');
                        break;
                    case 't':
                        sb.append('\t');
                        break;
                    case 'u':
                        // 处理Unicode转义
                        String hex = json.substring(index + 1, index + 5);
                        sb.append((char) Integer.parseInt(hex, 16));
                        index += 4;
                        break;
                    default:
                        throw new RuntimeException("Invalid escape sequence");
                }
            } else {
                sb.append(c);
            }
            index++;
        }

        throw new RuntimeException("Unterminated string");
    }

    private Object parseNumber() {
        int start = index;

        if (currentChar() == '-') {
            index++;
        }

        while (index < json.length() && Character.isDigit(currentChar())) {
            index++;
        }

        // 处理小数部分
        if (index < json.length() && currentChar() == '.') {
            index++;
            while (index < json.length() && Character.isDigit(currentChar())) {
                index++;
            }
        }

        // 处理指数部分
        if (index < json.length() && (currentChar() == 'e' || currentChar() == 'E')) {
            index++;
            if (currentChar() == '+' || currentChar() == '-') {
                index++;
            }
            while (index < json.length() && Character.isDigit(currentChar())) {
                index++;
            }
        }

        String numStr = json.substring(start, index);

        // 判断是整数还是浮点数
        if (numStr.contains(".") || numStr.contains("e") || numStr.contains("E")) {
            return Double.parseDouble(numStr);
        } else {
            try {
                return Integer.parseInt(numStr);
            } catch (NumberFormatException e) {
                return Long.parseLong(numStr);
            }
        }
    }

    private void skipWhitespace() {
        while (index < json.length() && Character.isWhitespace(currentChar())) {
            index++;
        }
    }

    private char currentChar() {
        if (index >= json.length()) {
            throw new RuntimeException("Unexpected end of input");
        }
        return json.charAt(index);
    }

    private void expect(char expected) {
        skipWhitespace();
        if (currentChar() != expected) {
            throw new RuntimeException("Expected '" + expected + "'");
        }
        index++;
    }

    public static void main(String[] args) {
        String json = "{\"name\":\"John\", \"age\":30, \"isStudent\":false, \"address\":null, \"scores\":[90, 85.5, 92]}";

        SimpleJsonParser parser = new SimpleJsonParser();
        Object result = parser.parse(json);

        System.out.println("Parsed result: " + result);
    }

}
