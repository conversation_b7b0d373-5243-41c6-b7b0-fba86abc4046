package com.xiaomi.mone.log.manager.service;

import com.xiaomi.mone.log.manager.service.impl.InnerMilogMiddlewareConfigServiceImpl;
import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ozhera.log.api.model.bo.MiLogResource;
import org.apache.ozhera.log.api.model.vo.EsIndexVo;
import org.apache.ozhera.log.common.Result;
import org.apache.ozhera.log.manager.mapper.MilogEsClusterMapper;
import org.apache.ozhera.log.manager.model.pojo.MilogEsClusterDO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

import static com.xiaomi.mone.log.manager.service.listener.StoreOperateListener.regStoreOperateListener;
import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;

/**
 * @author: songyutong1
 * @date: 2024/08/27/15:53
 */
@Slf4j
public class InnerMilogMiddlewareConfigServiceImplTest {

    private InnerMilogMiddlewareConfigServiceImpl innerMilogMiddlewareConfigService;

    private MilogEsClusterMapper milogEsClusterMapper;

    @Before
    public void buildBean() {
        getConfigFromNanos();
        Ioc.ins().init("com.xiaomi");
        regStoreOperateListener();
        innerMilogMiddlewareConfigService = Ioc.ins().getBean(InnerMilogMiddlewareConfigServiceImpl.class);
        milogEsClusterMapper = Ioc.ins().getBean(MilogEsClusterMapper.class);
    }

    @Test
    public void testEsIndexHealth() {
        MilogEsClusterDO milogEsClusterDO = milogEsClusterMapper.selectById(120032);
        MiLogResource miLogResource = new MiLogResource();
        miLogResource.setServiceUrl(milogEsClusterDO.getAddr());
        miLogResource.setAk(milogEsClusterDO.getUser());
        miLogResource.setSk(milogEsClusterDO.getPwd());
        miLogResource.setConWay("pwd");
        miLogResource.setResourceCode(4);
        miLogResource.setStorageType("elasticsearch");

        ArrayList<EsIndexVo> list = new ArrayList<>();
        EsIndexVo esIndexVo = new EsIndexVo();
        ArrayList<String> indexList = new ArrayList<>();
        indexList.add("nox_processed_event_c3");

        esIndexVo.setEsIndexList(indexList);
        list.add(esIndexVo);
        miLogResource.setMultipleEsIndex(list);

        Result<List> successResult = innerMilogMiddlewareConfigService.esIndexHealth(miLogResource);
        Assert.assertTrue(successResult.getCode() == 0 && CollectionUtils.isEmpty(successResult.getData()));

        indexList.add("123");
        indexList.add("nox_event");
        Result<List> indexFailResult = innerMilogMiddlewareConfigService.esIndexHealth(miLogResource);
        ArrayList<String> expectedList = new ArrayList<>();
        expectedList.add("123");
        expectedList.add("nox_event");
        Assert.assertTrue(indexFailResult.getCode() == 0 && indexFailResult.getData().equals(expectedList));

        miLogResource.setServiceUrl("c3srv.api.es.srv:8080");
        Result<List> esServerFailResult = innerMilogMiddlewareConfigService.esIndexHealth(miLogResource);
        Assert.assertTrue(esServerFailResult.getCode() == 1);
    }

    @Test
    public void testAppendPortIfNecessary() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Method method = innerMilogMiddlewareConfigService.getClass().getDeclaredMethod("appendPortIfNecessary", String.class);
        method.setAccessible(true);

        String url = (String) method.invoke(innerMilogMiddlewareConfigService, "localhost");
        Assert.assertEquals("localhost:80", url);

        url = (String) method.invoke(innerMilogMiddlewareConfigService, "localhost:80");
        Assert.assertEquals("localhost:80", url);

        url = (String) method.invoke(innerMilogMiddlewareConfigService, "localhost:");
        Assert.assertEquals("localhost:80", url);
    }

}
