package com.xiaomi.mone.log.manager.service;

import com.google.gson.Gson;
import com.xiaomi.mone.log.manager.model.Pair;
import com.xiaomi.mone.log.manager.model.bo.SpacePartitionBalance;
import com.xiaomi.mone.log.manager.model.vo.MachinePartitionParam;
import com.xiaomi.mone.log.manager.model.vo.SpaceIpParam;
import com.xiaomi.mone.log.manager.service.impl.StreamPartitionServiceInnerImpl_Test;
import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.api.enums.MachineRegionEnum;
import org.apache.ozhera.log.manager.model.page.PageInfo;
import org.junit.Before;
import org.junit.Test;
import org.springframework.util.Assert;

import java.util.List;

import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/10/11 16:40
 */
@Slf4j
public class StreamPartitionServiceInnerTest {

    private Gson gson = new Gson();

    private StreamPartitionServiceInnerImpl_Test streamPartitionService;

    @Before
    public void buildBean() {
        getConfigFromNanos();
        Ioc.ins().init("com.xiaomi");
        streamPartitionService = Ioc.ins().getBean(StreamPartitionServiceInnerImpl_Test.class);
    }

    @Test
    public void querySpacePartitionBalanceTest() {
        MachinePartitionParam machinePartitionParam = new MachinePartitionParam();
        machinePartitionParam.setMachineRoom(MachineRegionEnum.CN_MACHINE.getEn());

        machinePartitionParam.setPageNum(74);

        PageInfo<SpacePartitionBalance> spacePartitionBalances = streamPartitionService.querySpacePartitionBalance(machinePartitionParam);
        log.info("res result:{}", gson.toJson(spacePartitionBalances));
    }

    @Test
    public void addSpaceToIpTest() {
        SpaceIpParam spaceIpParam = new SpaceIpParam();
        spaceIpParam.setMachineRoom("cn");
        spaceIpParam.setUniqueKey("127.0.0.1");
        spaceIpParam.setSpaceId(123L);
        spaceIpParam.setSpaceId(1233L);
        Boolean result = streamPartitionService.addSpaceToIp(spaceIpParam);
        Assert.isTrue(result, "添加失败");
    }

    @Test
    public void delSpaceToIpTest() {
        SpaceIpParam spaceIpParam = new SpaceIpParam();
        spaceIpParam.setMachineRoom("cn");
        spaceIpParam.setUniqueKey("127.0.0.1");
        spaceIpParam.setSpaceId(123L);
//        spaceIpParam.setSpaceId(1233L);
        Boolean result = streamPartitionService.delSpaceToIp(spaceIpParam);
        Assert.isTrue(result, "删除失败");
    }

    @Test
    public void queryStreamListTest() {
        MachinePartitionParam partitionParam = new MachinePartitionParam();
        partitionParam.setMachineRoom("cn");
        PageInfo<Pair<String, String>> streamList = streamPartitionService.queryStreamList(partitionParam);
        log.info("queryStreamList result:{}", gson.toJson(streamList));
        Assert.notEmpty(streamList.getList(), "日志消费机器不存在");
    }

    @Test
    public void queryIpPartitionBalanceTest() {
        MachinePartitionParam partitionParam = new MachinePartitionParam();
        partitionParam.setMachineRoom("cn");
        partitionParam.setUniqueKey("************");
        PageInfo<Pair<Long, String>> ipPartitionBalance = streamPartitionService.queryIpPartitionBalance(partitionParam);
        log.info("queryIpPartitionBalance result:{}", gson.toJson(ipPartitionBalance));
    }

    @Test
    public void findUnIncludedSpaceListTest() {
        SpaceIpParam spaceIpParam = new SpaceIpParam();
        spaceIpParam.setMachineRoom("cn");
        spaceIpParam.setUniqueKey("************");
        List<Pair<String, Long>> result = streamPartitionService.findUnIncludedSpaceList(spaceIpParam);
        log.info("results:{}", gson.toJson(result));
    }

    @Test
    public void queryAllUniqueKeyListTest() {
        SpaceIpParam spaceIpParam = new SpaceIpParam();
        spaceIpParam.setMachineRoom("cn");
        spaceIpParam.setSpaceId(2L);
        List<Pair<String, String>> result = streamPartitionService.queryAllUniqueKeyList(spaceIpParam);
        log.info("results:{}", gson.toJson(result));
    }
}
