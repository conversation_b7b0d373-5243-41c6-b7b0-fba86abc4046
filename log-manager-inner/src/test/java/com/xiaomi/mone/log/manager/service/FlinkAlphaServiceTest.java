package com.xiaomi.mone.log.manager.service;

import com.xiaomi.bigdata.workshop.model.BaseJobDetailDto;
import com.xiaomi.bigdata.workshop.model.Notify;
import com.xiaomi.mone.log.manager.controller.AlertController;
import com.xiaomi.mone.log.manager.model.alert.Alert;
import com.xiaomi.mone.log.manager.model.alert.AlertJobDetail;
import com.xiaomi.mone.log.manager.model.bo.alert.AlertParam;
import com.xiaomi.mone.log.manager.model.bo.alert.AlertUpdateParam;
import com.xiaomi.mone.log.manager.service.alert.AlertService;
import com.xiaomi.mone.log.manager.service.alert.FlinkAlphaService;
import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import static com.xiaomi.mone.log.manager.service.MoneUserDetailService.GSON;
import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/9/8 15:34
 */
@Slf4j
public class FlinkAlphaServiceTest {

    private FlinkAlphaService flinkAlphaService;
    private AlertService alertService;
    private AlertController alertController;

    @Before
    public void buildBean() {
        getConfigFromNanos();
        Ioc.ins().init("com.xiaomi");
        flinkAlphaService = Ioc.ins().getBean(FlinkAlphaService.class);
        alertService = Ioc.ins().getBean(AlertService.class);
        alertController = Ioc.ins().getBean(AlertController.class);
    }

    @Test
    public void test_submitTask() {
        // --producerSecretKey 7THi9WIWHfxNv90/fCyjH24XlrqcDh4tgcZO7asb
        // --producerTopic mione_alarm
        // --producerAccessKey AKJVQGLXQRCSVBBJNF
        // --producerServer http://staging-cnbj2-talos.api.xiaomi.net
        // --windowSize 60
        // --secretKey 7THi9WIWHfxNv90/fCyjH24XlrqcDh4tgcZO7asb
        // --consumerTopic 60734_milog_5_china
        // --accessKey AKJVQGLXQRCSVBBJNF
        // --appName milog
        // --consumerServer http://staging-cnbj2-talos.api.xiaomi.net
        // --alertId 87
        // --department china
        // --mqType talos
        Alert alert = alertService.getAlert(87);
        AlertParam params = new AlertParam();
        // params.setMqType("talos");
        // params.setConsumerAccessKey("AKJVQGLXQRCSVBBJNF");
        // params.setConsumerSecretKey("");
        // params.setAlertId(alert.getId());
        // params.setConsumerServer("http://staging-cnbj2-talos.api.xiaomi.net");
        // params.setConsumerTopic("60734_milog_5_china");
        // flinkAlphaService.submitFlinkJob(alert, params, mqInfoBo);
    }

    @Test
    public void test_start_job() {
        flinkAlphaService.startJob("cn", 1102598L, 30091L);
    }

    @Test
    public void test_delete_job() {
        flinkAlphaService.deleteJob("cn", 908539L, 87L);
    }

    @Test
    public void test_stop_job() {
        flinkAlphaService.stopJob("cn", 1102598L, 30091L);
    }

    @Test
    public void testJobDetail() {
        Long jobId = 1L;
        try {
            BaseJobDetailDto jobDetail = flinkAlphaService.getJobDetail("cn", jobId);
            if (BaseJobDetailDto.JobStatusEnum.STARTED == jobDetail.getJobStatus()) {
                log.info("success");
            }
            log.info("result:{}", GSON.toJson(jobDetail));
        } catch (Exception e) {
            log.info("flink job not found in current cluster, dt response:{},stack:{}",
                    e.getMessage(), e.getStackTrace());
        }

    }

    @Test
    public void test_update() {
        Alert alert = alertService.getAlert(87);
        alert.setJobId(1041167L);
        AlertParam params = new AlertParam();
        // params.setMqType("talos");
        // params.setConsumerAccessKey("AKJVQGLXQRCSVBBJNF");
        // params.setConsumerSecretKey("");
        // params.setAlertId(alert.getId());
        // params.setConsumerServer("http://staging-cnbj2-talos.api.xiaomi.net");
        // params.setConsumerTopic("667_zzytest_157_china");
        // flinkAlphaService.updateJob(alert, params, mqInfoBo);
    }

    @Test
    public void test_update_con() {
        AlertUpdateParam param = new AlertUpdateParam();
        param.setAlertId(60002L);
        param.setContacts("wangtao29");
        Alert alert = alertService.getAlert(60002L);
        // flinkAlphaService.updateJob(alert, flinkAlphaService.buildAlertParam(param),
        // mqInfoBo);
    }

    @Test
    public void test_query_job_id() {
        Long jobId = flinkAlphaService.getFlinkJobIdByName("Mione__intra_60");
        log.info("jobId:{}", jobId);
        Assert.assertNotNull(jobId);
    }

    @Test
    public void testGetUserNotify() {
        Notify userNotify = flinkAlphaService.getUserNotify();
        log.info("result:{}", GSON.toJson(userNotify));
        Assert.assertNotNull(userNotify);
    }

    @Test
    public void getJobDetailTest() {
        AlertJobDetail jobDetail = flinkAlphaService.getJobDetail(1264397L);
        log.info("result:{}", GSON.toJson(jobDetail));
    }

}
