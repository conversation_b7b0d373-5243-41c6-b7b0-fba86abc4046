package com.xiaomi.mone.log.manager.user;

import cn.hutool.core.lang.Assert;
import com.xiaomi.mone.log.manager.service.IdmMoneUserDetailService;
import com.xiaomi.youpin.docean.Ioc;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;


public class IdmMoneUserDetailServiceTest {
    private IdmMoneUserDetailService idmDept;

    @Before
    public void pushBean() {
        getConfigFromNanos();
        Ioc.ins().init("com.xiaomi");
        idmDept = Ioc.ins().getBean(IdmMoneUserDetailService.class);
    }

    @Test
    public void queryDeptPersonIds() {
        List<String> mw6310 = idmDept.queryDeptPersonIds("MW6310");
    }
    @Test
    public void queryUsers() {
        List<String> mw6310 = idmDept.queryUses();
    }

    @Test
    public void fuzzySearchAccountListByNameTest() {
        List<String> mw6310 = idmDept.fuzzySearchAccountListByName("wangtao");
    }

    @Test
    public void queryDeptPerson() {
        List<UseDetailInfo> mw6310 = idmDept.queryDeptPerson("MW6310");
        System.out.println(mw6310);
    }

    @Test
    public void queryActiveUserUIdByUserNameTest() {
        String emailPrefix = "wangtao29";
        String uId = idmDept.queryActiveUserUIdByUserName(emailPrefix);
        Assert.isNull(uId);
    }
}