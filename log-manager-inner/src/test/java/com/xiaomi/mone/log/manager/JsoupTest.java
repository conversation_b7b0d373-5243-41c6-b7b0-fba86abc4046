package com.xiaomi.mone.log.manager;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;

/**
 *
 * @description
 * @version 1.0
 * <AUTHOR>
 * @date 2024/6/7 10:31
 *
 */
public class JsoupTest {

    public static void main(String[] args) {
        String htmlContent = "<h1>问诊思维导图：</h1><figure class=\"image\"><img src=\"https://cdn.cnbj0.fds.api.mi-img.com/b2c-mixms-kbs/充电桩无法充电故障问诊-1717490160611.png?GalaxyAccessKeyId=AKERAK5VNPCDLFH4V5&amp;Expires=2664174960799&amp;Signature=gqwJGoI0chE88qt5uoRpth56T3g=\"></figure>";

        // 解析HTML字符串
        Document doc = Jsoup.parse(htmlContent);

        // 获取图片元素
        Element imgElement = doc.select("img").first();

        // 提取图片的URL
        String imageUrl = imgElement.attr("src");

        // 提取标题和段落文本，使用Jsoup的文本提取功能
        String text = doc.text();

        // 打印提取的文本
        System.out.println(text+imageUrl);
    }
}
