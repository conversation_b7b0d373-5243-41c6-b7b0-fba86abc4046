package com.xiaomi.mone.log.manager.service;

import com.google.gson.Gson;
import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.api.model.meta.LogCollectMeta;
import org.apache.ozhera.log.manager.service.impl.AgentConfigServiceImpl;
import org.apache.rocketmq.acl.common.AclClientRPCHook;
import org.apache.rocketmq.acl.common.SessionCredentials;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.RPCHook;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Set;


/**
 * <AUTHOR>
 * @version 1.0
 * @description rocketMQ 操作topic 测试
 * @date 2021/6/30 14:45
 */
@Slf4j
public class RocketMqServiceTest {

    @Before
    public void before() {
//        getConfigFromNanos();
    }

    @Test
    public void testCreateTopic() {
        Ioc.ins().init("com.xiaomi");
        RocketMqService rocketMqService = Ioc.ins().getBean(RocketMqService.class);
    }

    @Test
    public void testDeleteTopic() {
        Ioc.ins().init("com.xiaomi");
        RocketMqService rocketMqService = Ioc.ins().getBean(RocketMqService.class);
        String topicName = "milog_test";
        String serviceTopic = rocketMqService.deleteTopic(1L, 2L, "testProject", topicName);
        Assert.assertNotNull(serviceTopic);
    }

    @Test
    public void testUpdateMqConfig() {
        Ioc.ins().init("com.xiaomi");
        RocketMqService rocketMqService = Ioc.ins().getBean(RocketMqService.class);
    }

    @Test
    public void testCreateConsumerGroup() {
        Ioc.ins().init("com.xiaomi");
        RocketMqService rocketMqService = Ioc.ins().getBean(RocketMqService.class);
        boolean tagSuccess = rocketMqService.createConsumerGroup(100L, 200L, 120L, null);
        Assert.assertTrue(tagSuccess);
    }

    @Test
    public void deleteCreateConsumerGroup() {
        Ioc.ins().init("com.xiaomi");
        RocketMqService rocketMqService = Ioc.ins().getBean(RocketMqService.class);
        boolean tagSuccess = rocketMqService.deleteConsumerGroup(1L, 2L, 1L);
        Assert.assertTrue(tagSuccess);
    }

    @Test
    public void updateTopiSubGroupAuth() {
        Ioc.ins().init("com.xiaomi");
        RocketMqService rocketMqService = Ioc.ins().getBean(RocketMqService.class);
        boolean tagSuccess = rocketMqService.updateTopiSubGroupAuth("122_testProject0_202108040423");
        Assert.assertTrue(tagSuccess);
    }


    @Test
    public void test() {
        Ioc.ins().init("com.xiaomi");
        AgentConfigServiceImpl rocketMqService = Ioc.ins().getBean(AgentConfigServiceImpl.class);
        LogCollectMeta logCollectMetaFromManager = rocketMqService.getLogCollectMetaFromManager("10.38.161.15");
        System.out.println(new Gson().toJson(logCollectMetaFromManager));

    }

    /**
     * 测试查询mq过滤
     */
    @Test
    public void testMqQuery() {
        Ioc.ins().init("com.xiaomi");
        RocketMqService rocketMqService = Ioc.ins().getBean(RocketMqService.class);
        Set<String> queryExistTopics = rocketMqService.queryExistTopic();
        System.out.println(new Gson().toJson(queryExistTopics));

    }

    @Test
    public void sendMessageTest() throws MQBrokerException, RemotingException, InterruptedException, MQClientException, IOException {
        String ak = "";
        String sk = "sKmVZ5iV66Ox1oCSG1Or8xh+01HX7WNam1PLBF79";
        String group = "k8s_machine_changed_intranet";
        String topic = "k8s_machine_changed_intranet";
        String nameServer = "alsgp0-rocketmq.namesrv.api.xiaomi.net:9876";
        DefaultMQProducer producer;
        RPCHook rpcHook = new AclClientRPCHook(new SessionCredentials(ak, sk));
        producer = new DefaultMQProducer(group, rpcHook, true, null);

        producer.setNamesrvAddr(nameServer);
        try {
            producer.start();
        } catch (MQClientException e) {
            log.error("RocketmqPlugin.initDefaultMQProducer error, RocketmqConfig: {}", e);
        }
        String mqMessage = "我是测试信息";
        Message message = new Message();
        message.setTopic(topic);
        message.setBody(mqMessage.getBytes(StandardCharsets.UTF_8));
        producer.send(message);
        System.in.read();
    }
}
