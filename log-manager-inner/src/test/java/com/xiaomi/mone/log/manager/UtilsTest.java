package com.xiaomi.mone.log.manager;

import cn.hutool.extra.mail.MailAccount;
import cn.hutool.extra.mail.MailUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.manager.common.Utils;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/12/13 17:33
 */
@Slf4j
public class UtilsTest {


    @Test
    public void getKeyValueListTest() {
        String keyList = "timestamp:1,level:1,traceId:1,threadName:1,className:1,line:1,methodName:1,message:1,podName:1,logstore:3,logsource:3,mqtopic:3,mqtag:3,logip:3,tail:3,linenumber:3,tailId:3";
        String valueList = "0,1,2,3,4,-1,-1,7,6,5";
        String keyValueList = Utils.getKeyValueList(keyList, valueList);
        log.info("keyValueList:{}", keyValueList);
    }

    @Test
    public void sendMailTest(){
        // 配置 QQ 邮箱的 SMTP 服务器
        MailAccount account = new MailAccount();
        account.setHost("smtp.qq.com"); // SMTP 服务器地址
        account.setPort(587); // SMTP 服务器端口
        account.setAuth(true); // 是否需要身份验证
        account.setFrom("<EMAIL>"); // 发件人邮箱
        account.setUser("<EMAIL>"); // 发件人用户名
        account.setPass("your-authorization-code"); // 发件人授权码

        // 发送邮件到 QQ 邮箱
        MailUtil.send(account, "<EMAIL>", "测试邮件", "这是一封测试邮件", false);
        System.out.println("邮件发送成功！");
    }
}
