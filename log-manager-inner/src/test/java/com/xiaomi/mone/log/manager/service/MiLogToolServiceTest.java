package com.xiaomi.mone.log.manager.service;

import com.google.gson.Gson;
import com.xiaomi.mone.log.manager.model.MigrateDtResourceConfig;
import com.xiaomi.mone.log.manager.service.impl.MiLogToolServiceImpl;
import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/11/2 21:34
 */
@Slf4j
public class MiLogToolServiceTest {

    private MiLogToolServiceImpl miLogToolService;
    private Gson gson = new Gson();
    private Map<String, String> headerMap = new HashMap<>();

    @Before
    public void buildBean() {
        getConfigFromNanos();
        Ioc.ins().init("com.xiaomi");
        miLogToolService = Ioc.ins().getBean(MiLogToolServiceImpl.class);
        String authorization = String.format("workspace-token/1.0 %s", "8d1f337ba417483c88cba72d24c5fe7e");
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Authorization", authorization);
    }

    @Test
    public void testDubbo() {
        miLogToolService.fixLogTailLogAppId("zzytest");
    }

    @Test
    public void testFixLogAlertJarPath() {
        Long alertId = 90873L;
        miLogToolService.fixLogAlertJarPath(alertId);
    }

    @Test
    public void testStartJob() {
        Long alertId = 90183L;
        miLogToolService.startJob(alertId, headerMap);
    }

    @Test
    public void testQueryAlarmByCondition() {
        String key = "";
        String value = "";
        List<Long> jobIds = miLogToolService.queryAlarmByCondition(key, value);
        log.info("test result:{}", gson.toJson(jobIds));
        Assert.notEmpty(jobIds, "没有满足的条件");
    }

    @Test
    public void testFixAlarmLogPath() {
        Long alertId = 90104L;
        miLogToolService.fixAlarmLogPath(alertId);
    }

    @Test
    public void testFixAlarmLogAppId() {
        MigrateDtResourceConfig config = MigrateDtResourceConfig.builder()
                .alphaToken("2498ca7257de415f9182774fe7c7f69c")
                .talosServiceUrl("http://car-nc4srv-talos.api.xiaomi.net")
                .talosAk("AKU2TWYZHOCAAZXXM2")
                .talosSk("")
                .talosCatalog("talos_nc4srv_car_talos")
                .esCatalog("es_sh_nc4")
                .esAddr("nc4dw.api.es.srv:80")
                .esDatabase("default")
                .machineRoom("shgs-nc4-303")
                .spaceId(43L)
                .esClusterId(90014L)
                .enableEs(false)
                .enableTalos(false)
                .doneMySQLOrder(false)
                .enableNacosRefresh(false)
                .build();
        log.info("online result:{}", gson.toJson(config));
    }

    @Test
    public void testMigrateCollectionResourceToAnotherDtSpace() {
        // 测试：复制 天津‘Hera日志’ 下的资源到 线上北京机房
        MigrateDtResourceConfig config = MigrateDtResourceConfig.builder()
                .alphaToken("4244b7014a5c44fea63bea711c7697fe")
                .talosServiceUrl("http://cnbj4-talos.api.xiaomi.net")
                .talosAk("AKJRYAOV367G4NX4C7")
                .talosSk("")
                .talosCatalog("talos_cnbj4_talos")
                .esCatalog("es_zjy_log")
                .esAddr("zjydw.api.es.srv:80")
                .esDatabase("default")
                .machineRoom("cn")
                .spaceId(90080L) // test_应用日志
                .esClusterId(90002L)
                .enableEs(false)
                .enableTalos(true)
                .doneMySQLOrder(false)
                .enableNacosRefresh(false)
                .build();

        String result = miLogToolService.migrateCollectionResourceToAnotherDtSpace(config);
        log.info("test result:{}", gson.toJson(result));

    }

    @Test
    public void testRebuildAlert() {
        //测试：在天津-日志告警空间下重建被删除的一个 job
        Long alertId = 120050L;
        String oldflinkCluster = "tjwqstaging";
        String oldAlphaToken = "72c7689185bb4014985a42ad0a678210"; // 天津-日志告警

        String result = miLogToolService.rebuildAlert(alertId, oldflinkCluster, oldAlphaToken, "", false);
        log.info("test result:{}", gson.toJson(result));
    }

}
