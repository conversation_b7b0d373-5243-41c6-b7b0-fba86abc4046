package com.xiaomi.mone.log.manager.service;

import com.xiaomi.mone.log.manager.service.impl.HeraLogApiServiceImpl;
import com.xiaomi.mone.model.req.LogFilterOptions;
import com.xiaomi.mone.model.req.LogUrlParam;
import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;

import java.util.List;
import java.util.Map;

import static com.xiaomi.mone.log.manager.service.MoneUserDetailService.GSON;
import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/3/6 16:42
 */
@Slf4j
public class HeraLogApiServiceImplTest {
    private HeraLogApiServiceImpl heraLogApiService;

    @Before
    public void buildBean() {
        getConfigFromNanos();
        Ioc.ins().init("com.xiaomi");
        heraLogApiService = Ioc.ins().getBean(HeraLogApiServiceImpl.class);
    }

    @Test
    public void queryLogUrlTest() {
        LogUrlParam logUrlParam = new LogUrlParam();
        logUrlParam.setProjectId(60734L);
        logUrlParam.setTraceId("d990233428f7814c9db99082d6c48c89");
        List<String> logUrls = heraLogApiService.queryLogUrl(logUrlParam);
        log.info("logUrls:{}", GSON.toJson(logUrls));
    }

    @Test
    public void queryLogDataTest() {
        LogFilterOptions filterOptions = new LogFilterOptions();
        filterOptions.setProjectId(5L);
        filterOptions.setEnvId(506L);
        filterOptions.setLevel("ERROR");
        filterOptions.setStartTime("1712717688587");
        filterOptions.setEndTime("1712718588999");
        filterOptions.setTraceId("dc09d33634def2d212f0b42ecc5ad900");
        List<Map<String, Object>> logData = heraLogApiService.queryLogData(filterOptions);
        log.info("logData:{}", GSON.toJson(logData));
    }


}
