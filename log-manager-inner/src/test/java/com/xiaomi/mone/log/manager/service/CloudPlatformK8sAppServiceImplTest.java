package com.xiaomi.mone.log.manager.service;


import com.xiaomi.mione.tesla.k8s.bo.LogAgentListBo;
import com.xiaomi.mone.cache.redis.RedisClientFactory;
import com.xiaomi.mone.log.manager.model.dto.K8sMachineChangeDTO;
import com.xiaomi.mone.log.manager.model.dto.MetaAppInfoDTO;
import com.xiaomi.mone.log.manager.service.impl.CloudPlatformK8sAppServiceImpl;
import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.xiaomi.mone.log.manager.service.MoneUserDetailService.GSON;
import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;


@Slf4j
public class CloudPlatformK8sAppServiceImplTest {
    private CloudPlatformK8sAppServiceImpl cloudPlatformK8sAppService;

    @Before
    public void buildBean() {
        getConfigFromNanos();
        RedisClientFactory.init();
        Ioc.ins().init("com.xiaomi.mone", "com.xiaomi.youpin", "org.apache.ozhera.log.manager");
        cloudPlatformK8sAppService = Ioc.ins().getBean(CloudPlatformK8sAppServiceImpl.class);
    }

    @Test
    public void testQueryAppInfos() {
        Long appId = 9734L;
        MetaAppInfoDTO metaApp = cloudPlatformK8sAppService.getMetaAppByAppId(appId, false);
        log.info("testQueryTraceAppInfos without instance result:{}", GSON.toJson(metaApp));
        metaApp = cloudPlatformK8sAppService.getMetaAppByAppId(appId, true);
        log.info("testQueryTraceAppInfos with instance result:{}", GSON.toJson(metaApp));
        List<LogAgentListBo> list = metaApp.getPodsForCluster("kscn-tj-serving-k8s", true);
        log.info("testQueryTraceAppInfos alive pod list:{}", GSON.toJson(list));
        list = metaApp.getPodsForCluster("kscn-tj-serving-k8s", false);
        log.info("testQueryTraceAppInfos dead pod list:{}", GSON.toJson(list));
        list = metaApp.getPodsByPodIps(Arrays.asList("*************"));
        log.info("testQueryTraceAppInfos pod list:{}", GSON.toJson(list));
    }

    @Test
    public void testQueryPodListByIp() {
        String hostIp = "**********";
        List<LogAgentListBo> lists = cloudPlatformK8sAppService.queryPodListByIp(hostIp);
        log.info("testQueryPodListByIp result:{}", GSON.toJson(lists));
    }


    @Test
    public void testGetMiKSAppByAppInfo() {
        String machineRoom = "";
        Long appId = 16503L;
        MetaAppInfoDTO.MiKSAppData lists = cloudPlatformK8sAppService.getMiKSAppByAppInfo(appId, machineRoom);
        log.info("testGetMiKSAppByAppInfo result:{}", GSON.toJson(lists));
    }


    @Test
    public void testGetMiKSAppEnvIPsByAppInfo() {
        String envName = "kscn-tj-serving-k8s";
        Long appId = 16503L;
        MetaAppInfoDTO.Env lists = cloudPlatformK8sAppService.getMiKSAppEnvIPsByAppInfo(appId, "", envName);
        log.info("testGetMiKSAppEnvIPsByAppInfo result:{}", GSON.toJson(lists));
    }

    @Test
    public void testQueryAppLogGroups() {
        Long appId = 16503L;
        List<MetaAppInfoDTO.ServerInstance> lists = cloudPlatformK8sAppService.getAppInstancesByLogGroup(appId, "miks", "miks_cluster", "");
        log.info("testQueryAppLogGroups result:{}", GSON.toJson(lists));
    }

    @Test
    public void testHandleK8sTopicTail() {
        String msg = "{\"appId\":19867,\"appType\":6,\"iamTreeId\":14216,\"appName\":\"demo\",\"envName\":\"demo-test\",\"changedMachines\":[{\"podName\":\"demo-test-zgbdb\",\"podIP\":\"**************\",\"nodeName\":\"tj1-matrix-dev-k8s-slave04-20231206.kscn\",\"nodeIP\":\"*************\"}],\"deletingMachines\":[]}";
        K8sMachineChangeDTO machineChangeDTO = GSON.fromJson(msg, K8sMachineChangeDTO.class);
        cloudPlatformK8sAppService.handleK8sTopicTail(machineChangeDTO);
        log.info("testHandleK8sTopicTail end");
    }

}
