package com.xiaomi.mone.log.manager;

import com.xiaomi.mone.log.manager.job.MatrixAppJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class MatrixAppJobTest {
     final MatrixAppJob job = new MatrixAppJob();

    @Test
    public void testCreateTopic() {
        List<String> oldIps = new ArrayList<>();
        oldIps.add("**************");
        oldIps.add("*************");
        oldIps.add("");

        List<String> targetPodIps = new ArrayList<>();
        targetPodIps.add("**************");
        targetPodIps.add("*************");
        targetPodIps.add("************");


        List<String> increasedIps = job.increasedIps(oldIps, targetPodIps);
        List<String> delIps = job.increasedIps(targetPodIps, oldIps);
        log.info("increasedIps:{},delIps:{}", increasedIps, delIps);
    }

    @Test
    public void testEqualCollection() {
        List<String> oldIps = new ArrayList<>();
        oldIps.add("************");
        oldIps.add("*************");
        oldIps.add("**************");

        List<String> targetPodIps = new ArrayList<>();
        targetPodIps.add("**************");
        targetPodIps.add("*************");
        targetPodIps.add("************");


        log.info("equal:{}", CollectionUtils.isEqualCollection(oldIps, targetPodIps));
    }
}
