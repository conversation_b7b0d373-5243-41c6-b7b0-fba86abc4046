package com.xiaomi.mone.log.manager.service;

import com.google.gson.Gson;
import com.xiaomi.infra.galaxy.talos.admin.TalosAdmin;
import com.xiaomi.infra.galaxy.talos.thrift.*;
import com.xiaomi.mone.cache.redis.RedisClientFactory;
import com.xiaomi.mone.log.manager.dao.InnerLogMiddlewareConfigDao;
import com.xiaomi.youpin.docean.Ioc;
import libthrift091.TException;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.manager.model.dto.DictionaryDTO;
import org.apache.ozhera.log.manager.model.pojo.MilogAppMiddlewareRel;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static com.xiaomi.mone.log.manager.service.MoneUserDetailService.GSON;
import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/1/8 17:21
 */
@Slf4j
public class TalosMqConfigServiceTest {

    private TalosMqConfigService talosMqConfigService;

    private InnerLogMiddlewareConfigDao innerLogMiddlewareConfigDao;

    private String ak = "";
    private String sk = "7THi9WIWHfxNv90/fCyjH24XlrqcDh4tgcZO7asb";

    private String serviceUrl = "http://staging-cnbj2-talos.api.xiaomi.net";

    private String orgId = "CL80522";
    private String teamId = "CI112178";

    private Gson gson = new Gson();

    @Before
    public void init() {
        getConfigFromNanos();
        RedisClientFactory.init();
        Ioc.ins().init("com.xiaomi");
        talosMqConfigService = Ioc.ins().getBean(TalosMqConfigService.class);
        innerLogMiddlewareConfigDao = Ioc.ins().getBean(InnerLogMiddlewareConfigDao.class);
    }

    @Test
    public void generateConfigTest() {
        MilogAppMiddlewareRel.Config config = talosMqConfigService.generateConfig(ak, sk,
                null, serviceUrl, "", orgId, teamId, 1L,
                "test", "china", 123L);
        log.info("result:{}", gson.toJson(config));
        Assert.assertNotNull(config);
    }

    @Test
    public void queryTopicList() {
        List<DictionaryDTO> dictionaryDTOS = talosMqConfigService.queryExistsTopic(ak, sk, "", serviceUrl, "", orgId, teamId);
        log.info("result:{}", GSON.toJson(dictionaryDTOS));
    }

    @Test
    public void createTopicTest() throws TException {
        String serviceUrl = "http://ksyru0-fusion-talos.api.xiaomi.net";
        String ak = "";
        String sk = "VxYaZZRQykljpDkzXZD9qR4k33xqtwBtTuHRCTrE";

        String teamId = "CI138196";
        String orgId = "CL61218";


        String topicName = orgId + "/" + "666_mi_i18n_payment_96746_mis";
        Integer partitionNumber = 2;
        TalosAdmin talosAdmin = talosMqConfigService.talosAdminGenerate(serviceUrl, ak, sk);

        List<Topic> topicList = talosAdmin.getTopicList();
        ListTopicGroupResponse listTopicGroupResponse = talosAdmin.listTopicGroup();
        List<String> topics = topicList.stream().map(topic -> topic.getTopicInfo().topicName).toList();
        CreateTopicRequest topicRequest = talosMqConfigService.createTopicRequest(topicName, teamId, partitionNumber);
        CreateTopicResponse talosAdminTopic = talosAdmin.createTopic(topicRequest);
        log.info("result{}", GSON.toJson(talosAdminTopic));
    }

    @Test
    public void queryQuotaTest() throws TException {
        String serviceUrl = "http://staging-cnbj2-talos.api.xiaomi.net";
        String ak = "";
        String sk = "oZaPsv69xg5GBhed+K4wQRRQXeUpoAfdVcXH5esr";

        TalosAdmin talosAdmin = talosMqConfigService.talosAdminGenerate(serviceUrl, ak, sk);

        ListQuotaResponse listQuotaResponse = talosAdmin.listQuota();
        List<PartitionQuotaInfo> quotaList = listQuotaResponse.getQuotaList();
        log.info("result:{}", gson.toJson(quotaList));
    }

    @Test
    public void deleteTopicTest() throws TException {
        String serviceUrl = "http://ksyru0-fusion-talos.api.xiaomi.net";
        String ak = "";
        String sk = "VxYaZZRQykljpDkzXZD9qR4k33xqtwBtTuHRCTrE";
        TalosAdmin talosAdmin = talosMqConfigService.talosAdminGenerate(serviceUrl, ak, sk);
        DeleteTopicRequest deleteTopicRequest = new DeleteTopicRequest();
        String deleteTopicName = "prod_matrix_hera_topic_93117_97525";
        TopicTalosResourceName topicTalosResourceName = getTopicTalosResourceName(talosAdmin, deleteTopicName);
        deleteTopicRequest.setTopicTalosResourceName(topicTalosResourceName);
        talosAdmin.deleteTopic(deleteTopicRequest);
        log.info("deleteTopic success,topicName:{}", deleteTopicName);
    }

    @Test
    public void updateAttributeTest() throws TException {
        String serviceUrl = "http://cnbj1-talos.api.xiaomi.net";
        String ak = "AKS7T6LK4S47PWXHUX";
        String sk = "2Kn6IniE2GAduBitvfin5sSLbk7IcIiH8uazSkPb";
        String topicName = "390298_proretail-car-product_1054881_mone";

        TalosAdmin talosAdmin = talosMqConfigService.talosAdminGenerate(serviceUrl, ak, sk);

        DescribeTopicRequest describeTopicRequest = new DescribeTopicRequest();
        describeTopicRequest.setTopicName(topicName);

        Topic topicInfo = talosAdmin.getTopic(describeTopicRequest);
        TopicAttribute topicAttribute = topicInfo.getTopicAttribute().setPartitionNumber(4);

        TopicTalosResourceName topicTalosResourceName = getTopicTalosResourceName(talosAdmin, topicName);

        ChangeTopicAttributeRequest topicAttributeRequest = new ChangeTopicAttributeRequest(topicTalosResourceName, topicAttribute);
        talosAdmin.changeTopicAttribute(topicAttributeRequest);
        log.info("updateAttribute success");
    }

    @Test
    public void deleteConsumerGroupTest() throws TException {
        String serviceUrl = "http://staging-cnbj2-talos.api.xiaomi.net";
        String ak = "";
        String sk = "7THi9WIWHfxNv90/fCyjH24XlrqcDh4tgcZO7asb";
        String topicName = "common_mq_miLog_CL80522_forth";
        String consumerGroupName = "logSystemGroup14425_car-ins-agent-server_car-ins-agent-server_98060_de";

        TalosAdmin talosAdmin = talosMqConfigService.talosAdminGenerate(serviceUrl, ak, sk);
//        List<Topic> topicInfos = talosAdmin.getTopicList();
//        for (Topic topic : topicInfos) {
//            String topicName = topic.getTopicInfo().topicName;
        TopicTalosResourceName topicTalosResourceName = getTopicTalosResourceName(talosAdmin, topicName);

        QueryConsumerGroupRequest request = new QueryConsumerGroupRequest();
        request.setTopicTalosResourceName(topicTalosResourceName);
        QueryConsumerGroupResponse listTopicGroupResponse = talosAdmin.queryConsumerGroup(request);
        Set<String> consumerGroupList = listTopicGroupResponse.consumerGroupList;
//            List<MilogAppMiddlewareRel> milogAppMiddlewareRels = innerLogMiddlewareConfigDao.queryLogAppMiddlewareRelByTopicName(topicName);
//            List<String> tailIds = milogAppMiddlewareRels.stream().map(MilogAppMiddlewareRel::getTailId).map(String::valueOf).toList();
//            List<String> normalGroups = consumerGroupList.stream().filter(data -> {
//                for (String tailId : tailIds) {
//                    if (data.endsWith(tailId) || data.endsWith(tailId + "_backup")) {
//                        return true;
//                    }
//                }
//                return false;
//            }).toList();
        List<String> delGroups = consumerGroupList.stream().toList();
        for (String delGroup : delGroups) {
            try {
                talosAdmin.deleteConsumerGroup(new DeleteConsumerGroupRequest(delGroup, topicTalosResourceName));
                log.info("deleteConsumerGroup success,topic:{},consumerGroup:{}", topicName, delGroup);
            } catch (Exception e) {
                log.error("deleteConsumerGroup error", e);
            }
        }
//        }
        log.info("success");
    }

    private TopicTalosResourceName getTopicTalosResourceName(TalosAdmin talosAdmin, String topicName) throws TException {
        GetDescribeInfoResponse response = talosAdmin.getDescribeInfo(
                new GetDescribeInfoRequest(topicName));
        TopicTalosResourceName resourceName = response.getTopicTalosResourceName();
        return resourceName;
    }

    @Test
    public void queryMorePartitionTest() throws TException {
        TalosAdmin talosAdmin = talosMqConfigService.talosAdminGenerate(serviceUrl, ak, sk);
        List<Topic> topicList = talosAdmin.getTopicList();
        List<String> topicNames = new ArrayList<>();
        for (Topic topic : topicList) {
            int partitionNumber = topic.topicAttribute.getPartitionNumber();
            if (partitionNumber > 30) {
                topicNames.add(topic.topicInfo.topicName);
            }
        }
        log.info("result:{}", topicNames);
    }
}
