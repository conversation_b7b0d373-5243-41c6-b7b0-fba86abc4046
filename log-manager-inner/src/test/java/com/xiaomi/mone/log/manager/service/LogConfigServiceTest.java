package com.xiaomi.mone.log.manager.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.nacos.api.config.ConfigFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import com.xiaomi.data.push.nacos.NacosNaming;
import com.xiaomi.mone.cache.redis.RedisClientFactory;
import com.xiaomi.mone.log.manager.nacos.StreamBalanceWeight;
import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.api.enums.OperateEnum;
import org.apache.ozhera.log.common.Result;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.dao.MilogLogstoreDao;
import org.apache.ozhera.log.manager.model.pojo.MilogLogStoreDO;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;
import org.apache.ozhera.log.manager.service.impl.LogTailServiceImpl;
import org.apache.ozhera.log.manager.service.nacos.MultipleNacosConfig;
import org.apache.ozhera.log.manager.service.nacos.impl.StreamConfigNacosProvider;
import org.apache.ozhera.log.model.MiLogStreamConfig;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.io.IOException;
import java.util.List;

import static com.xiaomi.mone.log.manager.service.MoneUserDetailService.GSON;
import static org.apache.ozhera.log.common.Constant.DEFAULT_GROUP_ID;
import static org.apache.ozhera.log.common.Constant.DEFAULT_TIME_OUT_MS;
import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;


/**
 * <AUTHOR>
 * @version 1.0
 * @description 配置相关的单元测试
 * @date 2023/8/24 10:30
 */
@Slf4j
public class LogConfigServiceTest {
    private LogTailServiceImpl logTailService;
    private MilogLogstoreDao milogLogstoreDao;
    private MilogLogTailDao milogLogTailDao;

    @Before
    public void init() {
        getConfigFromNanos();
        RedisClientFactory.init();
        Ioc.ins().init("com.xiaomi");
        logTailService = Ioc.ins().getBean(LogTailServiceImpl.class);
        milogLogstoreDao = Ioc.ins().getBean(MilogLogstoreDao.class);
        milogLogTailDao = Ioc.ins().getBean(MilogLogTailDao.class);
    }

    @Test
    public void testDelLogTail() throws IOException {
        Result<Void> delResult = logTailService.deleteLogTail(203L);
        Assert.assertNotNull(delResult);
        System.in.read();
    }


    @Test
    public void testQueryConfigFromNacos() throws NacosException {
        StreamConfigNacosProvider nacosProvider = new StreamConfigNacosProvider();
        ConfigService configService = ConfigFactory.createConfigService("nacos.india.pro.mi.com:80");
        nacosProvider.setConfigService(configService);
        MiLogStreamConfig config = nacosProvider.getConfig(60022L);
        log.info(GSON.toJson(config));
        Assert.assertNull(config);
    }

    @Test
    public void testSendToStream() {
        Long storeId = 90316L;
        Long tailId = 90679L;
        MilogLogStoreDO milogLogstoreDO = milogLogstoreDao.queryById(storeId);
        MilogLogTailDo mt = milogLogTailDao.queryById(tailId);
        logTailService.handleNacosConfigByMotorRoom(mt, milogLogstoreDO.getMachineRoom(), OperateEnum.UPDATE_OPERATE.getCode(), mt.getAppType());
    }

    @Test
    public void streamBalanceTest() throws NacosException {
        String nacosAddr = "nacos.test.b2c.srv:80";
        String logStreamName = "inner_hera_log_stream";
        String dataId = "hera_log_stream_balance";
        ConfigService configService = ConfigFactory.createConfigService(nacosAddr);
        NacosNaming nacosNaming = MultipleNacosConfig.getNacosNaming(nacosAddr);

        List<Instance> instanceList = nacosNaming.getAllInstances(logStreamName);
        List<StreamBalanceWeight> streamBalanceWeights = Lists.newArrayList();
        for (Instance instance : instanceList) {

            StreamBalanceWeight streamBalanceWeight = new StreamBalanceWeight();
            StreamBalanceWeight.ActualUniqueRel actualUniqueRel = new StreamBalanceWeight.ActualUniqueRel();
            actualUniqueRel.setIp(instance.getIp());
            actualUniqueRel.setUniqueId(instance.getIp());
            streamBalanceWeight.setActualUnique(actualUniqueRel);
            streamBalanceWeight.setWeight(0.1);
            streamBalanceWeights.add(streamBalanceWeight);
        }
        configService.publishConfig(dataId, DEFAULT_GROUP_ID, GSON.toJson(streamBalanceWeights));
        log.info("instanceList:{}", instanceList);
    }

    @Test
    public void streamReBalanceTest() throws NacosException {
        String nacosAddr = "nacos.test.b2c.srv:80";
        String logStreamName = "inner_hera_log_stream";
        String dataId = "hera_log_stream_balance";
        ConfigService configService = ConfigFactory.createConfigService(nacosAddr);
        NacosNaming nacosNaming = MultipleNacosConfig.getNacosNaming(nacosAddr);

        String config = configService.getConfig(dataId, DEFAULT_GROUP_ID, DEFAULT_TIME_OUT_MS);
        List<StreamBalanceWeight> streamBalanceWeights = GSON.fromJson(config, new TypeToken<List<StreamBalanceWeight>>() {
        }.getType());

        //1.第一步分配(简单分配)，查看一共有多少tail,平均
        List<Long> allIds = milogLogTailDao.queryAllIds();
        List<List<Long>> splitTailIds = CollectionUtil.split(allIds, streamBalanceWeights.size());
        for (List<Long> tailIds : splitTailIds) {

        }
        //2.

        System.out.println("test");
    }

    @Test
    public void test() {
        List<String> ids = Lists.newArrayList("1", "2", "3");
        List<List<String>> split = CollectionUtil.split(ids, 3);
        System.out.println(split);
    }
}
