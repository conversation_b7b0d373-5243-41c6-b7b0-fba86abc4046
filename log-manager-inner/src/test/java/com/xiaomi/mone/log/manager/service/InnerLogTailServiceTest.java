package com.xiaomi.mone.log.manager.service;

import com.google.common.collect.Lists;
import com.xiaomi.mione.tesla.k8s.bo.LogAgentListBo;
import com.xiaomi.mone.log.manager.model.dto.DeploySystemAppsDTO.DeploySystemAppData;
import com.xiaomi.mone.log.manager.model.dto.MatrixAppsDTO.MatrixAppData;
import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.common.Result;
import org.apache.ozhera.log.manager.dao.MilogLogTailDao;
import org.apache.ozhera.log.manager.model.pojo.MilogLogTailDo;
import org.junit.Before;
import org.junit.Test;

import java.util.List;
import java.util.Map;

import static com.xiaomi.mone.log.manager.service.MoneUserDetailService.GSON;
import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;


@Slf4j
public class InnerLogTailServiceTest {

    private InnerLogTailService innerLogTailService;

    private MilogLogTailDao milogLogTailDao;

    @Before
    public void buildBean() {
        getConfigFromNanos();
        Ioc.ins().init("com.xiaomi");
        innerLogTailService = Ioc.ins().getBean(InnerLogTailService.class);
        milogLogTailDao = Ioc.ins().getBean(MilogLogTailDao.class);
    }

    @Test
    public void testGetMatrixDeploySpaceByAppId() {
        Long appId = 663L;
        Result<MatrixAppData> matrixAppDataResult = innerLogTailService.getMatrixDeploySpaceByAppId(
                appId, "staging");
        log.info("matrixAppDataResult result:{}", GSON.toJson(matrixAppDataResult));
    }

    @Test
    public void testGetDeploySystemClusterByAppId() {
        Long appId = 1450L;
        Result<DeploySystemAppData> deploySystemClusterResult = innerLogTailService.getDeploySystemClusterByAppId(
                appId, "staging");
        log.info("deploySystemClusterResult result:{}", GSON.toJson(deploySystemClusterResult));
    }

    @Test
    public void testGetLogAgentIP() {
        List<String> podIp = Lists.newArrayList("**************");
        Map<String, List<LogAgentListBo>> stringListMap = innerLogTailService.queryAgentIpByPodIps(podIp);
        log.info("");
    }

    @Test
    public void testInsert() {
        MilogLogTailDo milogLogTailDo = milogLogTailDao.queryById(4L);
        milogLogTailDo.setCollectionReady(false);
        milogLogTailDao.update(milogLogTailDo);
    }
}
