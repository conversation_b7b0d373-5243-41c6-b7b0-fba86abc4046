package com.xiaomi.mone.log.manager;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.xiaomi.youpin.docean.plugin.es.EsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ozhera.log.api.enums.OperateEnum;
import org.apache.ozhera.log.manager.domain.EsCluster;
import org.apache.ozhera.log.manager.service.extension.common.CommonExtensionServiceFactory;
import org.apache.ozhera.log.model.MiLogStreamConfig;
import org.elasticsearch.client.RestHighLevelClient;
import org.junit.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.apache.ozhera.log.common.Constant.TAIL_CONFIG_DATA_ID;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/6/3 11:13
 */
@Slf4j
public class EsTest extends BaseTest {

    private EsCluster esCluster;

    @Test
    public void testEsSql() {
        esCluster = ioc.getBean(EsCluster.class);
        EsService esService = esCluster.getEsService(1L);
        RestHighLevelClient client = esService.getEsClient().getEsOriginalClient();

        // 构建SQL查询请求
//        SearchRequest searchRequest = new SearchRequest();
//        searchRequest.indices("my_index"); // 替换为您的索引名称
//        searchRequest.source(new SearchSourceBuilder()
//                .query(QueryBuilders.sqlQuery("SELECT * FROM my_index LIMIT 10"))
//                .fetchSource(true));
//
//        // 执行SQL查询
//        SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
        List<String> ipList = Lists.newArrayList();
        ipList.add("***********");
    }

    private synchronized MiLogStreamConfig dealStreamConfigByRule(List<String> ipList, Long spaceId, Integer type) {
        String str = "{\"config\":{\"shgs1-nc4-303.ip\":{\"43\":\"log_manage_create_tail_config:43\",\"90016\":\"log_manage_create_tail_config:90016\",\"188876\":\"log_manage_create_tail_config:188876\",\"90195\":\"log_manage_create_tail_config:90195\",\"309053\":\"log_manage_create_tail_config:309053\",\"309062\":\"log_manage_create_tail_config:309062\",\"90043\":\"log_manage_create_tail_config:90043\",\"309129\":\"log_manage_create_tail_config:309129\",\"309317\":\"log_manage_create_tail_config:309317\",\"90040\":\"log_manage_create_tail_config:90040\",\"309341\":\"log_manage_create_tail_config:309341\",\"309347\":\"log_manage_create_tail_config:309347\",\"309356\":\"log_manage_create_tail_config:309356\",\"309391\":\"log_manage_create_tail_config:309391\",\"309397\":\"log_manage_create_tail_config:309397\",\"309411\":\"log_manage_create_tail_config:309411\",\"90045\":\"log_manage_create_tail_config:90045\",\"309377\":\"log_manage_create_tail_config:309377\"}}}";
        MiLogStreamConfig existConfig = new Gson().fromJson(str, MiLogStreamConfig.class);
        // New configuration
        String spaceKey = CommonExtensionServiceFactory.getCommonExtensionService().getLogManagePrefix() + TAIL_CONFIG_DATA_ID + spaceId;
        if (null == existConfig || OperateEnum.ADD_OPERATE.getCode().equals(type) || OperateEnum.UPDATE_OPERATE.getCode().equals(type)) {
            // The configuration is not configured yet, initialize the configuration
            if (null == existConfig) {
                existConfig = new MiLogStreamConfig();
                Map<String, Map<Long, String>> config = new HashMap<>();
                boolean idAdd = false;
                for (String ip : ipList) {
                    Map<Long, String> map = Maps.newHashMapWithExpectedSize(1);
                    if (!idAdd) {
                        map.put(spaceId, spaceKey);
                        idAdd = true;
                    }
                    config.put(ip, map);
                }
                existConfig.setConfig(config);
            } else {
                Map<String, Map<Long, String>> config = existConfig.getConfig();
                if (config.values().stream()
                        .flatMap(longStringMap -> longStringMap.values().stream())
                        .anyMatch(s -> s.equals(spaceKey))) {
                    return existConfig;
                }
                // 1.Average the quantity first
                // 2.Added name Space
                if (CollectionUtils.isNotEmpty(ipList)) {
                    for (String ip : ipList) {
                        if (!config.containsKey(ip)) {
                            config.put(ip, Maps.newHashMap());
                        }
                    }
                }
                // The number of name spaces held per machine
                Map<String, Integer> ipSizeMap = config.entrySet().stream()
                        .collect(Collectors.toMap(Map.Entry::getKey, stringMapEntry -> stringMapEntry.getValue().size()));
                String key = ipSizeMap.entrySet().stream()
                        .filter(entry -> ipList.contains(entry.getKey()))
                        .min(Map.Entry.comparingByValue()).get().getKey();
                config.get(key).put(spaceId, spaceKey);
            }
        }
        // Delete the configuration
        if (OperateEnum.DELETE_OPERATE.getCode().equals(type)) {
            if (null != existConfig) {
                Map<String, Map<Long, String>> config = existConfig.getConfig();
                config.values().forEach(longStringMap -> longStringMap.keySet().removeIf(key -> key.equals(spaceId)));
            }
//            spaceConfigNacosPublisher.remove(spaceId.toString());
        }
        return existConfig;
    }
}
