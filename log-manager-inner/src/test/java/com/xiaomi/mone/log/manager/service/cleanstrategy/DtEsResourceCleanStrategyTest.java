package com.xiaomi.mone.log.manager.service.cleanstrategy;

import com.xiaomi.mone.log.manager.enums.ClearStageEnum;
import com.xiaomi.mone.log.manager.model.vo.ClearDtResourceParam;
import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.UUID;

import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;

/**
 * @author: songyutong1
 * @date: 2024/09/14/11:28
 */
@Slf4j
public class DtEsResourceCleanStrategyTest {

    private AbstractCleanStrategy dtEsResourceCleanStrategy;
    private String uuid;
    private ClearDtResourceParam param = new ClearDtResourceParam();

    @Before
    public void buildUp() {
        getConfigFromNanos();
        Ioc.ins().init("com.xiaomi");
        dtEsResourceCleanStrategy = CleanStrategyFactory.getCleanStrategy(ClearStageEnum.STAGE_6);
        uuid = UUID.randomUUID().toString();
        param.setMachineRoom("cn");
        param.setClearFlag(false);
        param.setToken("ecd4fcf210004296945800466e263de5");
        param.setFlinkJobToken("0d529701f8354e55a2b058c42f7773b0");
    }

    @Test
    public void testClean() {
        try {
            dtEsResourceCleanStrategy.clean(param, uuid);
        } catch (Exception e) {
            log.error("DtEsResourceCleanStrategyTest.testClean error, err={}", e.getMessage(), e);
            Assert.fail();
        }
    }

}
