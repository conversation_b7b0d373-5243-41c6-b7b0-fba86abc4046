package com.xiaomi.mone.log.manager.service;

import com.google.gson.Gson;
import com.xiaomi.mone.log.manager.service.impl.MilogAppTopicServiceImpl;
import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.common.Result;
import org.apache.ozhera.log.manager.model.bo.AccessMilogParam;
import org.apache.ozhera.log.manager.model.vo.AccessMiLogVo;
import org.junit.Before;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/6/8 20:01
 */
@Slf4j
public class MilogAppTopicServiceTest {

    private MilogAppTopicServiceImpl milogAppTopicService;
    private Gson gson;

    @Before
    public void before() {
        Ioc.ins().init("com.xiaomi");
        milogAppTopicService = Ioc.ins().getBean(MilogAppTopicServiceImpl.class);
        gson = new Gson();
    }

    @Test
    public void testAccessLog() {
        String str = "{\"spaceName\":\"mifaas_space_all\",\"storeName\":\"mone\",\"appId\":301305,\"appName\":\"wtt-log-test\",\"appCreator\":\"wangtao29\",\"appCreatTime\":1686215803518,\"funcId\":1153,\"funcName\":\"testLog\",\"logPath\":\"/home/<USER>/log/mifaas/server.log\",\"appType\":4,\"appTypeText\":\"serverLess\",\"envName\":\"wtt-log-test#1153#92247\",\"envId\":10803086,\"machineRoom\":\"cn\"}";
        AccessMilogParam milogParam = gson.fromJson(str, AccessMilogParam.class);
        Result<AccessMiLogVo> accessMiLogVoResult = milogAppTopicService.accessToMilog(milogParam);
        log.info("result；{}", gson.toJson(accessMiLogVoResult));
    }
}
