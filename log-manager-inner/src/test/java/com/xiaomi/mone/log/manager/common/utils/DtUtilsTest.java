package com.xiaomi.mone.log.manager.common.utils;


import com.google.gson.Gson;
import com.xiaomi.bigdata.workshop.model.WsTableDTO;
import com.xiaomi.youpin.docean.Ioc;
import java.util.ArrayList;
import java.util.List;
import junit.framework.TestCase;
import org.apache.ozhera.log.manager.model.dto.DTTableGetResponseDTO;
import org.junit.Before;
import org.junit.Test;

import static com.xiaomi.mone.log.manager.user.MoneUserDetailService.GSON;
import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;

public class DtUtilsTest extends TestCase {
    private Gson gson = new Gson();

    @Before
    public void pushBean() {
        getConfigFromNanos();
        Ioc.ins().init("com.xiaomi");
    }

    @Test
    public void testGet() {
        String url = "/openapi/metadata/table/get?catalog=es_zjy_log&dbName=default&tableNameEn=c3_serving_k8s_trace_demo_client_production_3368";
        String token = "";
        DTTableGetResponseDTO result = DtUtils.get(url, token, DTTableGetResponseDTO.class);
        System.out.println(result.toString());
    }

    @Test
    public void testPost() {
        String url = "/openapi/metadata/table/table/delete";
        String token = "";
        List<WsTableDTO> dtoList = new ArrayList<>();
        WsTableDTO wsTableDTO = new WsTableDTO();
        wsTableDTO.setCatalog("talos_cnbj4_talos");
        wsTableDTO.setDbName("default");
        wsTableDTO.setTableNameEn("prod_matrix_hera_topic_92617_95913");
        dtoList.add(wsTableDTO);
        String body = gson.toJson(dtoList);
        boolean result = DtUtils.post(url, body, token, boolean.class);
        System.out.println("删除结果：" + result);
    }

    @Test
    public void testDeleteIndex() {
        String url = DtUtils.URL_TABLE_DELETE;
        List<WsTableDTO> wsTableDTOS = new ArrayList<>();
        String indexName = "prod_hera_index_92645";
        String catalog = "es_sh2_nc4cloud";
        String dbName = "default";
        String token = "1485ffdc8d894bada93ac419ac968ae0";

        WsTableDTO wsTableDTO = new WsTableDTO().catalog(catalog).dbName(dbName).tableNameEn(indexName);
        wsTableDTOS.add(wsTableDTO);
        String body = GSON.toJson(wsTableDTOS);
        Boolean result = DtUtils.post(url, body, token, Boolean.class);
        System.out.println("result:" + result);
    }

}