package com.xiaomi.mone.log.manager.service;

import com.google.gson.Gson;
import com.xiaomi.mone.cache.redis.RedisClientFactory;
import com.xiaomi.mone.log.manager.service.impl.DefaultLogDiscoverService;
import com.xiaomi.youpin.docean.Ioc;
import com.xiaomi.youpin.docean.common.Cons;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.manager.model.vo.LogQuery;
import org.elasticsearch.action.fieldcaps.FieldCapabilities;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

import static com.xiaomi.mone.log.manager.service.MoneUserDetailService.GSON;
import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025/1/2 16:38
 */
@Slf4j
public class LogDiscoverServiceTest {

    private LogDiscoverService logDiscoverService;

    private Gson gson = new Gson();


    @Before
    public void init() {
        getConfigFromNanos();
        RedisClientFactory.init();
        Ioc.ins().putBean(Cons.AUTO_FIND_IMPL, "true").init("com.xiaomi.mone", "com.xiaomi.youpin", "org.apache.ozhera.log.manager");
        logDiscoverService = Ioc.ins().getBean(DefaultLogDiscoverService.class);
    }


    @Test
    public void getAggregateFieldsTest() {
        String msg = "{\"storeId\":120929,\"startTime\":1735806320677,\"endTime\":1735807220677,\"fullTextSearch\":\"\"}";
        LogQuery logQuery = GSON.fromJson(msg, LogQuery.class);
        List<FieldCapabilities> aggregateFields = logDiscoverService.getAggregateFields(logQuery);
        log.info("aggregateFields: {}", gson.toJson(aggregateFields));
        Assert.assertNotNull(aggregateFields);
    }
}
