package com.xiaomi.mone.log.manager.service;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.text.csv.CsvData;
import cn.hutool.core.text.csv.CsvReadConfig;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.util.CharsetUtil;
import com.alibaba.nacos.api.config.ConfigFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.xiaomi.youpin.docean.plugin.nacos.NacosNaming;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.model.MiLogStreamConfig;
import org.junit.Before;
import org.junit.Test;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.xiaomi.mone.log.manager.service.MoneUserDetailService.GSON;
import static org.apache.ozhera.log.common.Constant.DEFAULT_GROUP_ID;
import static org.apache.ozhera.log.common.Constant.DEFAULT_TIME_OUT_MS;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/10/19 11:31
 */
@Slf4j
public class CsvFileTest {

    private List<Long> spaceIds;

    private List<String> dataIdList;

    private ConfigService configService;

    private NacosNaming nacosNaming;

    private String nacosAddress = "nacos.systech.b2c.srv:80";

    @Test
    public void testConfig() throws NacosException {
        for (String dataId : dataIdList) {
            configService.removeConfig(dataId, DEFAULT_GROUP_ID);
        }
    }


    @Before
    public void init() throws NacosException {
        CsvReadConfig config = new CsvReadConfig();
        config.setContainsHeader(true);
        CsvReader reader = new CsvReader(config);

        configService = ConfigFactory.createConfigService(nacosAddress);

        nacosNaming = new NacosNaming();
        nacosNaming.setServerAddr(nacosAddress);
        nacosNaming.init();

        CsvData data = reader.read(ResourceUtil.getReader("milog.milog_logstail.20240417151551750.csv", CharsetUtil.CHARSET_UTF_8));
        CsvData dataIdData = reader.read(ResourceUtil.getReader("zeus_2024_11_26_config_info_1732610469207.csv", CharsetUtil.CHARSET_UTF_8));

        spaceIds = data.getRows().stream()
                .mapToLong(row -> Long.parseLong(row.get(0)))
                .boxed()
                .collect(Collectors.toList());

        dataIdList = dataIdData.getRows().stream()
                .map(row -> row.get(0))
                .collect(Collectors.toList());
    }

    @Test
    public void testPubMifeConfig() throws NacosException {

        Map<Long, String> mifeTailConfig = spaceIds.stream()
                .collect(Collectors.toMap(Long::valueOf,
                        value -> String.format("%s:%d", "log_manage_create_tail_config", value)));
        MiLogStreamConfig mifeStreamConfig = new MiLogStreamConfig();
        mifeStreamConfig.getConfig().put("mife", mifeTailConfig);
        log.info("result:{}", GSON.toJson(mifeStreamConfig));

        String mife_data_id = "log_manage_create_namespace_config_mife";
        configService.publishConfig(mife_data_id, DEFAULT_GROUP_ID, GSON.toJson(mifeStreamConfig));
    }


    @Test
    public void testRemoveConfig() throws NacosException {
        String old_data_Id = "log_manage_create_namespace_config";
        String config = configService.getConfig(old_data_Id, DEFAULT_GROUP_ID, DEFAULT_TIME_OUT_MS);
        MiLogStreamConfig miLogStreamConfig = GSON.fromJson(config, MiLogStreamConfig.class);

        for (Long spaceId : spaceIds) {
            Map<String, Map<Long, String>> configConfig = miLogStreamConfig.getConfig();
            Iterator<Map<Long, String>> iterator = configConfig.values().iterator();
            while (iterator.hasNext()) {
                Map<Long, String> configMap = iterator.next();
                configMap.remove(spaceId);
            }
        }
        log.info("res result:{}", GSON.toJson(miLogStreamConfig));
    }

    @Test
    public void testBackupConfig() throws NacosException {
        String oldDataId = "log_manage_create_namespace_config";
        String config = configService.getConfig(oldDataId, DEFAULT_GROUP_ID, DEFAULT_TIME_OUT_MS);
        MiLogStreamConfig miLogStreamConfig = GSON.fromJson(config, MiLogStreamConfig.class);
        Map<String, Map<Long, String>> streamConfig = miLogStreamConfig.getConfig();

        String matrixDataId = "log_manage_create_namespace_config_matrix";
        String matrixConfig = configService.getConfig(matrixDataId, DEFAULT_GROUP_ID, DEFAULT_TIME_OUT_MS);
        MiLogStreamConfig matrixLogStreamConfig = GSON.fromJson(matrixConfig, MiLogStreamConfig.class);
        Map<String, Map<Long, String>> matrixStreamConfig = matrixLogStreamConfig.getConfig();

        Map<Long, String> backupConfig = new ConcurrentHashMap<>();
        combineConfig(streamConfig, backupConfig);
        combineConfig(matrixStreamConfig, backupConfig);

        String serviceName = "inner_hera_log_stream_backup";
        List<Instance> allInstances = nacosNaming.getAllInstances(serviceName);
        List<String> ipList = allInstances.stream().map(Instance::getIp).toList();
        Set<Long> keyedSet = backupConfig.keySet();
        List<Long> keyList = new ArrayList<>(keyedSet);
        List<List<Long>> splitList = ListUtil.splitAvg(keyList, ipList.size());

        MiLogStreamConfig backupMiLogStreamConfig = new MiLogStreamConfig();

        for (int i = 0; i < ipList.size(); i++) {
            String ip = ipList.get(i);
            List<Long> perKeyList = splitList.get(i);
            Map<Long, String> backupConfigPer = new ConcurrentHashMap<>();
            perKeyList.forEach(key -> backupConfigPer.put(key, backupConfig.get(key)));
            backupMiLogStreamConfig.getConfig().put(ip, backupConfigPer);
        }

        String newDataId = "log_manage_create_namespace_config_backup";
        configService.publishConfig(newDataId, DEFAULT_GROUP_ID, GSON.toJson(backupMiLogStreamConfig));
    }

    private void combineConfig(Map<String, Map<Long, String>> sourceConfig, Map<Long, String> targetConfig) {
        for (Map<Long, String> values : sourceConfig.values()) {
            targetConfig.putAll(values);
        }
    }

    @Test
    public void mergeConfigTest() throws NacosException {
        String data_Id1 = "log_manage_create_namespace_config";
        MiLogStreamConfig miLogStreamConfig = getMiLogStreamConfig(data_Id1);
        MiLogStreamConfig miLogStreamConfig2 = getMiLogStreamConfig("log_manage_create_namespace_config_matrix");
        for (Map.Entry<String, Map<Long, String>> mapEntry : miLogStreamConfig2.getConfig().entrySet()) {
            Map<Long, String> value = mapEntry.getValue();
            miLogStreamConfig.getConfig().get(mapEntry.getKey()).putAll(value);
        }
        log.info("est");
        configService.publishConfig(data_Id1, DEFAULT_GROUP_ID, GSON.toJson(miLogStreamConfig));

    }


    public MiLogStreamConfig getMiLogStreamConfig(String dataId) throws NacosException {
        String config = configService.getConfig(dataId, DEFAULT_GROUP_ID, DEFAULT_TIME_OUT_MS);
        MiLogStreamConfig miLogStreamConfig = GSON.fromJson(config, MiLogStreamConfig.class);
        return miLogStreamConfig;
    }

}
