package com.xiaomi.mone.log.manager;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;

import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;


/**
 *
 * @description
 * @version 1.0
 * <AUTHOR>
 * @date 2024/5/31 11:15
 *
 */
@Slf4j
public class BaseTest {

    public Ioc ioc;

    @Before
    public void init() {
        getConfigFromNanos();
        Ioc.ins().init("com.xiaomi");
        ioc = Ioc.ins();
    }

    private static final ThreadLocal<String> threadLocal = new ThreadLocal<>();
    private static final ThreadLocal<String> threadLocal1 = new ThreadLocal<>();

    @Test
    public void test1(){
        Snowflake snowflake = IdUtil.getSnowflake();
        for (int i = 0; i < 100; i++) {
            log.info("id:{}", snowflake.nextId());
        }
    }

    @Test
    public void threadLocalTest() {
        threadLocal.set("123");
        threadLocal.set("456");
        threadLocal1.set("789");
        new Thread(() -> {
            threadLocal.set("789");
            log.info("threadLocal:{}", threadLocal.get());
        }).start();
        log.info("threadLocal:{}", threadLocal.get());
    }

}
