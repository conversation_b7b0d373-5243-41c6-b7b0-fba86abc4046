package com.xiaomi.mone.log.manager;

import com.xiaomi.mone.log.manager.model.dto.HdfsDTO;
import com.xiaomi.mone.log.manager.model.dto.HdfsDetailDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/11/13 15:23
 */
@Slf4j
public class IcebergTest {

    private static final String alphaJdbc = "************************************************************";
    private static final String alphaCatalog = "hive_zjyprc_hadoop";
    private static final String alphaDb = "mi_log";
    private static final String alphaEngine = "proxy.engine=presto";
    private static final String alphaToken = "5b1c8f4fe25649b8b208352068cc5e68";
    private static final String alphatableName = "milog_talos_sink_data_prd";


    @Test
    public void queryHiveData() {

        String date = "20";
        String traceid = "r34r34";

        HdfsDTO hdfsDTO = new HdfsDTO();
        List<HdfsDetailDto> list = new ArrayList<>();
        Statement statement = null;
        Connection connection = null;
        ResultSet resultSet;
        String url = alphaJdbc + "/" + alphaCatalog + "/" + alphaDb + "?" + alphaEngine;
        String hql = "SELECT * " +
                " FROM " + alphaCatalog + "." + alphaDb + "." + alphatableName +
                " where date=" + date + " and  " +
                " traceid= '" + traceid + "'" +
                " LIMIT 5";
        log.info("hql:{}", hql);
        try {
            connection = DriverManager.getConnection(url, alphaToken, "");
            statement = connection.createStatement();
            resultSet = statement.executeQuery(hql);
            while (resultSet.next()) {
                HdfsDetailDto hdfsDetailDto = new HdfsDetailDto();
                hdfsDetailDto.setTraceId(String.valueOf(resultSet.getObject(resultSet.findColumn("traceid"))));
                hdfsDetailDto.setOther(String.valueOf(resultSet.getObject(resultSet.findColumn("other"))));
                hdfsDetailDto.setLevel(String.valueOf(resultSet.getObject(resultSet.findColumn("level"))));
                hdfsDetailDto.setAppName(String.valueOf(resultSet.getObject(resultSet.findColumn("appname"))));
                hdfsDetailDto.setTail(String.valueOf(resultSet.getObject(resultSet.findColumn("tail"))));
                hdfsDetailDto.setClassName(String.valueOf(resultSet.getObject(resultSet.findColumn("classname"))));
                hdfsDetailDto.setMessage(String.valueOf(resultSet.getObject(resultSet.findColumn("message"))));
                hdfsDetailDto.setLogstore(String.valueOf(resultSet.getObject(resultSet.findColumn("logsource"))));
                hdfsDetailDto.setThreadName(String.valueOf(resultSet.getObject(resultSet.findColumn("threadname"))));
                hdfsDetailDto.setMqtopic(String.valueOf(resultSet.getObject(resultSet.findColumn("mqtopic"))));
                hdfsDetailDto.setLogstore(String.valueOf(resultSet.getObject(resultSet.findColumn("logstore"))));
                hdfsDetailDto.setTimestamp(String.valueOf(resultSet.getObject(resultSet.findColumn("timestamp"))));
                list.add(hdfsDetailDto);
            }
            hdfsDTO.setList(list);
        } catch (Exception e) {
            log.error("exception queryHiveData:{}", e);
        } finally {
            try {
                if (statement != null) {
                    statement.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (Exception e) {
                log.error("alpha query exception:{}", e);
            }
        }
    }
}

