package com.xiaomi.mone.log.manager.service;

import com.xiaomi.youpin.docean.Ioc;
import com.xiaomi.youpin.docean.common.Cons;
import lombok.extern.slf4j.Slf4j;
import org.apache.ozhera.log.manager.service.MilogMiddlewareConfigService;
import org.apache.ozhera.log.manager.service.impl.MilogMiddlewareConfigServiceImpl;
import org.junit.Before;
import org.junit.Test;

import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/9/9 16:58
 */
@Slf4j
public class LogMiddlewareConfigServiceTest {

    private MilogMiddlewareConfigService milogMiddlewareConfigService;

    @Before
    public void init() {
        getConfigFromNanos();
        Ioc.ins().putBean(Cons.AUTO_FIND_IMPL, "true").init("com.xiaomi.mone", "com.xiaomi.youpin");

        milogMiddlewareConfigService = Ioc.ins().getBean(MilogMiddlewareConfigServiceImpl.class);
    }

    @Test
    public void synchronousResourceLabelTest() {
        milogMiddlewareConfigService.synchronousResourceLabel(2L);
    }
}
