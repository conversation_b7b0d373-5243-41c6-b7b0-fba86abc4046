package com.xiaomi.mone.log.manager.service;

import com.xiaomi.mone.log.manager.dao.alert.AlertDao;
import com.xiaomi.mone.log.manager.model.alert.Alert;
import com.xiaomi.mone.log.manager.model.bo.RegexMatchRes;
import com.xiaomi.mone.log.manager.model.bo.alert.AlertMsgPattern;
import com.xiaomi.mone.log.manager.service.alert.AlertLogService;
import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.xiaomi.mone.log.manager.service.MoneUserDetailService.GSON;
import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;


/**
 * @author: wtt
 * @date: 2022/6/2 10:57
 * @description:
 */
@Slf4j
public class AlertLogServiceTest {

    private AlertDao alertDao;

    private AlertLogService alertLogService;

    private final String regex = "^(?=.*(?i:-csw-|-6csw-|-dwdm-|-lvs-|-base-|-fw-))(?=.*(?:BGP|OSPF)(?!.*(?i:full|establish))).*$";
    private final String message = "[2023-11-15-00-04][Nov 15 00:04:43][************][zjy-206-e15-lan-lvs-h6850-01.bj] %%10OSPF/5/OSPF_NBR_CHG: -DevIP=************; OSPF 700 Neighbor *************(Twenty-FiveGigE1/0/1) changed from LOADING to FULL.";


    @Before
    public void init() {
        getConfigFromNanos();
        Ioc.ins().init("com.xiaomi");
        alertDao = Ioc.ins().getBean(AlertDao.class);
        alertLogService = Ioc.ins().getBean(AlertLogService.class);
    }

    @Test
    public void testPattern() {

        final Pattern pattern = Pattern.compile(regex, Pattern.MULTILINE);
        final Matcher matcher = pattern.matcher(message);

        while (matcher.find()) {
            System.out.println("Full match: " + matcher.group(0));

            for (int i = 1; i <= matcher.groupCount(); i++) {
                System.out.println("Group " + i + ": " + matcher.group(i));
            }
        }
    }

    @Test
    public void testQueryByTailId() {
        Long tailId = 4L;
        List<Alert> alertList = alertDao.queryByTailId(tailId);
        log.info("result:{}", GSON.toJson(alertList));
    }

    @Test
    public void testRegexMatch() {
        AlertMsgPattern alertMsgPattern = new AlertMsgPattern();
        alertMsgPattern.setMessage(message);
        alertMsgPattern.setRule(regex);
        RegexMatchRes regexMatchParam = alertLogService.checkRegexMatch(alertMsgPattern);
        if (regexMatchParam.isMatch()) {
            for (RegexMatchRes.RegexRes regexRes : regexMatchParam.getResList()) {
                log.info("match str:{}", message.substring(regexRes.getStart(), regexRes.getEnd()));
            }
        }
        log.info("res result:{}", GSON.toJson(regexMatchParam));
    }
}
