package com.xiaomi.mone.log.manager.service;

import com.xiaomi.mone.cache.redis.RedisClientFactory;
import com.xiaomi.mone.log.manager.model.dto.MatrixAppsDTO;
import com.xiaomi.mone.log.manager.model.dto.MatrixDataDTO;
import com.xiaomi.mone.log.manager.model.dto.TraceAppInfoDTO;
import com.xiaomi.mone.log.manager.service.impl.MatrixLogServiceImpl;
import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

import static com.xiaomi.mone.log.manager.service.MoneUserDetailService.GSON;
import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023/6/21 17:18
 */
@Slf4j
public class MatrixLogServiceImplTest {

    private MatrixLogServiceImpl matrixLogService;

    @Before
    public void buildBean() {
        getConfigFromNanos();
        Ioc.ins().init("com.xiaomi");
        matrixLogService = Ioc.ins().getBean(MatrixLogServiceImpl.class);
    }

    @Test
    public void testQueryTraceAppInfos() {
        Long[] appIds = {2763L};
        List<TraceAppInfoDTO> traceAppInfoDTOS = matrixLogService.queryTraceAppInfos(appIds);
        log.info("testQueryTraceAppInfos result:{}", GSON.toJson(traceAppInfoDTOS));
    }

    @Test
    public void testQueryMatrixPodInfoByIp() {
        String ip = "**************";
        List<MatrixDataDTO.PodInfo> pods = matrixLogService.queryMatrixPodInfoByIp(ip);
        log.info("testQueryMatrixPodInfoByIp result:{}", GSON.toJson(pods));
    }

    @Test
    public void testHostNameToIP() {
        String host = "*************";
        String ip = matrixLogService.hostNameToIP(host);
        log.info("hostNameToIP result:{}", ip);

        host = "tj1-serving-k8s-slave105-20230621.kscn";
        ip = matrixLogService.hostNameToIP(host);
        log.info("hostNameToIP result:{}", ip);
    }

    @Test
    public void testGetMatrixPodInfoByAppInfo() {
        long appId = 2701;
        String deploySpaceName = "oc-pull-order-cn-test";
        MatrixAppsDTO.MatrixDeploySpace matrixPodInfoByAppInfo = matrixLogService.getMatrixPodInfoByAppInfo(appId, deploySpaceName);
        log.info("testGetMatrixPodInfoByAppInfo result:{}", GSON.toJson(matrixPodInfoByAppInfo));
    }
}
