package com.xiaomi.mone.log.manager.service;


import com.xiaomi.mone.log.manager.model.dto.DeploySystemAppsDTO.DeploySystemAppData;
import com.xiaomi.mone.log.manager.model.dto.DeploySystemAppsDTO.DeploySystemCluster;
import com.xiaomi.mone.log.manager.service.impl.DeploySystemLogServiceImpl;
import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;

import static com.xiaomi.mone.log.manager.service.MoneUserDetailService.GSON;
import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;

@Slf4j
public class DeploySystemLogServiceImplTest {
    private DeploySystemLogServiceImpl deploySystemLogService;

    @Before
    public void buildBean() {
        getConfigFromNanos();
        Ioc.ins().init("com.xiaomi");
        deploySystemLogService = Ioc.ins().getBean(DeploySystemLogServiceImpl.class);
    }

    @Test
    public void testQueryTraceAppInfos() {
        Long appId = 677L;
        DeploySystemAppData deploySystemApp = deploySystemLogService.getDeploySystemApp(appId);
        log.info("testQueryTraceAppInfos result:{}", GSON.toJson(deploySystemApp));
        deploySystemApp = deploySystemLogService.getDeploySystemApp(appId, "staging");
        log.info("testQueryTraceAppInfos result:{}", GSON.toJson(deploySystemApp));
    }

    @Test
    public void testQueryClusterIps() {
        Long appId = 677L;
        String appName = "MiCloudRobotService_MiCloudRobotService";
        String cluster = "staging";
        DeploySystemCluster deploySystemIPs = deploySystemLogService.getDeploySystemIPs(appId, appName, cluster);
        log.info("testQueryClusterIps result:{}", GSON.toJson(deploySystemIPs));
    }
}
