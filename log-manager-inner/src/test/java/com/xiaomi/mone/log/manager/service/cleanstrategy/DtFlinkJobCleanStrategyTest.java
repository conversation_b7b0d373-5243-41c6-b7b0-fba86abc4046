package com.xiaomi.mone.log.manager.service.cleanstrategy;

import com.xiaomi.mone.log.manager.enums.ClearStageEnum;
import com.xiaomi.mone.log.manager.model.vo.ClearDtResourceParam;
import com.xiaomi.youpin.docean.Ioc;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.apache.ozhera.log.manager.common.utils.ManagerUtil.getConfigFromNanos;

/**
 * @author: songyutong1
 * @date: 2024/09/14/11:29
 */
@Slf4j
public class DtFlinkJobCleanStrategyTest {

    private AbstractCleanStrategy dtFlinkJobCleanStrategy;
    private String uuid;
    private ClearDtResourceParam param = new ClearDtResourceParam();

    @Before
    public void buildUp() {
        getConfigFromNanos();
        Ioc.ins().init("com.xiaomi");
        dtFlinkJobCleanStrategy = CleanStrategyFactory.getCleanStrategy(ClearStageEnum.STAGE_8);
        uuid = UUID.randomUUID().toString();
        param.setMachineRoom("cn");
        param.setClearFlag(false);
        param.setToken("ecd4fcf210004296945800466e263de5");
        param.setFlinkJobToken("0d529701f8354e55a2b058c42f7773b0");
    }

    @Test
    public void testClean() {
        try {
            dtFlinkJobCleanStrategy.clean(param, uuid);
        } catch (Exception e) {
            log.error("DtFlinkJobCleanStrategyTest.testClean error, err={}", e.getMessage(), e);
            Assert.fail();
        }
    }

    @Test
    public void testGetFinalTailList() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        dtFlinkJobCleanStrategy = (DtFlinkJobCleanStrategy) dtFlinkJobCleanStrategy;
        Method method = DtFlinkJobCleanStrategy.class.getDeclaredMethod("getFinalTailList", List.class);
        method.setAccessible(true);
        List<Long> tailId = new ArrayList<>();
        tailId.add(28L);
        tailId.add(1243124123L);
        tailId.add(79L);
        tailId.add(12343434341L);
        String finalTailStr = (String) method.invoke(dtFlinkJobCleanStrategy, tailId);

        List<Long> resultTailId = new ArrayList<>();
        resultTailId.add(28L);
        resultTailId.add(79L);
        String resultStr = "28,79";
        Assert.assertEquals(resultStr, finalTailStr);
        Assert.assertEquals(resultTailId, tailId);
    }

}
