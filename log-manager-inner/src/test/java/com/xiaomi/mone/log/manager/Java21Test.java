package com.xiaomi.mone.log.manager;

import org.junit.Test;

import static java.lang.StringTemplate.STR;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/1/15 13:11
 */
public class Java21Test {

    String name = "z<PERSON><PERSON>";
    int age = 18;

    /**
     * 字符串模板测试
     * --enable-preview
     */
    @Test
    public void StrTemplateTest() {

        String message = STR."My name is \{name}, I'm \{age} years old.";
        System.out.println(message);
    }

    @Test
    public void textBlockTest() {
        String json3 = STR."""
               {
                 "name": "\{name}",
                 "age": \{age}
               }
               """;
        System.out.println(json3);
    }

    @Test
    public void switchTest() {
        String type = "child";
        int result = switch (type) {
            case "child" -> 0;
            case "adult" -> 1;
            default -> -1;
        };
        System.out.println(result);
        Object obj = 123;
        String formatted = switch (obj) {
            case Integer i -> String.format("int %d", i);
            case Long l -> String.format("long %d", l);
            case Double d -> String.format("double %f", d);
            case String s -> String.format("string %s", s);
            default -> "unknown";
        };
        System.out.println(formatted);
    }
}
