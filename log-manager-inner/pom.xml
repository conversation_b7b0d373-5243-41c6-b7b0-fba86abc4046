<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xiaomi.mone</groupId>
        <artifactId>hera-log-inner</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>log-manager-inner</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>docean</artifactId>
            <version>1.6.0-jdk21-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>docean-plugin-configuration</artifactId>
            <version>1.6.0-jdk21-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>log-api-inner</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.gliwka.hyperscan</groupId>
            <artifactId>hyperscan</artifactId>
            <version>5.4.0-2.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.10.1</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>log-common-inner</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
            <version>2.7.12-mone-v14-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
            <version>3.4.3</version>
        </dependency>

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>es</artifactId>
<!--            <version>1.6.3-jdk21</version>-->
            <version>1.6.3-jdk21-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.apache.ozhera</groupId>
            <artifactId>log-manager</artifactId>
            <version>2.2.7-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>run.mone</groupId>
                    <artifactId>dubbo</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>run.mone</groupId>
                    <artifactId>nacos-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>run.mone</groupId>
                    <artifactId>spring-context-support</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-core</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>lombok</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
                <exclusion>
                    <groupId>run.mone</groupId>
                    <artifactId>docean-plugin-es-antlr4</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.nacos</groupId>
                    <artifactId>nacos-client</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-plus-generator</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-extension</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.ozhera</groupId>
                    <artifactId>log-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>run.mone</groupId>
                    <artifactId>docean-plugin-mybatis-plus</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>run.mone</groupId>
                    <artifactId>docean-plugin-configuration</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>docean</artifactId>
                    <groupId>run.mone</groupId>
                </exclusion>
                <exclusion>
                    <groupId>run.mone</groupId>
                    <artifactId>docean-plugin-config</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>run.mone</groupId>
                    <artifactId>docean-plugin-datasource</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>run.mone</groupId>
                    <artifactId>docean-plugin-datasource</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>run.mone</groupId>
                    <artifactId>excel</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>run.mone</groupId>
                    <artifactId>feishu</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.youpin</groupId>
            <artifactId>feishu</artifactId>
            <version>1.5.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>excel</artifactId>
            <version>1.5.0-jdk21</version>
        </dependency>

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>docean-plugin-mybatis-plus</artifactId>
            <version>1.6.0-jdk21-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>docean-plugin-es-antlr4</artifactId>
            <version>1.4-jdk20-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>run.mone</groupId>
                    <artifactId>es</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>lombok</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>docean</artifactId>
                    <groupId>run.mone</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 融合云v2认证 -->
        <dependency>
            <groupId>com.xiaomi.cloud</groupId>
            <artifactId>cloud-iam-sdk-java</artifactId>
            <version>1.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.youpin</groupId>
            <artifactId>gwdash-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.nacos</groupId>
                    <artifactId>nacos-client</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>lombok</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
                <exclusion>
                    <groupId>run.mone</groupId>
                    <artifactId>common</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>gitlab</artifactId>
                    <groupId>run.mone</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.infra.galaxy</groupId>
            <artifactId>galaxy-talos-sdk</artifactId>
            <version>2.7.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpclient</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpcore</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpclient</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.infra.galaxy</groupId>
            <artifactId>galaxy-derora-client</artifactId>
            <version>3.1.1</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 数据工厂api -->
        <dependency>
            <groupId>com.xiaomi.bigdata.workshop</groupId>
            <artifactId>open-api-client</artifactId>
            <version>1.0.10-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.youpin.mione</groupId>
            <artifactId>hera-flink-api</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>lombok</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.cloud</groupId>
            <artifactId>cloud-iam-sdk-java</artifactId>
            <version>1.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>lombok</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpcore</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>app-common-inner</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>5.1.48</version>
        </dependency>
        <!-- Quartz Scheduler -->
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <version>2.3.2</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.htmlparser</groupId>
            <artifactId>htmlparser</artifactId>
            <version>2.1</version>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.14.3</version>
        </dependency>


        <dependency>
            <groupId>com.aventrix.jnanoid</groupId>
            <artifactId>jnanoid</artifactId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>3.3.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.ozhera</groupId>
            <artifactId>log-common</artifactId>
            <version>2.2.7-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>infra-result</artifactId>
            <version>1.6.1-jdk21</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>run.mone</groupId>
            <artifactId>log-common</artifactId>
            <version>1.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.checkerframework</groupId>
            <artifactId>checker-qual</artifactId>
            <version>3.41.0</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

    <build>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>**/log-manager/config.properties</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources/META-INF</directory>
                <filtering>true</filtering>
                <includes>
                    <include>app.properties</include>
                </includes>
                <targetPath>META-INF/</targetPath>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.5.1</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <finalName>${project.artifactId}-${project.version}</finalName>
                            <shadedArtifactAttached>false</shadedArtifactAttached>
                            <filters>
                                <filter>
                                    <artifact>*:run.mone:log-manager</artifact>
                                    <includes>
                                        <include>**/*.class</include>
                                        <include>**/*.properties</include>
                                        <include>**/*.xml</include>
                                    </includes>
                                    <excludes>
                                        <exclude>**/config.properties</exclude>
                                    </excludes>
                                </filter>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                            <transformers>
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <mainClass>com.xiaomi.mone.log.manager.MiLogManagerBootstrap</mainClass>
                                </transformer>
                            </transformers>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>21</source>
                    <target>21</target>
                    <compilerArgs>--enable-preview</compilerArgs>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <argLine>--enable-preview -XX:+UseZGC
                        --add-opens=java.xml/com.sun.org.apache.xerces.internal.impl.dv.util=ALL-UNNAMED
                        --add-opens=java.base/java.util.regex=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED
                        --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED
                        --add-opens=java.base/sun.nio.fs=ALL-UNNAMED
                        --add-exports=java.base/sun.reflect.annotation=ALL-UNNAMED
                        --add-opens=java.base/java.math=ALL-UNNAMED
                    </argLine>
                </configuration>
            </plugin>

        </plugins>


    </build>

    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <profiles.active>local</profiles.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <filters>
                    <filter>src/main/resources/config/local.properties</filter>
                </filters>
            </build>
        </profile>

        <profile>
            <id>staging</id>
            <properties>
                <profiles.active>staging</profiles.active>
            </properties>
            <build>
                <filters>
                    <filter>src/main/resources/config/staging.properties</filter>
                </filters>
            </build>
        </profile>

        <profile>
            <id>intranet</id>
            <properties>
                <profiles.active>intranet</profiles.active>
            </properties>
            <build>
                <filters>
                    <filter>src/main/resources/config/intranet.properties</filter>
                </filters>
            </build>
        </profile>
    </profiles>

    <distributionManagement>
        <repository>
            <id>central</id>
            <name>maven-release-virtual</name>
            <url>https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>snapshots</name>
            <url>https://pkgs.d.xiaomi.net/artifactory/snapshots</url>
        </snapshotRepository>
    </distributionManagement>

</project>