FROM micr.cloud.mioffice.cn/mixiao/openjdk:20-jdk-bullseye AS mvnBuild
COPY . /tmp/hera-log/
WORKDIR /tmp/hera-log/log-agent-inner/
RUN mvn -U -P ${profile} clean package -Dmaven.test.skip=true

FROM micr.cloud.mioffice.cn/mixiao/miserver:0.2.48-jae<PERSON>-jaco<PERSON>
COPY --from=mvnBuild /tmp/hera-log/log-agent-inner/target/log-agent-inner-1.0-SNAPSHOT.jar /home/<USER>/china-efficiency/observability_milog/log-agent-inner-1.0-SNAPSHOT.jar
RUN echo 'Asia/Shanghai' >/etc/timezone
WORKDIR /home/<USER>/milog/

ENTRYPOINT ["sh","-c","java --enable-preview -Xms1g -Xmx1g --add-opens=java.base/java.util.regex=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.xml/com.sun.org.apache.xerces.internal.impl.dv.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-modules=jdk.incubator.concurrent --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -jar /home/<USER>/china-efficiency/observability_milog/log-agent-inner-1.0-SNAPSHOT.jar"]