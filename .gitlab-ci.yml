#使用文档:http://micode.be.xiaomi.com/help/ci/quick_start/README.md
#image 指定了运行的docker镜像,用以区分编译环境
#hub.pf.xiaomi.com/ci-images/ci:maven-openjdk8-onbuild JAVA8
#hub.pf.xiaomi.com/ci-images/ci:maven-jdk-7-onbuild JAVA7
#hub.pf.xiaomi.com/ci-images/ci:maven-jdk-6-onbuild JAVA6
#hub.pf.xiaomi.com/ci-images/ci:golang-1.6.2-onbuild GO1.6.2
#hub.pf.xiaomi.com/ci-images/ci:golang-1.13.4-onbuild GO1.13.4
#hub.pf.xiaomi.com/ci-images/ci:golang-1.15.8-onbuild GO1.15.8

#**此处根据自身项目需求选择对应的镜像版本(需要开发修改)**
image: hub.pf.xiaomi.com/ci-images/ci:maven-openjdk8-onbuild

# 定义 stages（阶段）。任务将按此顺序执行。
stages:
    - build
    - build_image

# 全局变量
variables:
    # 运行应用所在目录，将会复制到 deploy/
    APP_PATH: log-stream/target/log-stream-1.0-SNAPSHOT.jar


# 为了加快打包速度，cache 功能可能对你非常有用
#cache:
#  key: "$CI_PROJECT_ID"  # 启用按项目ID缓存
#  untracked: true        # 缓存所有Git未追踪的文件
#  paths:  #添加需要cache的目录，以下几个目录会被缓存起来，下次构建会解压出来
#  #- deploy
#  - .local
#  - .m2
#  - node_modules

# 定义 job（任务）
# 测试环境
test-build:
    stage: build
    script:
        #请根据自己项目，修改构建命令
        - cd log-stream
        - mvn -U clean package -P europe -Dsonar.skip=true -Dmaven.test.skip=true
        - cd ../
        - cp -rf $APP_PATH log-stream/deploy
    only:
        - tags
#        except:
#            - intranet
    artifacts:
        #发布时，会以target的下级目录为根目录发布
        paths:  #此处的deploy名称请勿修改,修改会导致发布失败
            - log-stream/deploy/*
        untracked: false
deploy2docker:
    stage: build_image
    image:
        name: cr.d.xiaomi.net/containercloud/kaniko-executor-xiaomi:native-v1.0.0
        entrypoint: [""]
    script:
        - echo "build image"
        - IMAGE="hub.pf.xiaomi.com/log-stream/log-stream:$CI_COMMIT_TAG"
        - CI_REGISTRY="hub.pf.xiaomi.com"
        - echo "{\"auths\":{\"$CI_REGISTRY\":{\"username\":\"$HUBUSER\",\"password\":\"$HUBPASS\"}}}" > /kaniko/.docker/config.json
        - echo "generate docker image $IMAGE"
        - /kaniko/executor --context ./log-stream/deploy --dockerfile ./Dockerfile  --destination $IMAGE
    after_script:
        - echo "build completed"
    only:
        - tags
#    except:
#        - intranet
