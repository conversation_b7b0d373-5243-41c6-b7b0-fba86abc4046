hera-log-agent:
  build:
    command: sh make_logagent.sh
    environments:
      JAVA_HOME: /opt/jdk/jdk-21.0.1
      MAVEN_HOME: /opt/maven/apache-maven-3.8.4
      MAVEN_OPTS: -Xms256m -Xmx4096m
    target: release
  runtime:
    environments:
      CLUSTER: ${cluster}
      SERVICE: '{{ service }}'
    process_log: /home/<USER>/log/{{ service }}/server.log
    start: sh {{ release_path }}/bin/start_logagent.sh
    stderr_log: /home/<USER>/log/{{ service }}/stderr.log
    stdout_log: /home/<USER>/log/{{ service }}/stdout.log