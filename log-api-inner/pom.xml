<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xiaomi.mone</groupId>
        <artifactId>hera-log-inner</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <groupId>run.mone</groupId>
    <artifactId>log-api-inner</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.apache.ozhera</groupId>
            <artifactId>log-api</artifactId>
            <version>2.2.6-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>log-common-inner</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

</project>