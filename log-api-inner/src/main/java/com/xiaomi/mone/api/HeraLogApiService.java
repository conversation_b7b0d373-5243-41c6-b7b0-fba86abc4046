package com.xiaomi.mone.api;

import com.xiaomi.mone.model.req.LogFilterOptions;
import com.xiaomi.mone.model.req.LogUrlParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/3/6 16:12
 */
public interface HeraLogApiService {

    List<String> queryLogUrl(LogUrlParam logUrlParam);

    List<Map<String, Object>> queryLogData(LogFilterOptions filterOptions);

}
