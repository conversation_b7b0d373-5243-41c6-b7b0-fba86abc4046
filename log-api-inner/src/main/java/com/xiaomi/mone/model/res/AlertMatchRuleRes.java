package com.xiaomi.mone.model.res;

import lombok.Data;
import org.apache.ozhera.log.parse.LogParserData;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/12/18 15:46
 */
@Data
public class AlertMatchRuleRes {
    private Long tailId;
    private String parserClassName;
    //规则
    private List<MatchContentRule> matchContentRule;
    //parserClassName 反射时需要的参数
    private LogParserData logParserData;
}
