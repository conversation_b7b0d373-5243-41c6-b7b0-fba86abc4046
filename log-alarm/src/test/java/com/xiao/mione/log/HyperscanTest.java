package com.xiao.mione.log;

import com.gliwka.hyperscan.util.PatternFilter;
import com.gliwka.hyperscan.wrapper.CompileErrorException;
import org.junit.Test;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static java.util.Arrays.asList;
import static java.util.stream.Collectors.toList;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

/**
 * @Auther: wtt
 * @Date: 2022/3/21 15:58
 * @Description:
 */
public class HyperscanTest {

    @Test
    public void testMultiPattern() throws CompileErrorException {
        String message = "{\"level\":\"error\",\"ts\":1685812028.7381313,\"caller\":\"server/ic_recover.go:22\",\"msg\":\"IcRecover\",\"service_name\":\"i18n_shop_user\",\"hostname\":\"i18n-shop-user-sg-plzxx\",\"log_id\":\"345028317199_c1fnw\",\"logger_from\":\"base_context\",\"debug\":\"goroutine 140077277 [running]:\\nruntime/debug.Stack(0xc00d67a178, 0x1800140, 0x2c2a840)\\n\\t/usr/local/go/src/runtime/debug/stack.go:24 +0x9f\\ngit.n.xiaomi.com/mit/i18n/rd/i18n-shop-user/infra/interceptors/server.IcRecover.func1(0x1dc3e60, 0xc011b6d880, 0xc00d67a5b8, 0xc00d67a5a8)\\n\\t/workspace/repo/i18n-shop-user/infra/interceptors/server/ic_recover.go:22 +0x70\\npanic(0x1800140, 0x2c2a840)\\n\\t/usr/local/go/src/runtime/panic.go:965 +0x1b9\\ngit.n.xiaomi.com/mit/i18n/rd/i18n-shop-user/domain/user/logic/web/token.GetUserTokensRecordList(0x1ddcb10, 0xc011b6d880, 0xc011bd81e0, 0x1ddcb10, 0xc011b6d880, 0xc00d67a501)\\n\\t/workspace/repo/i18n-shop-user/domain/user/logic/web/token/token.go:54 +0x765\\ngit.n.xiaomi.com/mit/i18n/rd/i18n-shop-user/service.(*UserService).GetUserTokensRecordList(0x2ca28b0, 0x1dc3e60, 0xc011b6d880, 0xc011bd81e0, 0x2ca28b0, 0xc028281380, 0x1f)\\n\\t/workspace/repo/i18n-shop-user/service/user_service.go:212 +0x117\\nmicode.be.xiaomi.com/mi-go/proto-go/sales/i18n/shop/user/web/v1._UserService_GetUserTokensRecordList_Handler.func1(0x1dc3e60, 0xc011b6d880, 0x19d8040, 0xc011bd81e0, 0xd17436, 0xc011bd81e0, 0x19922c0, 0x19d8040)\\n\\t/workspace/repo/i18n-shop-user/proto-go/sales/i18n/shop/user/web/v1/user_service.pb.go:1224 +0x8b\\ngit.n.xiaomi.com/mit/i18n/rd/i18n-shop-user/infra/interceptors/server.IcRecover(0x1dc3e60, 0xc011b6d880, 0x19d8040, 0xc011bd81e0, 0xc011bd60e0, 0xc007f67110, 0x0, 0x0, 0x0, 0x0)\\n\\t/workspace/repo/i18n-shop-user/infra/interceptors/server/ic_recover.go:36 +0xc2\\ngithub.com/grpc-ecosystem/go-grpc-middleware.ChainUnaryServer.func1.1.1(0x1dc3e60, 0xc011b6d880, 0x19d8040, 0xc011bd81e0, 0x0, 0xb, 0xf, 0x0)\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/chain.go:25 +0x63\\ngit.n.xiaomi.com/mit/i18n/rd/i18n-shop-user/infra/interceptors/server.IcValidator.func1(0x1dc3e60, 0xc011b6d880, 0x19d8040, 0xc011bd81e0, 0xc011bd60e0, 0xc011bd6100, 0x2, 0x2, 0xc011b6b3e0, 0xc011b6b380)\\n\\t/workspace/repo/i18n-shop-user/infra/interceptors/server/ic_validator.go:45 +0x465\\ngithub.com/grpc-ecosystem/go-grpc-middleware.ChainUnaryServer.func1.1.1(0x1dc3e60, 0xc011b6d880, 0x19d8040, 0xc011bd81e0, 0xc011b6b380, 0xb, 0xf, 0x0)\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/chain.go:25 +0x63\\ngit.n.xiaomi.com/mit/i18n/rd/i18n-shop-user/infra/interceptors/server.IcAop(0x1dc3bf8, 0xc011bd8660, 0x19d8040, 0xc011bd81e0, 0xc011bd60e0, 0xc011bd6120, 0xc0, 0x175dac0, 0x18c6bc0, 0xc011bd8660)\\n\\t/workspace/repo/i18n-shop-user/infra/interceptors/server/ic_aop.go:40 +0x897\\ngithub.com/grpc-ecosystem/go-grpc-middleware.ChainUnaryServer.func1.1.1(0x1dc3bf8, 0xc011bd8660, 0x19d8040, 0xc011bd81e0, 0xc011ba4600, 0x3, 0x3, 0x1765351bba580358)\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/chain.go:25 +0x63\\ngit.n.xiaomi.com/mit/i18n/rd/i18n-shop-user/infra/interceptors/server.IcAccessLog(0x1dc3bf8, 0xc011bd8660, 0x19d8040, 0xc011bd81e0, 0xc011bd60e0, 0xc011bd6140, 0xc011b94c80, 0x1000000019ff560, 0x0, 0xc011bb16d0)\\n\\t/workspace/repo/i18n-shop-user/infra/interceptors/server/ic_access_log.go:33 +0x5ce\\ngithub.com/grpc-ecosystem/go-grpc-middleware.ChainUnaryServer.func1.1.1(0x1dc3bf8, 0xc011bd85d0, 0x19d8040, 0xc011bd81e0, 0x0, 0x0, 0x0, 0x0)\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/chain.go:25 +0x63\\ngithub.com/grpc-ecosystem/go-grpc-middleware/recovery.UnaryServerInterceptor.func1(0x1dc3bf8, 0xc011bd85d0, 0x19d8040, 0xc011bd81e0, 0xc011bd60e0, 0xc011bd6160, 0x0, 0x0, 0x0, 0x0)\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/recovery/interceptors.go:33 +0xcb\\ngithub.com/grpc-ecosystem/go-grpc-middleware.ChainUnaryServer.func1.1.1(0x1dc3bf8, 0xc011bd85d0, 0x19d8040, 0xc011bd81e0, 0x40, 0xc116faaf2b523f2c, 0x328e53d881862, 0x2c72f20)\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/chain.go:25 +0x63\\ngit.n.xiaomi.com/miopen/mit/i18n/rd/mi-grpc-go/middle.zapUnaryServerInterceptor(0x1dc3bf8, 0xc011bd8510, 0x19d8040, 0xc011bd81e0, 0xc011bd60e0, 0xc011bd6180, 0x1153c5a, 0x1926fa0, 0xc011bd62e0, 0xc011bd60e0)\\n\\t/go/pkg/mod/git.n.xiaomi.com/miopen/mit/i18n/rd/mi-grpc-go@v0.3.6/middle/logging_server.go:65 +0xf1\\ngithub.com/grpc-ecosystem/go-grpc-middleware.ChainUnaryServer.func1.1.1(0x1dc3bf8, 0xc011bd8510, 0x19d8040, 0xc011bd81e0, 0x203004, 0x203004, 0xc00d67b508, 0xc00d67b510)\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/chain.go:25 +0x63\\ngithub.com/grpc-ecosystem/go-grpc-middleware.ChainUnaryServer.func1(0x1dc3bf8, 0xc011bd8510, 0x19d8040, 0xc011bd81e0, 0xc011bd60e0, 0xc011bd6180, 0x199cf80, 0x7fd9bc493f98, 0xc0008f3830, 0xcf4dd41e8283b32d)\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/chain.go:34 +0xd7\\ngithub.com/grpc-ecosystem/go-grpc-middleware.ChainUnaryServer.func1.1.1(0x1dc3bf8, 0xc011bd8510, 0x19d8040, 0xc011bd81e0, 0x1da6700, 0x7fd9bc493f98, 0xc00d67b5d8, 0xdf1f4a)\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/chain.go:25 +0x63\\ngit.n.xiaomi.com/miopen/mit/i18n/rd/mi-grpc-go/middle.merrorsUnaryServerInterceptor.func1(0x1dc3bf8, 0xc011bd8510, 0x19d8040, 0xc011bd81e0, 0xc011bd60e0, 0xc011bd61a0, 0x3, 0x3, 0x7fd9bc493f98, 0xc001137200)\\n\\t/go/pkg/mod/git.n.xiaomi.com/miopen/mit/i18n/rd/mi-grpc-go@v0.3.6/middle/merrors_server.go:23 +0x62\\ngithub.com/grpc-ecosystem/go-grpc-middleware.ChainUnaryServer.func1.1.1(0x1dc3bf8, 0xc011bd8510, 0x19d8040, 0xc011bd81e0, 0x40, 0xc011ba2640, 0x1b0a4c4, 0x8)\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/chain.go:25 +0x63\\ngithub.com/grpc-ecosystem/go-grpc-prometheus.(*ServerMetrics).UnaryServerInterceptor.func1(0x1dc3bf8, 0xc011bd8510, 0x19d8040, 0xc011bd81e0, 0xc011bd60e0, 0xc011bd61c0, 0x0, 0x0, 0x0, 0x0)\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-prometheus@v1.2.0/server_metrics.go:107 +0xb0\\ngithub.com/grpc-ecosystem/go-grpc-middleware.ChainUnaryServer.func1.1.1(0x1dc3bf8, 0xc011bd8510, 0x19d8040, 0xc011bd81e0, 0x1b0a4c4, 0x8, 0x0, 0x0)\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/chain.go:25 +0x63\\ngo.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc.UnaryServerInterceptor.func1(0x1dc3bf8, 0xc011bd8510, 0x19d8040, 0xc011bd81e0, 0xc011bd60e0, 0xc011bd61e0, 0x0, 0x0, 0x0, 0x0)\\n\\t/go/pkg/mod/go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc@v0.21.0/interceptor.go:338 +0x6b6\\ngithub.com/grpc-ecosystem/go-grpc-middleware.ChainUnaryServer.func1.1.1(0x1dc3bf8, 0xc011bd81b0, 0x19d8040, 0xc011bd81e0, 0xc0006c0400, 0x0, 0xc0017e7b20, 0x40ffd8)\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/chain.go:25 +0x63\\ngithub.com/grpc-ecosystem/go-grpc-middleware.ChainUnaryServer.func1(0x1dc3bf8, 0xc011bd81b0, 0x19d8040, 0xc011bd81e0, 0xc011bd60e0, 0xc007f67110, 0xc001e66b90, 0x509446, 0x196c0e0, 0xc011bd81b0)\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/chain.go:34 +0xd7\\nmicode.be.xiaomi.com/mi-go/proto-go/sales/i18n/shop/user/web/v1._UserService_GetUserTokensRecordList_Handler(0x1ae4100, 0x2ca28b0, 0x1dc3bf8, 0xc011bd81b0, 0xc000b22380, 0xc001076bd0, 0x1dc3bf8, 0xc011bd81b0, 0xc00e4386c0, 0x5a)\\n\\t/workspace/repo/i18n-shop-user/proto-go/sales/i18n/shop/user/web/v1/user_service.pb.go:1226 +0x150\\ngoogle.golang.org/grpc.(*Server).processUnaryRPC(0xc001560540, 0x1dd3278, 0xc002480820, 0xc0054699e0, 0xc0010d2660, 0x2c47170, 0x0, 0x0, 0x0)\\n\\t/go/pkg/mod/google.golang.org/grpc@v1.48.0/server.go:1295 +0x693\\ngoogle.golang.org/grpc.(*Server).handleStream(0xc001560540, 0x1dd3278, 0xc002480820, 0xc0054699e0, 0x0)\\n\\t/go/pkg/mod/google.golang.org/grpc@v1.48.0/server.go:1636 +0xd0c\\ngoogle.golang.org/grpc.(*Server).serveStreams.func1.2(0xc004dfc070, 0xc001560540, 0x1dd3278, 0xc002480820, 0xc0054699e0)\\n\\t/go/pkg/mod/google.golang.org/grpc@v1.48.0/server.go:932 +0xab\\ncreated by google.golang.org/grpc.(*Server).serveStreams.func1\\n\\t/go/pkg/mod/google.golang.org/grpc@v1.48.0/server.go:930 +0x1fd\\n\",\"context_site_id\":3295206437,\"context_uid\":6296947550,\"stacktrace\":\"git.n.xiaomi.com/mit/i18n/rd/i18n-shop-user/infra/interceptors/server.IcRecover.func1\\n\\t/workspace/repo/i18n-shop-user/infra/interceptors/server/ic_recover.go:22\\nruntime.gopanic\\n\\t/usr/local/go/src/runtime/panic.go:965\\nruntime.panicdivide\\n\\t/usr/local/go/src/runtime/panic.go:191\\ngit.n.xiaomi.com/mit/i18n/rd/i18n-shop-user/domain/user/logic/web/token.GetUserTokensRecordList\\n\\t/workspace/repo/i18n-shop-user/domain/user/logic/web/token/token.go:54\\ngit.n.xiaomi.com/mit/i18n/rd/i18n-shop-user/service.(*UserService).GetUserTokensRecordList\\n\\t/workspace/repo/i18n-shop-user/service/user_service.go:212\\nmicode.be.xiaomi.com/mi-go/proto-go/sales/i18n/shop/user/web/v1._UserService_GetUserTokensRecordList_Handler.func1\\n\\t/workspace/repo/i18n-shop-user/proto-go/sales/i18n/shop/user/web/v1/user_service.pb.go:1224\\ngit.n.xiaomi.com/mit/i18n/rd/i18n-shop-user/infra/interceptors/server.IcRecover\\n\\t/workspace/repo/i18n-shop-user/infra/interceptors/server/ic_recover.go:36\\ngithub.com/grpc-ecosystem/go-grpc-middleware.ChainUnaryServer.func1.1.1\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/chain.go:25\\ngit.n.xiaomi.com/mit/i18n/rd/i18n-shop-user/infra/interceptors/server.IcValidator.func1\\n\\t/workspace/repo/i18n-shop-user/infra/interceptors/server/ic_validator.go:45\\ngithub.com/grpc-ecosystem/go-grpc-middleware.ChainUnaryServer.func1.1.1\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/chain.go:25\\ngit.n.xiaomi.com/mit/i18n/rd/i18n-shop-user/infra/interceptors/server.IcAop\\n\\t/workspace/repo/i18n-shop-user/infra/interceptors/server/ic_aop.go:40\\ngithub.com/grpc-ecosystem/go-grpc-middleware.ChainUnaryServer.func1.1.1\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/chain.go:25\\ngit.n.xiaomi.com/mit/i18n/rd/i18n-shop-user/infra/interceptors/server.IcAccessLog\\n\\t/workspace/repo/i18n-shop-user/infra/interceptors/server/ic_access_log.go:33\\ngithub.com/grpc-ecosystem/go-grpc-middleware.ChainUnaryServer.func1.1.1\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/chain.go:25\\ngithub.com/grpc-ecosystem/go-grpc-middleware/recovery.UnaryServerInterceptor.func1\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/recovery/interceptors.go:33\\ngithub.com/grpc-ecosystem/go-grpc-middleware.ChainUnaryServer.func1.1.1\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/chain.go:25\\ngit.n.xiaomi.com/miopen/mit/i18n/rd/mi-grpc-go/middle.zapUnaryServerInterceptor\\n\\t/go/pkg/mod/git.n.xiaomi.com/miopen/mit/i18n/rd/mi-grpc-go@v0.3.6/middle/logging_server.go:65\\ngithub.com/grpc-ecosystem/go-grpc-middleware.ChainUnaryServer.func1.1.1\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/chain.go:25\\ngithub.com/grpc-ecosystem/go-grpc-middleware.ChainUnaryServer.func1\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/chain.go:34\\ngithub.com/grpc-ecosystem/go-grpc-middleware.ChainUnaryServer.func1.1.1\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/chain.go:25\\ngit.n.xiaomi.com/miopen/mit/i18n/rd/mi-grpc-go/middle.merrorsUnaryServerInterceptor.func1\\n\\t/go/pkg/mod/git.n.xiaomi.com/miopen/mit/i18n/rd/mi-grpc-go@v0.3.6/middle/merrors_server.go:23\\ngithub.com/grpc-ecosystem/go-grpc-middleware.ChainUnaryServer.func1.1.1\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/chain.go:25\\ngithub.com/grpc-ecosystem/go-grpc-prometheus.(*ServerMetrics).UnaryServerInterceptor.func1\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-prometheus@v1.2.0/server_metrics.go:107\\ngithub.com/grpc-ecosystem/go-grpc-middleware.ChainUnaryServer.func1.1.1\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/chain.go:25\\ngo.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc.UnaryServerInterceptor.func1\\n\\t/go/pkg/mod/go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc@v0.21.0/interceptor.go:338\\ngithub.com/grpc-ecosystem/go-grpc-middleware.ChainUnaryServer.func1.1.1\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/chain.go:25\\ngithub.com/grpc-ecosystem/go-grpc-middleware.ChainUnaryServer.func1\\n\\t/go/pkg/mod/github.com/grpc-ecosystem/go-grpc-middleware@v1.3.0/chain.go:34\\nmicode.be.xiaomi.com/mi-go/proto-go/sales/i18n/shop/user/web/v1._UserService_GetUserTokensRecordList_Handler\\n\\t/workspace/repo/i18n-shop-user/proto-go/sales/i18n/shop/user/web/v1/user_service.pb.go:1226\\ngoogle.golang.org/grpc.(*Server).processUnaryRPC\\n\\t/go/pkg/mod/google.golang.org/grpc@v1.48.0/server.go:1295\\ngoogle.golang.org/grpc.(*Server).handleStream\\n\\t/go/pkg/mod/google.golang.org/grpc@v1.48.0/server.go:1636\\ngoogle.golang.org/grpc.(*Server).serveStreams.func1.2\\n\\t/go/pkg/mod/google.golang.org/grpc@v1.48.0/server.go:932\"}";
        String patternStr = ".*^(?!.*(/global/).*).*\"ErrNo\":([^0]|\\d{2,4}|1\\d{4}),";
        Pattern pattern = Pattern.compile(patternStr);
//        Matcher matcher = pattern.matcher(message);
//        System.out.println(matcher.find());
        String patternStr2 = ".*?geoApi=\\{\"status\":\"0\".*";
        List<Pattern> patterns = asList(
                Pattern.compile(patternStr2, Pattern.DOTALL)
        );
        PatternFilter filter = new PatternFilter(patterns);
        System.out.println("");
    }

    @Test
    public void testMultHyper() throws CompileErrorException {
        String message = "[2023-01-05 16:13:50] [mi-i18n] [mos1-b2c-i18n-web12.ksru] [NOTICE] [************] {\"ActionName\":\"getenergy\",\"ControllerName\":\"activity\",\"DJobSize\":0,\"ErrNo\":1,\"Hostname\":\"mos1-b2c-i18n-web12.ksru\",\"InputData\":{\"BeginTimeSec\":1672906430,\"LoginVerifyMode\":1,\"IsNeedLogin\":false,\"OutputType\":1,\"MustHttps\":false,\"IsNeedSecurity\":false,\"IsCheckEtag\":false,\"IsNeedFilterReffer\":false,\"AppLocal\":\"ru\",\"Uri\":\"/ru/activity/getenergy?shizhan\",\"ClientType\":\"pc\",\"IsApp\":false,\"AppInfo\":\"\",\"AppVersion\":0,\"DeviceID\":\"\",\"PhoneModel\":\"\",\"AppType\":\"\",\"RequestFrom\":\"store\",\"GrpcClientType\":\"PC\",\"SdkVersion\":0,\"RnInfo\":{\"product\":\"151\"},\"RnVersion\":\"0.59.13\",\"ClientIp\":\"***********\",\"ServerType\":\"APP\",\"ProxyServerType\":\"\",\"LogId\":\"************\",\"IsHttps\":true,\"Warehouse\":\"\",\"GlobalProvince\":\"\",\"GlobalCity\":\"\",\"GlobalDistrict\":\"\",\"QuickOrder\":\"0\",\"Protocol\":\"https\",\"LoginStatus\":2,\"UserId\":0,\"LoginTime\":**********,\"GuestId\":\"XMGUEST-9FBCAB46-8692-92DA-9F19-BB05B7AFB958\",\"ImgQuality\":90,\"IsPrintTrace\":false,\"TokenInvalid\":false,\"MstUid\":\"\",\"Pincode\":\"\",\"IsSdk\":false,\"IsPoco\":false,\"IsB2b\":false,\"IsSupportV4\":true,\"CtxWithMetaData\":{\"Context\":0},\"UserAccountType\":0},\"Login_LoginVerifyMode\":1,\"Server\":\"ru.shopapi.b2c.srv\",\"TagName\":\"notice_uniq\",\"TimeUsed\":0,\"VJobSize\":0,\"rpcStat\":{}}\n";
        String patternStr = ".*^(!.*11118,.*).*\"ErrNo\":([^0]|\\d{2,4}|1\\d{4}),";
        List<Pattern> patterns = asList(
                Pattern.compile(patternStr, Pattern.DOTALL)
        );
        PatternFilter filter = new PatternFilter(patterns);

        List<Matcher> matchers = filter.filter(message);
        int count = 0;
        for (Matcher matcher : matchers) {
            while (matcher.find()) {
                count++;
            }
        }
        System.out.println("匹配的次数：" + count);
    }

    @Test
    public void readmeSample() throws CompileErrorException {
        List<Pattern> patterns = asList(
                Pattern.compile(".*(?i)error.*", Pattern.DOTALL)
                // and thousands more
        );
        //not thread-safe, create per thread
        PatternFilter filter = new PatternFilter(patterns);

        //this list now only contains the probably matching patterns, in this case the first one
        List<Matcher> matchers = filter.filter("level:WARN threadName:ConsumeMessageThread_3className:o.a.d.r.c.support.FailoverClusterInvokerline:87methodName: [DUBBO] Although retry the method getSkuBaseInfo in the service com.xiaomi.goods.gms.api.sku.SkuInfoService was successful by the provider 10.136.129.30:9344, but there have been failed providers [10.132.41.9:8479] (1/4) from the registry nacos.systech.b2c.srv:80 on the consumer 10.136.2.8 using the dubbo version 2.7.0-youpin-SNAPSHOT. Last error is:");
        int count = 0;
        //now we use the regular java regex api to check for matches- this is not hyperscan specific
        for (Matcher matcher : matchers) {
            if (matcher.find()) {
                // will print 7 and 27
                count++;
//                System.out.println(matcher.group(1));
            }
        }
        System.out.println("匹配的次数：" + count);
    }

    @Test
    public void filterNotMatchingPatterns() throws CompileErrorException {
        List<Pattern> patterns = asList(
                Pattern.compile(".*dealEventErrForPayMQ.*", Pattern.CASE_INSENSITIVE),
                Pattern.compile("The color is (blue|red|orange)"),
                Pattern.compile("The color is (blue|red|orange)")
        );

        PatternFilter filter = new PatternFilter(patterns);

        List<Matcher> matchers = filter.filter("The color is orange");
        for (Matcher matcher : matchers) {
            while (matcher.find()) {
                System.out.println(matcher.group(0));
                System.out.println(matcher.pattern().pattern());
            }
        }
//        assertHasPattern(patterns.get(1), matchers);
    }

    @Test
    public void handleFlagsProperly() throws CompileErrorException {
        List<Pattern> patterns = asList(
                Pattern.compile("The number is ([0-9]+)"),
                Pattern.compile("The number is ([0-9]+)", Pattern.CASE_INSENSITIVE),
                Pattern.compile("^The color is (blue|red|orange)$"),
                Pattern.compile("^The color is (blue|red|orange)$", Pattern.MULTILINE),
                Pattern.compile("something.else"),
                Pattern.compile("something.else", Pattern.DOTALL),
                Pattern.compile("match.THIS", Pattern.CASE_INSENSITIVE | Pattern.DOTALL)
        );

        PatternFilter filter = new PatternFilter(patterns);

        List<Matcher> matchers = filter.filter("tHE nuMBeR is 17");
        assertHasPattern(patterns.get(1), matchers);
        assertEquals(1, matchers.size());

        matchers = filter.filter("The number is 17");
        assertHasPattern(patterns.get(0), matchers);
        assertHasPattern(patterns.get(1), matchers);
        assertEquals(2, matchers.size());

        matchers = filter.filter("Some text\nThe color is red");
        assertHasPattern(patterns.get(3), matchers);
        assertEquals(1, matchers.size());

        matchers = filter.filter("something\nelse");
        assertHasPattern(patterns.get(5), matchers);
        assertEquals(1, matchers.size());

        matchers = filter.filter("match\nthiS");
        assertHasPattern(patterns.get(6), matchers);
        assertEquals(1, matchers.size());
    }

    private void assertHasPattern(Pattern pattern, List<Matcher> matchers) {
        List<Pattern> filteredPatterns = matchers.stream().map(Matcher::pattern).collect(toList());
        assertTrue(filteredPatterns.contains(pattern));
    }
}
