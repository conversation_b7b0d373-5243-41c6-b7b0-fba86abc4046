package com.xiao.mione.log;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

/**
 * @author: wtt
 * @date: 2022/6/6 12:56
 * @description:
 */
@Slf4j
public class MessageContainTest {

    @Test
    public void test() {
        String message = "[2022-06-06 11:09:08] [xmstore_api] [api-men-mi-com-stable-58f99945dd-lpsgc] [INFO] [296947434306] {\"message\":\"调用 X5 接口结束\",\"appid\":\"xm_oms\",\"args\":{\"\":\"\",\"debug\":false,\"token\":\"\"},\"resp\":{\"header\":{\"code\":403,\"msg\":{\"error\":\"未定义的接口\",\"method\":\"POST\",\"action\":\"getorglist\",\"controller\":\"Mihometransfer\",\"module\":\"Index\"},\"desc\":\"请求成功\",\"debug\":{\"cost_time\":\"8.890\"}},\"body\":[]},\"extra\":{\"uri\":\"\\/mihometransfer\\/getOrgList\",\"method\":\"POST\",\"query\":\"\",\"cookie\":\"\",\"usersAddr\":\"*************\",\"frontendAddr\":\"************\",\"referer\":\"\",\"function\":\"\",\"line\":\"\",\"file\":\"\",\"userAgent\":\"Google-HTTP-Java-Client\\/1.22.0 (gzip)\",\"timeUsed\":9,\"requestId\":\"756dd7ea34f1a56a00a02ba265a1309d\"}}";
        String code = "\"code\":,notContain,200";
        String[] codes = code.split(",");
        String[] split = message.split(codes[0]);
        boolean result = false;
        if (StringUtils.equals("Contain", codes[1])) {
            result = split[1].startsWith(codes[2]);
        }
        if (StringUtils.equals("notContain", codes[1])) {
            result = !split[1].startsWith(codes[2]);
        }
        log.info("result:{}", result);
    }
}
