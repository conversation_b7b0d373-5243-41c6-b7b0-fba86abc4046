package com.xiaomi.mione.log.match;

import com.xiaomi.mione.log.pojo.MatchContentRule;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/12/20 15:59
 */
public class MatchContentRuleEvaluator {
    /**
     * 根据 MatchContentRule 列表计算整体结果
     *
     * @param matchContentRules MatchContentRule 列表
     * @param resultList        结果列表
     * @return 整体结果
     */
    public static boolean evaluate(List<MatchContentRule> matchContentRules, List<Boolean> resultList) {
        // 参数检查
        if (matchContentRules == null || resultList == null || matchContentRules.isEmpty() || resultList.isEmpty()) {
            throw new IllegalArgumentException("Inputs cannot be null or empty");
        }

        if (matchContentRules.size() != resultList.size()) {
            throw new IllegalArgumentException("The size of matchContentRules and resultList must be the same");
        }

        // 计算结果

        return calculateFinalResult(matchContentRules, resultList);
    }

    private static boolean calculateFinalResult(List<MatchContentRule> matchContentRules, List<Boolean> resultList) {
        boolean finalResult = resultList.get(0);

        // 遍历规则并计算结果
        for (int i = 0; i < matchContentRules.size() - 1; i++) {
            MatchContentRule rule = matchContentRules.get(i);
            boolean nextResult = resultList.get(i + 1);
            // AND
            if (rule.getOperator() == 1) {
                finalResult = finalResult && nextResult;
                // OR
            } else if (rule.getOperator() == 2) {
                finalResult = finalResult || nextResult;
            } else {
                throw new IllegalArgumentException("Invalid operator in rule at index: " + i);
            }
        }
        return finalResult;
    }

}
