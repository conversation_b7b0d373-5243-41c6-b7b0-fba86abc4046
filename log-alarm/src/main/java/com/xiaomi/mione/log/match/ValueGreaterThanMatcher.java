package com.xiaomi.mione.log.match;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 大于匹配器
 * @date 2024/12/19 16:32
 */
public class ValueGreaterThanMatcher implements RuleMatcher {
    @Override
    public boolean isMatch(Map<String, Object> dataMap, String targetKey, String expectedValue) {
        // 检查 dataMap 中是否包含 targetKey
        if (!dataMap.containsKey(targetKey)) {
            return false;
        }

        // 获取 dataMap 中 targetKey 对应的值
        Object actualValue = dataMap.get(targetKey);

        // 检查 actualValue 是否为 Comparable 类型
        if (actualValue instanceof Comparable) {
            try {
                // 将 expectedValue 转换为 actualValue 的类型
                Comparable<?> expectedComparable = (Comparable<?>) actualValue.getClass().getMethod("valueOf", String.class).invoke(null, expectedValue);

                // 比较 actualValue 和 expectedValue
                return ((Comparable) actualValue).compareTo(expectedComparable) > 0;
            } catch (Exception e) {
                // 如果转换或比较过程中出现异常，返回 false
                return false;
            }
        }

        // 如果 actualValue 不是 Comparable 类型，返回 false
        return false;
    }
}
