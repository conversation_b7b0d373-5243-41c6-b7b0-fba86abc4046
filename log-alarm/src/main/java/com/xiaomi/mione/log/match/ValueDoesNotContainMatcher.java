package com.xiaomi.mione.log.match;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 是否包含
 * @date 2024/12/19 16:42
 */
public class ValueDoesNotContainMatcher implements RuleMatcher {
    @Override
    public boolean isMatch(Map<String, Object> dataMap, String targetKey, String expectedValue) {
        // 检查 dataMap 中是否包含 targetKey
        if (!dataMap.containsKey(targetKey)) {
            return true;
        }

        // 获取 dataMap 中 targetKey 对应的值
        Object actualValue = dataMap.get(targetKey);

        // 如果 actualValue 是字符串类型
        if (actualValue instanceof String) {
            return !((String) actualValue).contains(expectedValue);
        }

        // 如果 actualValue 是其他类型，尝试将其转换为字符串并检查是否包含 expectedValue
        return !actualValue.toString().contains(expectedValue);
    }
}
