package com.xiaomi.mione.log.match;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/12/19 16:56
 */
public class ValueDoesNotEndWithMatcher implements RuleMatcher {
    @Override
    public boolean isMatch(Map<String, Object> dataMap, String targetKey, String expectedValue) {
        // 检查 dataMap 中是否包含 targetKey
        if (!dataMap.containsKey(targetKey)) {
            return true;
        }

        // 获取 dataMap 中 targetKey 对应的值
        Object actualValue = dataMap.get(targetKey);

        // 如果 actualValue 是字符串类型
        if (actualValue instanceof String) {
            return !((String) actualValue).endsWith(expectedValue);
        }

        // 如果 actualValue 是其他类型，尝试将其转换为字符串并检查是否不以 expectedValue 结尾
        return !actualValue.toString().endsWith(expectedValue);
    }
}
